{"deployment": [{"_id": {"$oid": "60d9b623b07e15e833eae4a5"}, "name": "Hemophilia test", "iconFileId": "60357d1ee0524ba963a83ebe", "status": "DRAFT", "createDateTime": {"$date": "2021-06-28T11:44:35.252Z"}, "moduleConfigs": [{"id": {"$oid": "674ed473050e7626aade3ab5"}, "moduleId": "HemophiliaJournal", "status": "ENABLED", "configBody": {}, "about": ""}, {"id": {"$oid": "6a3e0a3765eed40b52746da7"}, "moduleId": "MedicationsV2", "status": "ENABLED", "configBody": {}, "about": ""}], "builder": {"tabs": [{"id": {"$oid": "60d9b623b07e15e833eae4a6"}, "order": 1, "name": "Home", "icon": "HOME", "status": "ENABLED", "background": {"value": "#ffffff", "type": "COLOR"}, "widgets": [{"order": 1, "enabled": true, "config": {"description": "This is a test description", "modules": [{"id": "674ed473050e7626aade3ab5", "moduleId": "HemophiliaJournal"}, {"id": "6a3e0a3765eed40b52746da7", "moduleId": "MedicationsV2"}], "form": {"q1": 1, "q2": 2}}, "type": "com.pfizer.widget.hemophilia_journal", "id": {"$oid": "668d3827538192c6fe227592"}}]}]}}], "huma_auth_user": [{"_id": {"$oid": "60d9b5b9b07e15e833eae4a2"}, "status": 1, "emailVerified": true, "email": "<EMAIL>"}, {"_id": {"$oid": "60d9b676b07e15e833eae4b5"}, "status": 1, "emailVerified": true, "email": "<EMAIL>"}], "user": [{"_id": {"$oid": "60d9b5b9b07e15e833eae4a2"}, "email": "<EMAIL>", "roles": [{"roleId": "SuperAdmin", "resource": "deployment/*", "userType": "SuperAdmin", "isActive": true}]}, {"_id": {"$oid": "60d9b676b07e15e833eae4b5"}, "email": "<EMAIL>", "givenName": "<PERSON>", "roles": [{"roleId": "User", "resource": "deployment/60d9b623b07e15e833eae4a5", "userType": "User", "isActive": true}], "timezone": "Europe/London", "finishedOnboarding": true}]}