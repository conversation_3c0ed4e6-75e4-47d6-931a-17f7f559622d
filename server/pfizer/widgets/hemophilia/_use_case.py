import typing as t
from collections import Counter
from dataclasses import dataclass
from datetime import datetime
from functools import cached_property

import i18n
import pytz
from aenum import Enum, auto
from dateutil.relativedelta import relativedelta

from pfizer.components.hemophilia.dtos import HemophiliaJournalDTO
from pfizer.components.hemophilia.dtos.enums import BodyLocation
from pfizer.components.hemophilia.modules import HemophiliaJournalModule
from pfizer.components.hemophilia.service import HemophiliaService
from sdk.authorization.dtos.role.role import RoleDTO
from sdk.builder.models import GetWidgetDataRequestObject
from sdk.common.common_models.sort import SortField
from sdk.common.usecase.response_object import ResponseObject
from sdk.common.usecase.use_case import UseCase
from sdk.common.utils.convertible import default_field, required_field
from sdk.common.utils.huconvertible import HuConvertible
from sdk.module_result.service.module_result_service import ModuleResultService
from ._hemophilia_journal import HemophiliaJournalUIWidget

LIGHT_GREY = "#EBEBEB"
GREY = "#909499"
AMBER = "#FA8333"
RED = "#EB0037"


@dataclass
class LegendItem(HuConvertible):
    color: str = required_field()
    label: str = required_field()


@dataclass
class BodyMapItem(HuConvertible):
    location: str = required_field()
    color: str = required_field()


class WidgetState(Enum):
    SETUP_REQUIRED: t.Self = auto()
    ACTIVE: t.Self = auto()


@dataclass
class HemophiliaResponseObject(ResponseObject, HuConvertible):
    title: str = required_field()
    description: str = default_field()
    primaryCTAtext: str = default_field()
    secondaryCTAtext: str = default_field()
    state: WidgetState = required_field()
    legend: list[LegendItem] = default_field()
    bodyMapColor: list[BodyMapItem] = required_field()
    tooltip: str = default_field()


class HemophiliaJournalUseCase(UseCase):
    """
    This use case is responsible for visualizing the hemophilia journal widget and the body map.
    Body map designs:
    https://www.figma.com/design/ywUoFSD1KBMG7sqakfUSEd/Hemophilia-Pfizer?node-id=7601-64358&t=Cwye0irdEnSpSTZu-0
    """

    request_object: GetWidgetDataRequestObject
    response_schema = HemophiliaResponseObject.Schema

    def process_request(self, request_object: GetWidgetDataRequestObject) -> HemophiliaResponseObject:
        language = request_object.authzUser.get_language()
        if not self._prerequisites_are_completed():
            return HemophiliaResponseObject(
                state=WidgetState.SETUP_REQUIRED,
                title=i18n.t("Hemophilia.Widget.prerequisites.title", locale=language),
                description=i18n.t("Hemophilia.Widget.prerequisites.desc", locale=language),
                primaryCTAtext=i18n.t("Hemophilia.Widget.prerequisites.CTA", locale=language),
                bodyMapColor=self._build_body_map_coloring([], color=LIGHT_GREY),
            )

        results = self._retrieve_journal_records()
        _, injury_count = self._get_body_location_injury_count(results).most_common(1)[0]
        tooltip_text = i18n.t("Hemophilia.Widget.body.tooltip", locale=language) if injury_count else None
        if not results:
            return HemophiliaResponseObject(
                state=WidgetState.ACTIVE,
                title=i18n.t("Hemophilia.Widget.empty.title", locale=language),
                description=i18n.t("Hemophilia.Widget.empty.desc", locale=language),
                primaryCTAtext=i18n.t("Hemophilia.Widget.CTA", locale=language),
                secondaryCTAtext=i18n.t("Hemophilia.Widget.secondaryCTA", locale=language),
                bodyMapColor=self._build_body_map_coloring(results),
                tooltip=tooltip_text,
            )

        return HemophiliaResponseObject(
            state=WidgetState.ACTIVE,
            primaryCTAtext=i18n.t("Hemophilia.Widget.CTA", locale=language),
            secondaryCTAtext=i18n.t("Hemophilia.Widget.secondaryCTA", locale=language),
            legend=self.__legend,
            bodyMapColor=self._build_body_map_coloring(results),
            tooltip=tooltip_text,
        )

    def _prerequisites_are_completed(self) -> bool:
        return bool(HemophiliaService().retrieve_prerequisites(self.request_object.authzUser.id))

    def _retrieve_journal_records(self) -> list[HemophiliaJournalDTO] | None:
        now = datetime.now(pytz.UTC)
        results = ModuleResultService().retrieve_module_results(
            user_id=self.request_object.authzUser.id,
            module_id=HemophiliaJournalModule.moduleId,
            skip=0,
            limit=0,
            direction=SortField.Direction.ASC,
            from_date_time=now - relativedelta(months=6),
            to_date_time=now,
            filters=None,
            deployment_id=self.request_object.deployment.id,
            role=RoleDTO.UserType.USER,
        )
        return results.get(HemophiliaJournalDTO.get_primitive_name())

    def _build_body_map_coloring(self, results: list[HemophiliaJournalDTO], color: str = None) -> list[BodyMapItem]:
        """
        Color the body map according to the number of results for each body location.
        Check __get_color for the color thresholds.
        If the body location was selected as a target joint in the latest prerequisites answer, it is colored in
        red regardless of the count.
        """

        body_location_injury_count = self._get_body_location_injury_count(results)
        return [
            BodyMapItem(location=key.name, color=color or self.__get_color(count))
            for key, count in body_location_injury_count.items()
        ]

    def _get_body_location_injury_count(self, results: list[HemophiliaJournalDTO]) -> Counter:
        count_by_body_location = Counter([item.bodyLocation for item in results])

        for body_location in BodyLocation:  # noqa
            count_by_body_location.setdefault(body_location, 0)
            if body_location in self._target_joints:
                count_by_body_location[body_location] += 3

        return count_by_body_location

    @cached_property
    def _target_joints(self) -> list[BodyLocation]:
        """
        Ignore values older than 6 months.
        Design https://www.figma.com/design/ywUoFSD1KBMG7sqakfUSEd/Hemophilia-Pfizer?node-id=7601-64446&t=Sc00UF3Rig9U4Zbi-4
        """
        result = HemophiliaService().retrieve_prerequisites(self.request_object.authzUser.id)
        if not result or result.createDateTime < datetime.utcnow() - relativedelta(months=6):
            return []

        return result.targetJoints or []

    @cached_property
    def _widget_config(self):
        return HemophiliaJournalUIWidget.from_dict(
            self.request_object.widget.to_dict(include_none=False),
            use_validator_field=False,
        ).config

    @property
    def __legend(self) -> list[LegendItem]:
        language = self.request_object.authzUser.get_language()
        return [
            LegendItem(
                color=GREY,
                label=i18n.t("Hemophilia.Widget.RAG.greyLabel", locale=language),
            ),
            LegendItem(
                color=AMBER,
                label=i18n.t("Hemophilia.Widget.RAG.amberLabel", locale=language),
            ),
            LegendItem(
                color=RED,
                label=i18n.t("Hemophilia.Widget.RAG.redLabel", locale=language),
            ),
        ]

    @staticmethod
    def __get_color(count: int):
        return GREY if count == 0 else AMBER if 0 < count < 3 else RED
