from pfizer.components.hemophilia.component import HemophiliaComponent
from sdk.builder.widget import AppWidget
from sdk.common.usecase.use_case import UseCase
from sdk.deployment.dtos.builder import UIWidget
from ._hemophilia_journal import HemophiliaJournalUIWidget
from ._use_case import HemophiliaJournalUseCase


class HemophiliaJournalWidget(AppWidget):
    id = "com.pfizer.widget.hemophilia_journal"
    title = "Hemophilia journal"
    description = "A widget for tracking bleeds and pain events."

    dependencies = [HemophiliaComponent]

    @property
    def config_class(self) -> type[UIWidget]:
        return HemophiliaJournalUIWidget

    @property
    def use_case(self) -> UseCase:
        return HemophiliaJournalUseCase()
