from datetime import datetime
from functools import cached_property

from sdk import convertibleclass
from sdk.builder.models import GetWidgetDataRequestObject
from sdk.common.exceptions.exceptions import InvalidRequestException
from sdk.common.usecase.response_object import ResponseObject
from sdk.common.usecase.use_case import UseCase
from sdk.common.utils import inject
from sdk.common.utils.convertible import default_field, meta, required_field
from servier.components.five_lives.dtos import GameId, GameProgressDTO, GameRank
from servier.components.five_lives.repository import GameRepository
from servier.widgets.five_lives._five_lives import FiveLivesUIWidget


@convertibleclass
class ScoreItem:
    value: int = required_field()
    date: int = required_field()


@convertibleclass
class ScoreTable:
    title: str = required_field()
    data: list[ScoreItem] = required_field()


@convertibleclass
class FiveLivesWidgetResponse(ResponseObject):
    title: str = required_field()
    description: str = required_field()
    about: str = required_field()
    rank: GameRank = required_field(metadata=meta(by_value=True))
    scoreTables: list[ScoreTable] = default_field()
    gameId: GameId = required_field(metadata=meta(by_value=True))
    buttonText: str = required_field()


class FiveLivesWidgetUseCase(UseCase):
    request_object: GetWidgetDataRequestObject
    response_schema = FiveLivesWidgetResponse.Schema

    def process_request(self, request_object: GetWidgetDataRequestObject):
        game_id = self._widget_config.gameId
        return self.build_response(game_id)

    def build_response(self, game_id: GameId) -> FiveLivesWidgetResponse:
        game_progress = self._get_game_progress(game_id)

        game_progress.scoreHistory.sort(key=lambda x: x.value, reverse=True)
        best_5_scores = game_progress.scoreHistory[:5]

        game_progress.scoreHistory.sort(key=lambda x: x.timestamp, reverse=True)
        last_5_scores = game_progress.scoreHistory[:5]

        best_scores_data = [
            ScoreItem(value=item.value, date=datetime.fromtimestamp(item.timestamp).strftime("%B %d, %Y"))
            for item in best_5_scores
        ]
        best_scores_table = ScoreTable(title="Best scores", data=best_scores_data)
        last_scores_data = [
            ScoreItem(value=item.value, date=datetime.fromtimestamp(item.timestamp).strftime("%B %d, %Y"))
            for item in last_5_scores
        ]
        last_scores_table = ScoreTable(title="Last scores", data=last_scores_data)

        # Generate about a text based on the current rank
        about = self._generate_about_text(game_progress.rank.value, game_id)

        return FiveLivesWidgetResponse(
            title=self._widget_config.card.title,
            description=self._widget_config.card.description,
            about=about,
            rank=game_progress.rank.value,
            scoreTables=[best_scores_table, last_scores_table],
            gameId=game_id,
            buttonText="Play now",
        )

    def _get_game_progress(self, game_id: GameId) -> GameProgressDTO | None:
        repo = inject.instance(GameRepository)
        user_id = self.request_object.authzUser.id
        games_progress = repo.retrieve_progress(user_id)

        # Find the progress for the specific game
        for progress in games_progress:
            if progress.gameId == game_id:
                return progress

        raise InvalidRequestException(f"Game progress not found #{game_id}")

    @staticmethod
    def _generate_about_text(current_rank: int, game_id: GameId) -> str:
        """Generate the about text based on the current rank and game ID."""
        # Define rank names
        rank_names = {
            0: "Beginner",
            1: "Elementary",
            2: "Novice",
            3: "Intermediate",
            4: "Advanced",
            5: "Expert",
            6: "Master",
            7: "Grand Master",
        }

        game_thresholds = {
            GameId.SPOT_ME: {
                0: 700,  # Beginner to Elementary
                1: 1400,  # Elementary to Novice
                2: 1800,  # Novice to Intermediate
                3: 2000,  # Intermediate to Advanced
                4: 2200,  # Advanced to Expert
                5: 2600,  # Expert to Master
                6: 3000,  # Master to Grand Master
            },
            GameId.BACK_BACK: {
                0: 300,  # Beginner to Elementary
                1: 2400,  # Elementary to Novice
                2: 3800,  # Novice to Intermediate
                3: 2400,  # Intermediate to Advanced
                4: 8000,  # Advanced to Expert
                5: 12000,  # Expert to Master
                6: 10000,  # Master to Grand Master
            },
            GameId.BUBBLE_POP: {
                0: 300,  # Beginner to Elementary
                1: 500,  # Elementary to Novice
                2: 800,  # Novice to Intermediate
                3: 1800,  # Intermediate to Advanced
                4: 2000,  # Advanced to Expert
                5: 3000,  # Expert to Master
                6: 4000,  # Master to Grand Master
            },
            GameId.THINK_TWICE: {
                0: 500,  # Beginner to Elementary
                1: 1200,  # Elementary to Novice
                2: 1800,  # Novice to Intermediate
                3: 2000,  # Intermediate to Advanced
                4: 2200,  # Advanced to Expert
                5: 2600,  # Expert to Master
                6: 3500,  # Master to Grand Master
            },
            GameId.CHEERY: {
                0: 300,  # Beginner to Elementary
                1: 500,  # Elementary to Novice
                2: 1000,  # Novice to Intermediate
                3: 1500,  # Intermediate to Advanced
                4: 2000,  # Advanced to Expert
                5: 2500,  # Expert to Master
                6: 5000,  # Master to Grand Master
            },
        }

        # Get the appropriate thresholds for the game
        score_thresholds = game_thresholds.get(game_id)

        # Ensure min/max values are handled correctly
        for rank, threshold in score_thresholds.items():
            if threshold < 0:
                score_thresholds[rank] = 0
            elif threshold > 7000:
                score_thresholds[rank] = 7000

        # Get current rank name
        current_rank_name = rank_names.get(current_rank, "Unknown")

        # If user is at the maximum rank (Grand Master)
        if current_rank == 7:
            return f"Awesome! You've reached the maximum level! You're a {current_rank_name}, congrats!"

        # Get next rank name and score threshold
        next_rank = current_rank + 1
        next_rank_name = rank_names.get(next_rank, "Unknown")
        score_threshold = score_thresholds.get(current_rank, 0)

        # If user is at the first rank (Beginner)
        if current_rank == 0:
            return f"You're currently at {current_rank_name} rank. Score above {score_threshold} to rank up to {next_rank_name}."

        # For all other ranks
        return f"You're currently at {current_rank_name} rank. Score above {score_threshold} twice in a row to rank up to {next_rank_name}."

    @cached_property
    def _widget_config(self):
        return FiveLivesUIWidget.from_dict(
            self.request_object.widget.to_dict(include_none=False),
            use_validator_field=False,
        ).config
