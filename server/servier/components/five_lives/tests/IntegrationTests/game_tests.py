from pathlib import Path

from flask import url_for

from huma_plugins.tests.plugin_test_case import PluginsTestCase
from sdk.auth.component import AuthComponent
from sdk.authorization.component import AuthorizationComponent
from sdk.deployment.component import DeploymentComponent
from sdk.versioning.component import VersionComponent
from servier.components.five_lives.component import FiveLivesComponent
from servier.components.five_lives.dtos import GameId

USER = "60d9b676b07e15e833eae4b5"
DEPLOYMENT_ID = "60d9b623b07e15e833eae4a5"


class FiveLivesGameTestCase(PluginsTestCase):
    components = [
        AuthComponent(),
        AuthorizationComponent(),
        DeploymentComponent(),
        FiveLivesComponent(),
        VersionComponent(server_version="1.0.0", api_version="v1"),
    ]
    fixtures = [
        Path(__file__).parent / "fixtures" / "game_dump.json",
    ]

    def test_retrieve_game_progress(self):
        """
        Test the first game progress.
        """
        game_progress = self._retrieve_game_progress()

        self.assertEqual(USER, game_progress["userId"])
        self.assertEqual(5, len(game_progress["scoreHistory"]))
        self.assertEqual(5, len(game_progress["rank"]))
        self.assertEqual(13, len(game_progress["instructionsShownHistory"]))

    def test_update_game_progress(self):
        """
        Test the first game progress.
        """
        game_progress = self._retrieve_game_progress()
        data = {
            "gameId": GameId.SPOT_ME,
            "score": 1200,
            "previousRank": 1,
            "updatedRank": 2,
            "updatedHistory": [
                *(game_progress["scoreHistory"][GameId.SPOT_ME] or []),
                {
                    "value": 1200,
                    "timestamp": 1234567890,
                    "rank": 2,
                },
            ],
        }
        game_progress = self._update_game_progress(data)
        self.assertEqual(USER, game_progress["userId"])
        self.assertEqual(1, len(game_progress["scoreHistory"][GameId.SPOT_ME.value]))
        self.assertEqual(2, game_progress["rank"][GameId.SPOT_ME.value])

    def test_update_instructions(self):
        data = {
            "keys": [
                "avoidBubbles",
                "letterBubbles",
                "n_back3",
            ]
        }
        response = self._update_instructions(data)
        for key in response["instructionsShownHistory"]:
            if key in data["keys"]:
                self.assertTrue(response["instructionsShownHistory"][key])
            else:
                self.assertFalse(response["instructionsShownHistory"][key])

    def _retrieve_game_progress(self):
        rsp = self.flask_client.get(
            url_for("five_lives.fetch_user_data", user_id=USER), headers=self.get_headers_for_token(USER)
        )
        self.assertEqual(rsp.status_code, 200)
        return rsp.json

    def _update_game_progress(self, data: dict):
        rsp = self.flask_client.post(
            url_for("five_lives.update_history", user_id=USER), json=data, headers=self.get_headers_for_token(USER)
        )
        self.assertEqual(rsp.status_code, 200)
        return rsp.json

    def _update_instructions(self, data: dict):
        rsp = self.flask_client.post(
            url_for("five_lives.update_instructions_shown_history", user_id=USER),
            json=data,
            headers=self.get_headers_for_token(USER),
        )
        self.assertEqual(rsp.status_code, 200)
        return rsp.json
