from sdk.security import Access, ProtectedBlueprint
from servier.components.five_lives.use_cases import (
    FetchUserDataRequestObject,
    FetchUserDataUseCase,
    UpdateHistoryRequestObject,
    UpdateHistoryUseCase,
    UpdateInstructionsShownHistoryRequestObject,
    UpdateInstructionsShownHistoryUseCase,
    UserDataResponse,
)

api = ProtectedBlueprint("five_lives", __name__, url_prefix="/api/plugins/v1/five_lives")


@api.get("/user/<user_id>", requires=Access.SELF.VIEW_DATA)
@api.input(FetchUserDataRequestObject.Schema, location="query")
@api.output(UserDataResponse.Schema)
def fetch_user_data(user_id: str, query_data: FetchUserDataRequestObject):
    """
    Fetch User Data
    Returns the user data including score history, rank, and instructions shown history.
    """
    return FetchUserDataUseCase().execute(query_data)


@api.post("/user/<user_id>/history", requires=Access.SELF.EDIT_DATA)
@api.input(UpdateHistoryRequestObject.Schema)
@api.output(UserDataResponse.Schema)
def update_history(user_id: str, json_data: UpdateHistoryRequestObject):
    """
    Update History
    Updates the score history and rank for a specific game.
    """
    return UpdateHistoryUseCase().execute(json_data)


@api.post("/user/<user_id>/instructions", requires=Access.SELF.EDIT_DATA)
@api.input(UpdateInstructionsShownHistoryRequestObject.Schema)
@api.output(UserDataResponse.Schema)
def update_instructions_shown_history(user_id: str, json_data: UpdateInstructionsShownHistoryRequestObject):
    """
    Update Instructions Shown History
    Sets the specified keys in the instructions shown history to true.
    """
    return UpdateInstructionsShownHistoryUseCase().execute(json_data)
