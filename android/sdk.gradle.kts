import org.jetbrains.kotlin.gradle.dsl.JvmTarget

plugins {
    alias(libs.plugins.android.library)
    alias(libs.plugins.detekt)
    alias(libs.plugins.kotlin)
    alias(libs.plugins.kotlin.compose.compiler)
    alias(libs.plugins.kotlin.parcelize)
    alias(libs.plugins.safeargs)
    alias(libs.plugins.dokka)
    alias(libs.plugins.test.retry)
    alias(test.plugins.kover)
    `maven-publish`
    alias(libs.plugins.ksp)
}

android {
    namespace = "com.huma.sdk.servier"

    compileSdk = app.versions.sdk.compile.get().toInt()

    defaultConfig {
        minSdk = app.versions.sdk.minimum.get().toInt()

        testInstrumentationRunner = "androidx.test.runner.AndroidJUnitRunner"
        consumerProguardFiles("consumer-rules.pro")
        vectorDrawables.useSupportLibrary = true
    }

    buildTypes {
        flavourConfig {
            isMinifyEnabled =
                findProperty("minify")?.toBoolean() ?: app.versions.minify.release.get().toBoolean()
            proguardFiles(
                getDefaultProguardFile("proguard-android-optimize.txt"), "proguard-rules.pro"
            )
        }
    }
    compileOptions {
        sourceCompatibility = JavaVersion.toVersion(app.versions.jdk.get())
        targetCompatibility = JavaVersion.toVersion(app.versions.jdk.get())
    }

    buildFeatures {
        viewBinding = true
    }

    publishing {
        singleVariant("release") {
            withSourcesJar()
        }
    }
}

kotlin {
    compilerOptions {
        jvmTarget.set(JvmTarget.fromTarget(app.versions.jdk.get()))
        freeCompilerArgs = compilerArgs
    }
}

fun findProperty(key: String): String? {
    if (project.hasProperty(key)) {
        return project.property(key) as String
    }
    return null
}

afterEvaluate {
    publishing {
        repositories {
            mavenLocal {
                name = "LibraryToMavenLocal"
            }
            maven {
                name = "GitHubPackages"
                url = uri("${rootProject.layout.buildDirectory.asFile.get()}/maven")
            }
        }
        publications {
            publishConfig(
                components = components,
                artifactId = "plugin-servier",
                version = huma.versions.huma.plugin.servier.get(),
                dependencyScope = DependencyScope.Project(project)
            )
        }
    }
}

tasks.withType(Test::class) {
    testLogging {
        exceptionFormat = org.gradle.api.tasks.testing.logging.TestExceptionFormat.FULL
        showStackTraces = true
        showStandardStreams = true
    }
    retry {
        maxFailures.set(10)
        maxRetries.set(3)
        failOnPassedAfterRetry.set(true)
    }
}

tasks {
    dokkaHtmlPartial.configure {
        dokkaSourceSets {
            named("main") {
                noAndroidSdkLink.set(false)
                includeNonPublic.set(false)
                suppressInheritedMembers.set(true)
                suppressObviousFunctions.set(true)
            }
        }
    }
}

dependencies {
    implementation(libs.core.ktx)
    implementation(libs.appcompat)
    implementation(libs.ui.swiperefreshlayout)

    implementation(libs.bundles.kotlin)
    runtimeOnly(libs.kotlin.reflect)
    implementation(libs.koin)
    ksp(libs.moshi.codegen)

    implementation(libs.ui.constraint)
    implementation(libs.ui.recycler)
    implementation(libs.navigation.ui)
    implementation(libs.navigation.fragment)

    dokkaPlugin(libs.dokka)

    // Foundation
    flavourImplementation(
        project = project,
        local = project(":foundation:resources"),
        remote = huma.foundation.resources
    )
    flavourImplementation(
        project = project,
        local = project(":foundation:core"),
        remote = huma.foundation.core
    )
    flavourImplementation(
        project = project,
        local = project(":foundation:charts"),
        remote = huma.foundation.charts
    )
    flavourImplementation(
        project = project,
        local = project(":foundation:ui"),
        remote = huma.foundation.ui
    )

    // SDK
    flavourImplementation(
        project = project,
        local = project(":sdk:core"),
        remote = huma.sdk.core
    )
    flavourImplementation(
        project = project,
        local = project(":sdk:caching"),
        remote = huma.sdk.caching
    )
    flavourImplementation(
        project = project,
        local = project(":sdk:module-kit"),
        remote = huma.sdk.module.kit
    )
    flavourImplementation(
        project = project,
        local = project(":sdk:questionnaire"),
        remote = huma.sdk.questionnaire
    )

    flavourImplementation(
        project = project,
        local = project(":sdk:widget-kit"),
        remote = huma.sdk.widget.kit
    )
    flavourImplementation(
        project = project,
        local = project(":sdk:object-storage-kit"),
        remote = huma.sdk.storage.kit
    )

    // AppKit
    flavourImplementation(
        project = project,
        local = project(":appkit:modules"),
        remote = huma.appkit.modules
    )

    testImplementation(project(":test:huma-testing-utils"))

    testImplementation(libs.threeten.threetenbp) {
        exclude("com.jakewharton.threetenabp", "threetenabp")
    }
    testImplementation(test.bundles.ext)
    testImplementation(test.bundles.mockito)
    testImplementation(test.bundles.mockk)
    testImplementation(project(":test:huma-testing-utils"))
    testImplementation(project(":test:huma-testing-offline-helper"))
}
