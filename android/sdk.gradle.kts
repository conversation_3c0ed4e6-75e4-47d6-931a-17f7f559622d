import org.jetbrains.kotlin.gradle.dsl.JvmTarget

plugins {
    alias(libs.plugins.android.library)
    alias(libs.plugins.kotlin)
    alias(libs.plugins.kotlin.compose.compiler)
    alias(libs.plugins.dokka)
    alias(libs.plugins.kotlin.parcelize)
    alias(libs.plugins.safeargs)
    alias(libs.plugins.kotlin.serial)
    alias(libs.plugins.test.retry)
    alias(libs.plugins.ksp)
    `maven-publish`
    alias(test.plugins.kover)
}

android {
    namespace = "com.huma.sdk.pfizer"
    compileSdk = app.versions.sdk.compile.get().toInt()

    defaultConfig {
        minSdk = app.versions.sdk.minimum.get().toInt()

        testInstrumentationRunner = "androidx.test.runner.AndroidJUnitRunner"
        consumerProguardFiles("consumer-rules.pro")
    }

    buildTypes {
        flavourConfig {
            isMinifyEnabled =
                findProperty("minify")?.toBoolean() ?: app.versions.minify.release.get().toBoolean()
            proguardFiles(
                getDefaultProguardFile("proguard-android-optimize.txt"),
                "proguard-rules.pro"
            )
        }
    }

    compileOptions {
        sourceCompatibility = JavaVersion.toVersion(app.versions.jdk.get())
        targetCompatibility = JavaVersion.toVersion(app.versions.jdk.get())
    }

    buildFeatures {
        viewBinding = true
        compose = true
    }

    publishing {
        singleVariant("release") {
            withSourcesJar()
        }
    }
}

kotlin {
    compilerOptions {
        jvmTarget.set(JvmTarget.fromTarget(app.versions.jdk.get()))
        freeCompilerArgs = compilerArgs
    }
}

afterEvaluate {
    publishing {
        repositories {
            mavenLocal {
                name = "LibraryToMavenLocal"
            }
            maven {
                name = "GitHubPackages"
                url = uri("${rootProject.layout.buildDirectory.asFile.get()}/maven")
            }
        }
        publications {
            publishConfig(
                components = components,
                artifactId = "plugin-pfizer",
                version = huma.versions.huma.plugin.pfizer.get(),
                dependencyScope = DependencyScope.Project(project)
            )
        }
    }
}

fun findProperty(key: String): String? {
    if (project.hasProperty(key)) {
        return project.property(key) as String
    }
    return null
}

dependencies {
    implementation(libs.core.ktx)
    implementation(libs.bundles.kotlin)
    implementation(libs.appcompat)
    implementation(libs.ui.constraint)
    implementation(libs.ui.swiperefreshlayout)
    implementation(libs.koin)
    implementation(libs.coil.compose)
    implementation(libs.navigation.fragment)
    implementation(libs.navigation.ui)
    ksp(libs.moshi.codegen)

    implementation(libs.camera.core)
    implementation(libs.camera.lifecycle)
    implementation(libs.camera.video)
    implementation(libs.camera.view)

    implementation(libs.bundles.vico)

    implementation(libs.firebase.messaging)

    implementation(libs.controlledconfig)

    flavourImplementation(
        project = project,
        local = project(":foundation:resources"),
        remote = huma.foundation.resources
    )

    flavourImplementation(
        project = project,
        local = project(":foundation:charts"),
        remote = huma.foundation.charts
    )

    flavourImplementation(
        project = project,
        local = project(":foundation:permissions"),
        remote = huma.foundation.permissions
    )

    flavourImplementation(
        project = project,
        local = project(":foundation:core"),
        remote = huma.foundation.core
    )
    flavourImplementation(
        project = project,
        local = project(":foundation:ui"),
        remote = huma.foundation.ui
    )
    flavourImplementation(
        project = project,
        local = project(":sdk:core"),
        remote = huma.sdk.core
    )
    flavourImplementation(
        project = project,
        local = project(":sdk:caching"),
        remote = huma.sdk.caching
    )
    flavourImplementation(
        project = project,
        local = project(":sdk:widget-kit"),
        remote = huma.sdk.widget.kit
    )
    flavourImplementation(
        project = project,
        local = project(":sdk:module-kit"),
        remote = huma.sdk.module.kit
    )
    flavourImplementation(
        project = project,
        local = project(":sdk:actions-kit"),
        remote = huma.sdk.actions.kit
    )
    flavourImplementation(
        project = project,
        local = project(":sdk:object-storage-kit"),
        remote = huma.sdk.storage.kit
    )
    flavourImplementation(
        project = project,
        local = project(":sdk:questionnaire"),
        remote = huma.sdk.questionnaire
    )

    flavourImplementation(
        project = project,
        local = project(":appkit:modules"),
        remote = huma.appkit.modules
    )

    flavourImplementation(
        project = project,
        local = project(":plugins:widgets:header"),
        remote = huma.plugin.widget.header,
    )

    testImplementation(libs.threeten.threetenbp) {
        exclude("com.jakewharton.threetenabp", "threetenabp")
    }
    testImplementation(test.bundles.ext)
    testImplementation(test.bundles.mockito)
    testImplementation(test.bundles.mockk)
    testImplementation(project(":test:huma-testing-utils"))
    testImplementation(project(":test:huma-testing-offline-helper"))
}
