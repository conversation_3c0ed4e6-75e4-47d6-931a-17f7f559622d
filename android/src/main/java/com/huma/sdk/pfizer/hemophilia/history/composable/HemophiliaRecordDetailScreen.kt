package com.huma.sdk.pfizer.hemophilia.history.composable

import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.statusBarsPadding
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.rememberLazyListState
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.unit.dp
import com.huma.sdk.pfizer.R
import com.huma.sdk.pfizer.hemophilia.model.HemophiliaBodyLocation
import com.huma.sdk.pfizer.hemophilia.module.HemophiliaModuleResult
import com.huma.sdk.ui.components.composable.base_text.BaseText
import com.huma.sdk.ui.components.composable.toolbar.TopNavBar
import com.huma.sdk.ui.humastyle.HumaTypeStyler
import com.huma.sdk.ui.utils.ext.throttledClickable

@Composable
fun HemophiliaRecordDetailScreen(
    record: HemophiliaModuleResult,
    bodyInjuryName: String,
    onOpenBodyLocationClick: (HemophiliaBodyLocation) -> Unit,
    onOpenPhotoClick: (String) -> Unit,
    onBackClick: () -> Unit,
) {
    val scrollState = rememberLazyListState()
    LazyColumn(
        state = scrollState,
        modifier = Modifier
            .fillMaxSize()
            .statusBarsPadding()
    ) {
        TopNavBar(
            title = bodyInjuryName,
            showBackButton = true,
            onBackClick = onBackClick,
            scrollState = scrollState,
        )

        item {
            Spacer(Modifier.height(24.dp))
        }

        item {
            HemophiliaDetailDataItem(
                name = stringResource(R.string.plugin_hemophilia_date_of_bleed),
                value = record.dateFormatted2,
            )
        }

        if (record.treatment != null) {
            item {
                HemophiliaDetailDataItem(
                    name = stringResource(R.string.plugin_hemophilia_treatment),
                    value = record.treatment,
                )
            }
        }

        item {
            HemophiliaDetailDataItem(
                name = stringResource(R.string.plugin_hemophilia_reason_for_the_bleed),
                value = record.reason?.titleRes?.let { stringResource(it) }.orEmpty(),
            )
        }

        if (record.severity != null) {
            item {
                HemophiliaDetailDataItem(
                    name = stringResource(R.string.plugin_hemophilia_severity_of_the_bleed),
                    value = record.severity?.titleRes?.let { stringResource(it) }.orEmpty(),
                )
            }
        }

        if (record.scale != null) {
            item {
                HemophiliaDetailDataItem(
                    name = stringResource(R.string.plugin_hemophilia_pain_scale_of_the_bleed),
                    value = record.scale.toString(),
                )
            }
        }

        item {
            HemophiliaDetailDataItem(
                name = stringResource(R.string.plugin_hemophilia_note),
                value = record.note.orEmpty(),
            )
        }

        item {
            Column(
                modifier = Modifier.padding(horizontal = 24.dp, vertical = 12.dp)
            ) {
                BaseText(
                    text = stringResource(R.string.common_details),
                    style = HumaTypeStyler.headline,
                )
            }
        }

        if (record.bodyLocation != null) {
            item {
                HemophiliaDetailOptionItem(
                    name = stringResource(R.string.plugin_hemophilia_body_location),
                    description = bodyInjuryName,
                    modifier = Modifier
                        .throttledClickable {
                            onOpenBodyLocationClick(record.bodyLocation)
                        }
                )
            }
        }
        if (record.photo != null) {
            item {
                HemophiliaDetailOptionItem(
                    name = stringResource(R.string.file_question_type_photo),
                    description = "1 ${stringResource(R.string.common_unit_photo_singular)}",
                    modifier = Modifier
                        .throttledClickable {
                            onOpenPhotoClick(record.photo)
                        }
                )
            }
        }

        item {
            Spacer(Modifier.height(24.dp))
        }
    }
}
