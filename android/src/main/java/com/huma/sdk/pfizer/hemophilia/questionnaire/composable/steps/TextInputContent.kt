package com.huma.sdk.pfizer.hemophilia.questionnaire.composable.steps

import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.text.KeyboardActions
import androidx.compose.foundation.text.KeyboardOptions
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Modifier
import androidx.compose.ui.platform.LocalFocusManager
import androidx.compose.ui.text.input.ImeAction
import androidx.compose.ui.unit.dp
import com.huma.sdk.ui.components.composable.input.InputText
import com.huma.sdk.ui.components.composable.input.InputTextStyle

@Composable
fun TextInputContent(
    isSubmitting: <PERSON><PERSON><PERSON>,
    placeholder: String,
    isLongText: <PERSON><PERSON><PERSON>,
    answersList: List<String>,
    onAnswersUpdated: (List<String>) -> Unit,
) {
    var input by remember(answersList) {
        mutableStateOf(answersList.firstOrNull().orEmpty())
    }
    Column(
        modifier = Modifier
            .fillMaxWidth()
    ) {
        Spacer(Modifier.height(16.dp))
        val focusManager = LocalFocusManager.current
        InputText(
            value = input,
            style = InputTextStyle.OUTLINE,
            isInputEnabled = !isSubmitting,
            placeholder = placeholder.ifBlank { "Enter value" },
            singleLine = !isLongText,
            minLines = if (isLongText) 5 else 1,
            onValueChange = {
                input = it
            },
            keyboardOptions = KeyboardOptions(imeAction = ImeAction.Done),
            keyboardActions = KeyboardActions {
                if (input.isNotBlank()) {
                    onAnswersUpdated(listOf(input))
                } else {
                    onAnswersUpdated(listOf())
                }
                focusManager.clearFocus()
            }
        )
    }
}
