package com.huma.sdk.pfizer.hemophilia.questionnaire.composable.steps

import androidx.compose.foundation.Image
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.width
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.unit.dp
import com.huma.sdk.pfizer.hemophilia.model.HemophiliaQuestionnaire
import com.huma.sdk.questionnaire.R
import com.huma.sdk.ui.components.composable.base_text.BaseText
import com.huma.sdk.ui.components.composable.button.Button
import com.huma.sdk.ui.components.composable.button.style.DefaultButtonStyle
import com.huma.sdk.ui.humastyle.HumaTypeStyler

@Composable
fun SubmissionContent(
    isSubmitting: Boolean,
    isSummary: Boolean,
    summaryItems: List<HemophiliaQuestionnaire.SummaryItem>,
    submitText: String?,
    onSubmitClick: () -> Unit,
) {
    Column(
        modifier = Modifier
            .fillMaxWidth()
    ) {
        Spacer(Modifier.height(16.dp))
        if (isSummary) {
            Spacer(Modifier.height(16.dp))
            summaryItems.forEach {
                SubmissionSummaryItem(it)
            }
            Spacer(Modifier.height(16.dp))
        }
        Button(
            text = submitText ?: stringResource(R.string.common_action_submit),
            buttonType = DefaultButtonStyle.primary,
            isLoading = isSubmitting,
            isEnable = !isSubmitting,
            onClick = onSubmitClick,
        )
    }
}

@Composable
fun SubmissionSummaryItem(item: HemophiliaQuestionnaire.SummaryItem) {
    SubmissionSummaryItemContent(
        title = item.summaryTitle,
        answer = item.summaryText ?: stringResource(R.string.common_skipped),
    )
}

@Composable
fun SubmissionSummaryItemContent(
    title: String,
    answer: String,
) {
    Column {
        Row(
            verticalAlignment = Alignment.CenterVertically,
            modifier = Modifier
                .fillMaxWidth()
        ) {
            Image(
                painterResource(R.drawable.hsdk_ic_touchpoint_selected),
                contentDescription = null,
            )
            Spacer(Modifier.width(16.dp))
            Column {
                BaseText(
                    text = title,
                    style = HumaTypeStyler.action1,
                    maxLines = 1,
                    overflow = TextOverflow.Ellipsis,
                )
                Spacer(Modifier.height(8.dp))
                BaseText(
                    text = answer,
                    style = HumaTypeStyler.headline,
                    maxLines = 2,
                    overflow = TextOverflow.Ellipsis,
                )
            }
        }
        Spacer(Modifier.height(22.dp))
    }
}
