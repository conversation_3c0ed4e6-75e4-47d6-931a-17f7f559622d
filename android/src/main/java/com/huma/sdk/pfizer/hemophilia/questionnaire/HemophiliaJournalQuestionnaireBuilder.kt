package com.huma.sdk.pfizer.hemophilia.questionnaire

import android.content.Context
import androidx.annotation.Keep
import androidx.annotation.StringRes
import com.huma.sdk.core.utils.ext.orNow
import com.huma.sdk.core.utils.ext.toLocalDate
import com.huma.sdk.pfizer.R
import com.huma.sdk.pfizer.hemophilia.model.HemophiliaInjuryReason
import com.huma.sdk.pfizer.hemophilia.model.HemophiliaInjurySeverity
import com.huma.sdk.questionnaire.core.data.remote.autocomplete.AutoCompleteString
import com.huma.sdk.questionnaire.core.domain.entities.Autocomplete
import com.huma.sdk.questionnaire.core.domain.entities.Form
import com.huma.sdk.questionnaire.core.domain.entities.InputType
import com.huma.sdk.questionnaire.core.domain.entities.questions.metadata.MediaQuestionAnswerFormat
import com.huma.sdk.questionnaire.core.domain.source.builder.autocompleteText
import com.huma.sdk.questionnaire.core.domain.source.builder.boolean
import com.huma.sdk.questionnaire.core.domain.source.builder.date
import com.huma.sdk.questionnaire.core.domain.source.builder.ext.ifFalse
import com.huma.sdk.questionnaire.core.domain.source.builder.ext.ifTrue
import com.huma.sdk.questionnaire.core.domain.source.builder.form
import com.huma.sdk.questionnaire.core.domain.source.builder.logic
import com.huma.sdk.questionnaire.core.domain.source.builder.options
import com.huma.sdk.questionnaire.core.domain.source.builder.page.params.FileValidationParams
import com.huma.sdk.questionnaire.core.domain.source.builder.pages
import com.huma.sdk.questionnaire.core.domain.source.builder.photo
import com.huma.sdk.questionnaire.core.domain.source.builder.scale
import com.huma.sdk.questionnaire.core.domain.source.builder.singleChoice
import com.huma.sdk.questionnaire.core.domain.source.builder.submissionPage
import com.huma.sdk.questionnaire.core.domain.source.builder.text
import com.huma.sdk.shared.user.UserStorage
import com.huma.sdk.shared.user.user.getPatientSignedUpDate
import org.threeten.bp.LocalDate

@Keep
object HemophiliaJournalQuestionnaireBuilder {
    fun forProfile(context: Context): Form {
        return form(PROFILE_QUESTIONNAIRE_ID) {
            name = context.getString(R.string.plugin_hemophilia_profile_questionnaire_title)
            pages {
                singleChoice {
                    id = HEMOPHILIA_TYPE_ID
                    text = context.getString(
                        R.string.plugin_hemophilia_please_indicate_your_type_of_hemophilia
                    )
                    isRequired = true
                    options {
                        HemophiliaType.entries.map {
                            it.name to context.getString(it.titleRes)
                        }
                    }
                }

                singleChoice {
                    id = HEMOPHILIA_SEVERITY_ID
                    text = context.getString(
                        R.string.plugin_hemophilia_please_indicate_the_severity_of_your_hemophilia
                    )
                    isRequired = true
                    options {
                        HemophiliaSeverity.entries.map {
                            it.name to context.getString(it.titleRes)
                        }
                    }
                }

                singleChoice {
                    id = HEMOPHILIA_ACTIVE_ANTIBODY_ID
                    text = context.getString(R.string.plugin_hemophilia_active_antibody_title)
                    description = context.getString(
                        R.string.plugin_hemophilia_active_antibody_description
                    )
                    isRequired = true
                    options {
                        HemophiliaActiveAntibody.entries.map {
                            it.name to context.getString(it.titleRes)
                        }
                    }
                }
            }
        }
    }

    enum class HemophiliaType(@StringRes val titleRes: Int) {
        HEMOPHILIA_A(R.string.plugin_hemophilia_type_a),
        HEMOPHILIA_B(R.string.plugin_hemophilia_type_b),
        ACQUIRED_HEMOPHILIA(R.string.plugin_hemophilia_type_acquired),
    }

    enum class HemophiliaSeverity(@StringRes val titleRes: Int) {
        MILD(R.string.common_mild),
        MODERATE(R.string.common_moderate),
        SEVERE(R.string.common_severe),
        NOT_SURE(R.string.common_not_sure)
    }

    enum class HemophiliaActiveAntibody(@StringRes val titleRes: Int) {
        NEVER(R.string.plugin_hemophilia_never_had_an_inhibitor),
        IN_PAST(R.string.plugin_hemophilia_had_an_inhibitor_in_the_past),
        HAVE_CURRENTLY(R.string.plugin_hemophilia_have_an_active_inhibitor),
        NOT_SURE(R.string.common_not_sure),
    }

    fun forAddNewBleed(
        context: Context,
        bodyPointName: String,
    ): Form {
        return form(ADD_NEW_BLEED_QUESTIONNAIRE_ID) {
            name = bodyPointName
            pages {
                date {
                    id = ACCIDENT_DATE_ID
                    text = context.getString(
                        R.string.plugin_hemophilia_please_confirm_date_of_the_bleed_pain
                    )
                    isRequired = true
                    isShouldInitWithDefaultDate = true
                    minDate = UserStorage.user?.getPatientSignedUpDate().orNow().toLocalDate()
                        .minusYears(1) // get 1 year from patient's signup date or get 1 year from now
                    maxDate = LocalDate.now()
                    summaryTitle = context.getString(R.string.plugin_hemophilia_record_date)
                }

                boolean {
                    id = TREATMENT_CONDITION_ID
                    isRequired = true
                    text = context.getString(R.string.plugin_hemophilia_did_you_treat_the_bleed)
                    logic {
                        ifTrue(
                            jumpId = TREATMENT_ID,
                            questionId = TREATMENT_CONDITION_ID,
                        )
                        ifFalse(
                            jumpId = REASON_ID,
                            questionId = TREATMENT_CONDITION_ID,
                        )
                    }
                }

                autocompleteText {
                    id = TREATMENT_ID
                    isRequired = true
                    text = context.getString(
                        R.string.plugin_hemophilia_what_treatment_was_used_to_treat_the_bleed
                    )
                    description =
                        context.getString(R.string.questionnaire_second_subtitle_select_one)
                    autocomplete = Autocomplete(
                        allowFreeText = true,
                        options = AutoCompleteString(
                            listOf(context.getString(R.string.plugin_hemophilia_alphanine_sd_coagulation_factor_ix))
                        )
                    )
                    summaryTitle = context.getString(R.string.plugin_hemophilia_treatment)
                }

                singleChoice {
                    id = REASON_ID
                    text = context.getString(R.string.plugin_hemophilia_reason_for_the_bleed_pain)
                    isRequired = true
                    options {
                        HemophiliaInjuryReason.entries.map {
                            it.name to context.getString(it.titleRes)
                        }
                    }
                    summaryTitle =
                        context.getString(R.string.plugin_hemophilia_reason_for_the_bleed)
                }

                singleChoice {
                    id = SEVERITY_ID
                    isRequired = true
                    text = context.getString(R.string.plugin_hemophilia_severity_of_the_bleed)
                    options {
                        HemophiliaInjurySeverity.entries.map {
                            it.name to context.getString(it.titleRes)
                        }
                    }
                    summaryTitle =
                        context.getString(R.string.plugin_hemophilia_severity_of_the_bleed)
                }

                scale {
                    id = SCALE_ID
                    isRequired = true
                    text = context.getString(R.string.plugin_hemophilia_pain_scale)
                    description =
                        context.getString(R.string.plugin_hemophilia_pain_scale_description)
                    lowerBound = 0
                    upperBound = 10
                    lowerBoundLabel = context.getString(R.string.plugin_hemophilia_no_pain)
                    upperBoundLabel = context.getString(R.string.common_severe)
                    summaryTitle =
                        context.getString(R.string.plugin_hemophilia_pain_scale_of_the_bleed)
                }

                text {
                    id = NOTE_ID
                    isRequired = true
                    inputType = InputType.LONG_TEXT
                    text =
                        context.getString(R.string.plugin_hemophilia_do_you_have_any_notes_to_add)
                    description =
                        context.getString(R.string.plugin_hemophilia_add_photos_and_notes_description)
                    hint = context.getString(R.string.plugin_hemophilia_enter_your_notes_here)
                    summaryTitle = context.getString(R.string.plugin_hemophilia_note)
                }

                photo {
                    id = PHOTO_ID
                    isRequired = false
                    text = context.getString(R.string.plugin_hemophilia_add_photos_title)
                    description =
                        context.getString(R.string.plugin_hemophilia_add_photos_and_notes_description)
                    fileValidationParams = FileValidationParams(
                        supportedFileFormats = MediaQuestionAnswerFormat.PHOTO.fileFormats.toList(),
                        maxFileSizeInMb = 100,
                    )
                    summaryTitle = context.getString(R.string.file_question_type_photo)
                }

                submissionPage {
                    text =
                        context.getString(R.string.plugin_hemophilia_confirm_the_information_title)
                    description =
                        context.getString(R.string.plugin_hemophilia_confirm_the_information_description)
                    isSummary = true
                }
            }
        }
    }

    private const val PROFILE_QUESTIONNAIRE_ID = "PROFILE_QUESTIONNAIRE_ID"
    private const val HEMOPHILIA_TYPE_ID = "HEMOPHILIA_TYPE_ID"
    private const val HEMOPHILIA_SEVERITY_ID = "HEMOPHILIA_SEVERITY_ID"
    private const val HEMOPHILIA_ACTIVE_ANTIBODY_ID = "HEMOPHILIA_ACTIVE_ANTIBODY_ID"

    private const val ADD_NEW_BLEED_QUESTIONNAIRE_ID = "ADD_NEW_BLEED_QUESTIONNAIRE_ID"
    const val ACCIDENT_DATE_ID = "ACCIDENT_DATE_ID"
    const val REASON_ID = "REASON_ID"
    const val SEVERITY_ID = "SEVERITY_ID"
    const val TREATMENT_CONDITION_ID = "TREATMENT_CONDITION_ID"
    const val TREATMENT_ID = "TREATMENT_ID"
    const val SCALE_ID = "SCALE_ID"
    const val NOTE_ID = "NOTE_ID"
    const val PHOTO_ID = "PHOTO_ID"
}
