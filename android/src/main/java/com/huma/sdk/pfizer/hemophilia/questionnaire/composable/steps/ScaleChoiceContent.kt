package com.huma.sdk.pfizer.hemophilia.questionnaire.composable.steps

import androidx.compose.foundation.interaction.DragInteraction
import androidx.compose.foundation.interaction.MutableInteractionSource
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableFloatStateOf
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Modifier
import androidx.compose.ui.text.AnnotatedString
import androidx.compose.ui.unit.dp
import com.huma.sdk.ui.components.composable.base_text.BaseText
import com.huma.sdk.ui.components.composable.slider.composable.LabeledSlider
import com.huma.sdk.ui.components.composable.slider.style.DefaultSliderStyle
import com.huma.sdk.ui.humastyle.HumaTypeStyler
import com.huma.sdk.ui.utils.ext.blinkOnce
import com.patrykandpatrick.vico.core.extension.orZero
import kotlin.math.roundToInt

@Composable
fun ScaleChoiceContent(
    isSubmitting: Boolean,
    lowerBound: Int?,
    upperBound: Int?,
    lowerBoundLabel: String?,
    upperBoundLabel: String?,
    answersList: List<String>,
    onAnswersUpdated: (List<String>) -> Unit,
) {
    var scaleList by remember(answersList) {
        mutableStateOf(answersList)
    }

    Column(
        modifier = Modifier
            .padding(start = 80.dp)
            .fillMaxWidth(),
    ) {
        Spacer(Modifier.height(16.dp))
        var progress by remember(answersList) {
            mutableFloatStateOf(
                answersList.firstOrNull()?.toFloatOrNull() ?: lowerBound?.toFloat() ?: 0f
            )
        }
        val interactionSource = remember { MutableInteractionSource() }
        Row {
            BaseText(
                text = lowerBoundLabel,
                style = HumaTypeStyler.headline,
            )
            Spacer(Modifier.weight(1f))
            BaseText(
                text = upperBoundLabel,
                style = HumaTypeStyler.headline,
            )
        }
        Spacer(Modifier.height(12.dp))
        LabeledSlider(
            sliderType = DefaultSliderStyle.horizontal,
            value = progress,
            onProgressChanged = {
                if (!isSubmitting) {
                    progress = it
                }
            },
            minValue = lowerBound?.toFloat().orZero,
            maxValue = upperBound?.toFloat() ?: 100f,
            labelTransformation = { newProgress ->
                AnnotatedString(newProgress.roundToInt().toString())
            },
            interactionSource = interactionSource,
            modifier = if (scaleList != answersList) {
                Modifier.blinkOnce {
                    onAnswersUpdated(scaleList)
                }
            } else Modifier
        )

        LaunchedEffect(answersList) {
            interactionSource.interactions.collect {
                when (it) {
                    is DragInteraction.Stop -> {
                        scaleList = listOf(progress.roundToInt().toString())
                    }
                }
            }
        }
    }
}
