package com.huma.sdk.pfizer.hemophilia.di

import com.huma.sdk.pfizer.hemophilia.data.HemophiliaRepository
import com.huma.sdk.pfizer.hemophilia.data.HemophiliaWidgetApi
import com.huma.sdk.pfizer.hemophilia.data.HemophiliaWidgetApiImpl
import com.huma.sdk.pfizer.hemophilia.data.HemophiliaWidgetDataResponseToWidgetDataConverter
import com.huma.sdk.pfizer.hemophilia.history.viewmodel.HemophiliaAllRecordsViewModel
import com.huma.sdk.pfizer.hemophilia.history.viewmodel.HemophiliaHistoryViewModel
import com.huma.sdk.pfizer.hemophilia.module.HemophiliaModuleProcessor
import com.huma.sdk.pfizer.hemophilia.questionnaire.HemophiliaQuestionnaireViewModel
import com.huma.sdk.pfizer.hemophilia.widget.viewmodel.HemophiliaWidgetViewModel
import org.koin.android.ext.koin.androidApplication
import org.koin.androidx.viewmodel.dsl.viewModel
import org.koin.dsl.module

val hemophiliaModule = module {
    single { HemophiliaRepository(get(), get(), get()) }
    single<HemophiliaWidgetApi> { HemophiliaWidgetApiImpl(get()) }
    single { HemophiliaWidgetDataResponseToWidgetDataConverter() }
    single { HemophiliaModuleProcessor() }

    viewModel { parameters ->
        HemophiliaQuestionnaireViewModel(
            parameters.get(),
            get(),
            androidApplication()
        )
    }
    viewModel { parameters -> HemophiliaHistoryViewModel(parameters[0], parameters[1], get()) }
    viewModel { HemophiliaAllRecordsViewModel() }
    viewModel { HemophiliaWidgetViewModel(get()) }
}
