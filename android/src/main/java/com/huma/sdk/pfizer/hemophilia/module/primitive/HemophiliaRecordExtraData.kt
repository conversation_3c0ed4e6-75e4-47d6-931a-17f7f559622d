package com.huma.sdk.pfizer.hemophilia.module.primitive

import androidx.annotation.Keep
import kotlinx.serialization.SerialName
import kotlinx.serialization.Serializable

@Keep
@Serializable
data class HemophiliaRecordExtraData(
    @SerialName("accidentDate")
    val accidentDate: String? = null,
    @SerialName("reason")
    val reason: String? = null,
    @SerialName("note")
    val note: String? = null,
    @SerialName("photo")
    val photo: String? = null,
    @SerialName("scale")
    val scale: Int? = null,
    @SerialName("severity")
    val severity: String? = null,
    @SerialName("treatment")
    val treatment: String? = null,
)
