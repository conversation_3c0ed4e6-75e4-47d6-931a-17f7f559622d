package com.huma.sdk.pfizer.hemophilia.widget

import android.os.Bundle
import androidx.annotation.Keep
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.lifecycle.compose.collectAsStateWithLifecycle
import com.huma.sdk.pfizer.hemophilia.addrecord.HemophiliaAddRecordActivity
import com.huma.sdk.pfizer.hemophilia.history.HemophiliaHistoryActivity
import com.huma.sdk.pfizer.hemophilia.questionnaire.HemophiliaQuestionnaireActivity
import com.huma.sdk.pfizer.hemophilia.questionnaire.HemophiliaQuestionnaireParam
import com.huma.sdk.pfizer.hemophilia.widget.composable.HemophiliaWidgetScreen
import com.huma.sdk.pfizer.hemophilia.widget.config.HemophiliaWidgetConfig
import com.huma.sdk.pfizer.hemophilia.widget.viewmodel.HemophiliaWidgetViewModel
import com.huma.sdk.ui.fragment.compose.BaseComposeFragment
import org.koin.androidx.viewmodel.ext.android.viewModel

@Keep
class HemophiliaWidgetFragment : BaseComposeFragment() {
    private val viewModel: HemophiliaWidgetViewModel by viewModel()

    private val id: String by lazy {
        arguments?.getString(EXTRA_HEMOPHILIA_WIDGET_ID)!!
    }

    private val type: String by lazy {
        arguments?.getString(EXTRA_HEMOPHILIA_WIDGET_TYPE)!!
    }

    private val config: HemophiliaWidgetConfig by lazy {
        arguments?.getParcelable(EXTRA_HEMOPHILIA_WIDGET_CONFIG)!!
    }

    override fun onResume() {
        super.onResume()
        viewModel.refresh(id, type)
    }

    @Composable
    override fun Content() {
        val state by viewModel.state.collectAsStateWithLifecycle()

        HemophiliaWidgetScreen(
            headerConfig = config.header,
            state = state,
            onSetupClick = {
                config.form?.let { form ->
                    HemophiliaQuestionnaireActivity.launch(
                        HemophiliaQuestionnaireParam.HemophiliaProfile(form),
                        requireContext(),
                    )
                }
            },
            onRetryClick = {
                viewModel.refresh(id, type)
            },
            onAddRecordClick = { location ->
                config.bodyMap.orEmpty().firstOrNull {
                    it.location == location
                }.let { bodyMapItem ->
                    HemophiliaAddRecordActivity.launch(
                        requireContext(),
                        config.bodyMap.orEmpty(),
                        bodyMapItem,
                    )
                }
            },
            onViewHistoryClick = {
                HemophiliaHistoryActivity.launch(
                    requireContext(),
                    id,
                    type,
                    config.description.orEmpty(),
                    config.bodyMap?.flatMap { it.points }.orEmpty(),
                )
            },
        )
    }

    companion object {
        private const val EXTRA_HEMOPHILIA_WIDGET_CONFIG = "EXTRA_HEMOPHILIA_WIDGET_CONFIG"
        private const val EXTRA_HEMOPHILIA_WIDGET_ID = "EXTRA_HEMOPHILIA_WIDGET_ID"
        private const val EXTRA_HEMOPHILIA_WIDGET_TYPE = "EXTRA_HEMOPHILIA_WIDGET_TYPE"
        fun newInstance(
            id: String,
            type: String,
            widgetConfig: HemophiliaWidgetConfig,
        ) = HemophiliaWidgetFragment().apply {
            arguments = Bundle().apply {
                putString(EXTRA_HEMOPHILIA_WIDGET_ID, id)
                putString(EXTRA_HEMOPHILIA_WIDGET_TYPE, type)
                putParcelable(EXTRA_HEMOPHILIA_WIDGET_CONFIG, widgetConfig)
            }
        }
    }
}
