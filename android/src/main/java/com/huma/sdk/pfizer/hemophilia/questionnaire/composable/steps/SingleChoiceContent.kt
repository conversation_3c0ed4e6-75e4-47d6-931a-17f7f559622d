package com.huma.sdk.pfizer.hemophilia.questionnaire.composable.steps

import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Modifier
import androidx.compose.ui.unit.dp
import com.huma.sdk.pfizer.hemophilia.model.HemophiliaQuestionnaire
import com.huma.sdk.ui.utils.ext.blinkOnce

@Composable
fun SingleChoiceContent(
    isSubmitting: Boolean,
    answersList: List<String>,
    onAnswersUpdated: (List<String>) -> Unit,
    options: List<HemophiliaQuestionnaire.Option>,
) {
    var selected by remember(answersList) {
        mutableStateOf(answersList)
    }
    Column(
        modifier = Modifier
            .fillMaxWidth()
    ) {
        Spacer(Modifier.height(8.dp))
        options.forEach {
            Spacer(Modifier.height(8.dp))
            it.Content(
                isEnabled = !isSubmitting,
                isSelected = it.value in selected,
                onClick = {
                    selected = listOf(it.value)
                },
                modifier = if (it.value in selected && answersList != selected) {
                    Modifier.blinkOnce {
                        onAnswersUpdated(selected)
                    }
                } else Modifier
            )
        }
    }
}
