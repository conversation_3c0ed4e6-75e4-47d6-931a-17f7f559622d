package com.huma.sdk.pfizer.hemophilia.questionnaire.composable.steps

import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Modifier
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.unit.dp
import com.huma.sdk.pfizer.hemophilia.model.HemophiliaQuestionnaire
import com.huma.sdk.ui.utils.ext.blinkOnce

@Composable
fun BooleanChoiceContent(
    isSubmitting: Boolean,
    answersList: List<String>,
    onAnswersUpdated: (List<String>) -> Unit,
) {
    var selected by remember(answersList) {
        mutableStateOf(answersList)
    }
    Column(
        modifier = Modifier
            .fillMaxWidth()
    ) {
        Spacer(Modifier.height(8.dp))
        HemophiliaQuestionnaire.Type.BooleanChoice.options.forEach {
            Spacer(Modifier.height(8.dp))
            HemophiliaQuestionnaire.Option(
                label = stringResource(it.first),
                value = it.second,
            ).Content(
                isEnabled = !isSubmitting,
                isSelected = it.second in selected,
                onClick = {
                    selected = listOf(it.second)
                },
                modifier = if (it.second in selected && answersList != selected) {
                    Modifier.blinkOnce {
                        onAnswersUpdated(selected)
                    }
                } else Modifier
            )
        }
    }
}
