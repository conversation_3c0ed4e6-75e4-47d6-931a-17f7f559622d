package com.huma.sdk.pfizer.hemophilia.module

import android.os.Parcelable
import com.huma.sdk.pfizer.hemophilia.model.HemophiliaBodyLocation
import com.huma.sdk.pfizer.hemophilia.model.HemophiliaInjuryReason
import com.huma.sdk.pfizer.hemophilia.model.HemophiliaInjurySeverity
import com.huma.sdk.pfizer.hemophilia.model.HemophiliaInjuryType
import kotlinx.parcelize.Parcelize
import org.threeten.bp.Instant
import org.threeten.bp.LocalDate
import org.threeten.bp.format.DateTimeFormatter

@Parcelize
data class HemophiliaModuleResult(
    val creationDateTime: Instant? = null,
    val bodyLocation: HemophiliaBodyLocation? = null,
    val bodyPartInjury: HemophiliaInjuryType,
    val customBodyPart: String? = null,
    val accidentDate: LocalDate,
    val reason: HemophiliaInjuryReason? = null,
    val note: String? = null,
    val photo: String? = null,
    val scale: Int? = null,
    val severity: HemophiliaInjurySeverity? = null,
    val treatment: String? = null,
) : Parcelable {
    val dateFormatted: String
        get() = dateFormatter.format(accidentDate)

    val dateFormatted2: String
        get() = dateFormatter2.format(accidentDate)

    companion object {
        private val dateFormatter by lazy { DateTimeFormatter.ofPattern("dd/MM/yyyy") }
        private val dateFormatter2 by lazy { DateTimeFormatter.ofPattern("d MMM yyyy") }

        fun dummy() = HemophiliaModuleResult(
            bodyLocation = HemophiliaBodyLocation.HEAD,
            bodyPartInjury = HemophiliaInjuryType.GUMS_BLEED,
            customBodyPart = null,
            accidentDate = LocalDate.now(),
            reason = HemophiliaInjuryReason.SURGERY,
            note = "Testing note",
            photo = null,
            scale = 5,
            severity = HemophiliaInjurySeverity.SEVERE,
        )
    }
}
