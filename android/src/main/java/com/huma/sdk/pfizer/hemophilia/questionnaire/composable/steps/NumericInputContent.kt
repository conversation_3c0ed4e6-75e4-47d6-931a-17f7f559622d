package com.huma.sdk.pfizer.hemophilia.questionnaire.composable.steps

import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.text.KeyboardActions
import androidx.compose.foundation.text.KeyboardOptions
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Modifier
import androidx.compose.ui.platform.LocalFocusManager
import androidx.compose.ui.text.input.KeyboardType
import androidx.compose.ui.unit.dp
import com.huma.sdk.ui.components.composable.input.InputRule
import com.huma.sdk.ui.components.composable.input.InputText
import com.huma.sdk.ui.components.composable.input.InputTextStyle

@Composable
fun NumericInputContent(
    isSubmitting: Boolean,
    upperBound: Double?,
    lowerBound: Double?,
    maxDecimals: Int?,
    answersList: List<String>,
    onAnswersUpdated: (List<String>) -> Unit,
) {
    var input by remember(answersList) {
        mutableStateOf(answersList.firstOrNull().orEmpty())
    }
    Column(
        modifier = Modifier
            .fillMaxWidth()
    ) {
        Spacer(Modifier.height(16.dp))
        val focusManager = LocalFocusManager.current
        InputText(
            value = input,
            style = InputTextStyle.OUTLINE,
            isInputEnabled = !isSubmitting,
            keyboardOptions = KeyboardOptions.Default.copy(
                keyboardType = KeyboardType.Number
            ),
            inputRules = listOf(
                InputRule.OnlyIntegerRule,
                InputRule.MaxDecimalsRule(maxDecimals)
            ),
            placeholder = "Enter value",
            onValueChange = { answer ->
                val upperCondition = upperBound?.takeIf {
                    answer.isNotBlank()
                }?.let {
                    answer.toDouble() <= it
                }
                val lowerCondition = lowerBound?.takeIf {
                    answer.isNotBlank()
                }?.let {
                    answer.toDouble() >= it
                }
                if (lowerCondition == false || upperCondition == false) {
                    return@InputText
                }
                input = answer
            },
            keyboardActions = KeyboardActions {
                if (input.isNotBlank()) {
                    onAnswersUpdated(listOf(input))
                } else {
                    onAnswersUpdated(listOf())
                }
                focusManager.clearFocus()
            }
        )
    }
}
