package com.huma.sdk.pfizer.hemophilia.model

import android.content.Context
import androidx.annotation.Keep
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.unit.dp
import com.huma.sdk.core.utils.ext.toLocalDate
import com.huma.sdk.questionnaire.R
import com.huma.sdk.questionnaire.core.domain.entities.Autocomplete
import com.huma.sdk.questionnaire.core.domain.entities.InputType
import com.huma.sdk.questionnaire.core.domain.entities.PageItem
import com.huma.sdk.questionnaire.core.domain.entities.questions.metadata.MultipleAnswers
import com.huma.sdk.questionnaire.core.domain.source.builder.page.params.FileValidationParams
import com.huma.sdk.ui.basicstyler.style.stylers.TypeStyle
import com.huma.sdk.ui.components.base.Palette
import com.huma.sdk.ui.components.composable.base_text.BaseText
import com.huma.sdk.ui.components.composable.button.Button
import com.huma.sdk.ui.components.composable.button.style.DefaultButtonStyle
import com.huma.sdk.ui.humastyle.HumaTypeStyler
import org.threeten.bp.LocalDate
import org.threeten.bp.Period
import org.threeten.bp.format.DateTimeFormatter

@Keep
data class HemophiliaQuestionnaire(
    val id: String,
    val questionnaireId: String,
    val name: String?,
    val steps: List<Step>,
) {
    @Keep
    data class Step(
        val id: String,
        val questionId: String,
        val logic: Logic?,
        val required: Boolean,
        val answersList: List<String>,
        val summaryTitle: String?,
        val header: Header?,
        val type: Type,
    ) {
        fun getAnswerText(context: Context) =
            type.getAnswerText(context, answersList)
    }

    @Keep
    sealed interface Type {
        fun getAnswerText(context: Context, answersList: List<String>): String? = null

        @Keep
        data object Info : Type

        @Keep
        data class Submission(
            val buttonText: String?,
            val isSummary: Boolean,
            val summaryItems: List<SummaryItem>,
        ) : Type

        @Keep
        data class SingleChoice(
            val options: List<Option>,
        ) : Type {
            override fun getAnswerText(context: Context, answersList: List<String>): String? {
                val answerValue = answersList.firstOrNull()
                return options.firstOrNull {
                    it.value == answerValue
                }?.label
            }

            companion object {
                fun create(item: PageItem) = SingleChoice(
                    options = item.options.orEmpty().map {
                        Option(
                            it.label,
                            it.value,
                        )
                    },
                )
            }
        }

        @Keep
        data class MultiChoice(
            val options: List<Option>,
        ) : Type {
            override fun getAnswerText(context: Context, answersList: List<String>): String? {
                return answersList.ifEmpty { null }?.mapNotNull { answerValue ->
                    options.firstOrNull {
                        it.value == answerValue
                    }?.label
                }?.joinToString()
            }

            companion object {
                fun create(item: PageItem) = MultiChoice(
                    options = item.options.orEmpty().map {
                        Option(
                            it.label,
                            it.value,
                        )
                    },
                )
            }
        }

        @Keep
        data class NumericInput(
            val upperBound: Double?,
            val lowerBound: Double?,
            val maxDecimals: Int?,
        ) : Type {
            override fun getAnswerText(context: Context, answersList: List<String>): String? {
                return answersList.firstOrNull()
            }

            companion object {
                fun create(item: PageItem) = NumericInput(
                    item.upperBound?.toDoubleOrNull(),
                    item.lowerBound?.toDoubleOrNull(),
                    item.maxDecimals,
                )
            }
        }

        @Keep
        data class TextInput(
            val placeholder: String,
            val isLongText: Boolean,
        ) : Type {
            override fun getAnswerText(context: Context, answersList: List<String>): String? {
                return answersList.firstOrNull()
            }

            companion object {
                fun create(item: PageItem) = TextInput(
                    placeholder = item.placeholder.orEmpty(),
                    isLongText = item.inputType == InputType.LONG_TEXT,
                )
            }
        }

        @Keep
        data class AutoCompleteTextInput(
            val multipleAnswers: MultipleAnswers,
            val autocomplete: Autocomplete,
        ) : Type {
            override fun getAnswerText(context: Context, answersList: List<String>): String? {
                return answersList.ifEmpty { null }?.joinToString()
            }

            companion object {
                fun create(item: PageItem) = AutoCompleteTextInput(
                    multipleAnswers = item.multipleAnswers,
                    autocomplete = item.autocomplete!!,
                )
            }
        }

        @Keep
        data class DateInput(
            val maxDate: LocalDate?,
            val minDate: LocalDate?,
            val maxISODuration: Period?,
            val minISODuration: Period?,
            val initWithDefaultDate: Boolean,
        ) : Type {
            override fun getAnswerText(context: Context, answersList: List<String>): String? {
                return answersList.firstOrNull()?.let {
                    answerTextFormatter.format(LocalDate.parse(it))
                }
            }

            @Keep
            companion object {
                private val answerTextFormatter by lazy {
                    DateTimeFormatter.ofPattern("d MMM yyyy")
                }

                fun create(item: PageItem) = DateInput(
                    maxDate = item.maxDate ?: item.validationFromBackend?.maxDate?.toLocalDate(),
                    minDate = item.minDate ?: item.validationFromBackend?.minDate?.toLocalDate(),
                    maxISODuration = item.maxISODuration
                        ?: item.validationFromBackend?.maxISODuration,
                    minISODuration = item.minISODuration
                        ?: item.validationFromBackend?.minISODuration,
                    initWithDefaultDate = item.isShouldInitWithDefaultDate,
                )
            }
        }

        @Keep
        data class ScaleChoice(
            var lowerBound: Int? = null,
            var upperBound: Int? = null,
            var lowerBoundLabel: String? = null,
            var upperBoundLabel: String? = null,
        ) : Type {
            override fun getAnswerText(context: Context, answersList: List<String>): String? {
                return answersList.firstOrNull()
            }

            companion object {
                fun create(item: PageItem) = ScaleChoice(
                    lowerBound = item.lowerBound?.toIntOrNull(),
                    upperBound = item.upperBound?.toIntOrNull(),
                    lowerBoundLabel = item.lowerBoundLabel,
                    upperBoundLabel = item.upperBoundLabel,
                )
            }
        }

        @Keep
        data class PhotoPicker(
            val fileValidationParams: FileValidationParams?,
        ) : Type {
            override fun getAnswerText(context: Context, answersList: List<String>): String? {
                return context.getString(R.string.common_uploaded)
                    .takeIf { answersList.isNotEmpty() }
            }

            companion object {
                fun create(item: PageItem) = PhotoPicker(
                    fileValidationParams = item.fileValidationParams,
                )
            }
        }

        @Keep
        data object BooleanChoice : Type {
            val options = listOf(
                R.string.questionnaire_boolean_yes to "true",
                R.string.questionnaire_boolean_no to "false",
            )

            override fun getAnswerText(context: Context, answersList: List<String>): String? {
                val answerValue = answersList.firstOrNull()
                return options.firstOrNull {
                    it.second == answerValue
                }?.let {
                    context.getString(it.first)
                }
            }
        }

        val isQuestion: Boolean
            get() = when (this) {
                is Info,
                is Submission -> false

                else -> true
            }
    }

    @Keep
    data class Header(
        val text: String,
        val description: String,
        val titleStyle: TypeStyle = HumaTypeStyler.title2,
        val descriptionStyle: TypeStyle = HumaTypeStyler.body,
    ) {
        @Composable
        fun Content(
            modifier: Modifier = Modifier,
        ) {
            Column(
                modifier = modifier
                    .fillMaxWidth()
            ) {
                BaseText(
                    text = text,
                    style = titleStyle,
                    textColor = Palette.Base.BLACK,
                    isHtml = true,
                )
                if (description.isNotBlank()) {
                    Spacer(Modifier.height(12.dp))
                    BaseText(
                        text = description,
                        style = descriptionStyle,
                        textColor = Palette.Base.GRAY_ABBEY,
                        isHtml = true,
                    )
                }
            }
        }
    }

    @Keep
    data class Logic(
        val rules: List<Rule> = emptyList(),
    ) {
        data class Rule(
            val jumpToQuestionId: String,
            val conditionSet: ConditionSet,
        )

        sealed interface ConditionSet {
            data class AnyOf(
                val conditions: List<Condition>,
            ) : ConditionSet

            data class AllOf(
                val conditions: List<Condition>,
            ) : ConditionSet

            fun isConditionPass(
                steps: List<Step>
            ) = when (this) {
                is AllOf -> {
                    conditions.all { condition ->
                        steps.any { step ->
                            condition.checkCondition(step)
                        }
                    }
                }

                is AnyOf -> conditions.any { condition ->
                    steps.any { step ->
                        condition.checkCondition(step)
                    }
                }
            }
        }

        @Keep
        sealed interface Condition {
            val questionId: String

            @Keep
            data class IsEqualTo(
                override val questionId: String,
                val value: String,
            ) : Condition

            @Keep
            data class IsNotEqualTo(
                override val questionId: String,
                val value: String,
            ) : Condition

            fun checkCondition(step: Step) =
                questionId == step.questionId && when (this) {
                    is IsEqualTo -> step.answersList.any { it == value }
                    is IsNotEqualTo -> step.answersList.all { it != value }
                }
        }
    }

    @Keep
    data class Option(
        val label: String,
        val value: String,
    ) {
        @Composable
        fun Content(
            isEnabled: Boolean,
            isSelected: Boolean,
            onClick: () -> Unit,
            modifier: Modifier = Modifier,
        ) {
            val style = if (isSelected) {
                DefaultButtonStyle.primary
            } else {
                DefaultButtonStyle.secondary
            }
            Button(
                text = label,
                buttonType = style,
                isEnable = isEnabled,
                onClick = {
                    if (!isSelected) {
                        onClick()
                    }
                },
                modifier = modifier,
            )
        }
    }

    @Keep
    data class SummaryItem(
        val summaryTitle: String,
        val summaryText: String?,
    )
}
