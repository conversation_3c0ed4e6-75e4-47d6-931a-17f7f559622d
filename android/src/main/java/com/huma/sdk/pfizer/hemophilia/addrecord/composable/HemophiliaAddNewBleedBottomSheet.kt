package com.huma.sdk.pfizer.hemophilia.addrecord.composable

import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.material3.HorizontalDivider
import androidx.compose.material3.Icon
import androidx.compose.material3.ModalBottomSheet
import androidx.compose.material3.rememberModalBottomSheetState
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import com.huma.sdk.pfizer.R
import com.huma.sdk.pfizer.hemophilia.model.HemophiliaInjuryType
import com.huma.sdk.pfizer.hemophilia.widget.config.HemophiliaWidgetConfig
import com.huma.sdk.ui.components.base.Palette
import com.huma.sdk.ui.components.base.toComposeColor
import com.huma.sdk.ui.components.composable.base_text.BaseText
import com.huma.sdk.ui.humastyle.HumaTypeStyler
import com.huma.sdk.ui.utils.ext.throttledClickable

@Composable
@Preview(showBackground = true)
private fun Preview() {
    HemophiliaAddNewBleedBottomSheetContent(
        bodyInjuryItems = listOf(
            HemophiliaWidgetConfig.BodyInjuryItem(
                name = "Pelvis",
                type = HemophiliaInjuryType.PELVIS,
            )
        ),
        onItemClick = {},
    )
}

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun HemophiliaAddNewBleedBottomSheet(
    bodyInjuryItems: List<HemophiliaWidgetConfig.BodyInjuryItem>,
    onItemClick: (HemophiliaWidgetConfig.BodyInjuryItem) -> Unit,
    onDismissRequest: () -> Unit = {},
) {
    ModalBottomSheet(
        onDismissRequest = onDismissRequest,
        sheetState = rememberModalBottomSheetState(skipPartiallyExpanded = true),
        shape = RoundedCornerShape(topEnd = 13.dp, topStart = 13.dp),
        containerColor = Color.White,
        dragHandle = null,
    ) {
        HemophiliaAddNewBleedBottomSheetContent(
            bodyInjuryItems = bodyInjuryItems,
            onItemClick = onItemClick,
        )
    }
}

@Composable
private fun HemophiliaAddNewBleedBottomSheetContent(
    bodyInjuryItems: List<HemophiliaWidgetConfig.BodyInjuryItem>,
    onItemClick: (HemophiliaWidgetConfig.BodyInjuryItem) -> Unit,
) {
    Column {
        Spacer(Modifier.height(12.dp))
        BaseText(
            text = stringResource(R.string.plugin_hemophilia_add_new_bleed),
            style = HumaTypeStyler.title2Bold,
            textColor = Palette.Primary.Text.primary,
            modifier = Modifier
                .padding(horizontal = 24.dp)
        )

        if (bodyInjuryItems.isNotEmpty()) {
            Spacer(Modifier.height(16.dp))
            for (bodyMapPointItem in bodyInjuryItems) {
                HemophiliaAddNewBleedBottomSheetItem(
                    name = bodyMapPointItem.name,
                    modifier = Modifier
                        .throttledClickable {
                            onItemClick(bodyMapPointItem)
                        }
                        .padding(horizontal = 24.dp)
                )
            }
        }

        Spacer(Modifier.height(24.dp))
    }
}

@Composable
private fun HemophiliaAddNewBleedBottomSheetItem(
    name: String,
    modifier: Modifier = Modifier,
) {
    Column(
        modifier = modifier,
    ) {
        Spacer(Modifier.height(16.dp))
        Row(
            verticalAlignment = Alignment.CenterVertically,
        ) {
            BaseText(
                text = name,
                style = HumaTypeStyler.body,
            )
            Spacer(Modifier.weight(1f))
            Icon(
                painter = painterResource(R.drawable.hsdk_arrow_right),
                contentDescription = null,
            )
        }
        Spacer(Modifier.height(14.dp))
        HorizontalDivider(color = Palette.Base.GRAY_GALLERY.toComposeColor())
    }
}
