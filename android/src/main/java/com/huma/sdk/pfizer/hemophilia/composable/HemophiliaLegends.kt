package com.huma.sdk.pfizer.hemophilia.composable

import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.unit.dp
import com.huma.sdk.pfizer.hemophilia.model.HemophiliaLegendItem
import com.huma.sdk.ui.components.base.Palette
import com.huma.sdk.ui.components.composable.base_text.BaseText
import com.huma.sdk.ui.humastyle.HumaTypeStyler
import com.huma.sdk.ui.utils.ext.parseColorIfNotBlank

@Composable
fun HemophiliaLegends(
    legends: List<HemophiliaLegendItem>,
) {
    Row(
        verticalAlignment = Alignment.CenterVertically,
        horizontalArrangement = Arrangement.Center,
        modifier = Modifier
            .fillMaxWidth()
    ) {
        legends.forEach {
            Box(
                Modifier
                    .background(
                        it.color.parseColorIfNotBlank(Color.Transparent),
                        CircleShape
                    )
                    .size(8.dp)
            )
            Spacer(Modifier.width(4.dp))
            BaseText(
                text = it.label,
                style = HumaTypeStyler.caption2,
                textColor = Palette.Base.BLACK,
            )
            Spacer(Modifier.width(8.dp))
        }
    }
}
