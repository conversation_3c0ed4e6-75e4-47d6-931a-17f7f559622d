package com.huma.sdk.pfizer.hemophilia.history

import android.content.Context
import android.content.Intent
import androidx.annotation.Keep
import androidx.compose.runtime.Composable
import com.huma.sdk.pfizer.hemophilia.history.composable.HemophiliaRecordLocationScreen
import com.huma.sdk.pfizer.hemophilia.model.HemophiliaBodyLocation
import com.huma.sdk.ui.activity.compose.AbsComposableActivity

@Keep
class HemophiliaRecordLocationActivity : AbsComposableActivity() {
    private val bodyLocation: HemophiliaBodyLocation by lazy {
        intent.getSerializableExtra(EXTRA_BODY_PART) as HemophiliaBodyLocation
    }

    private val injuryName: String by lazy {
        intent.getStringExtra(EXTRA_INJURY_NAME)!!
    }

    @Composable
    override fun Content() {
        HemophiliaRecordLocationScreen(
            injuryName = injuryName,
            bodyLocation = bodyLocation,
            onBackClick = this@HemophiliaRecordLocationActivity::finish,
        )
    }

    companion object {
        private const val EXTRA_BODY_PART = "EXTRA_BODY_PART"
        private const val EXTRA_INJURY_NAME = "EXTRA_INJURY_NAME"
        fun launch(
            context: Context,
            bodyLocation: HemophiliaBodyLocation,
            injuryName: String,
        ) {
            Intent(context, HemophiliaRecordLocationActivity::class.java).apply {
                putExtra(EXTRA_BODY_PART, bodyLocation)
                putExtra(EXTRA_INJURY_NAME, injuryName)
                context.startActivity(this)
            }
        }
    }
}
