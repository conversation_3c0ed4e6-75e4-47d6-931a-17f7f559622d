package com.huma.sdk.pfizer.hemophilia.data

import androidx.annotation.Keep
import com.huma.sdk.core.network.GenericResponse
import com.huma.sdk.core.network.ktor.getGeneric
import com.huma.sdk.core.network.ktor.postGeneric
import com.huma.sdk.core.network.ktor.setJsonBody
import io.ktor.client.HttpClient

@Keep
class HemophiliaWidgetApiImpl(
    private val ktor: HttpClient,
) : HemophiliaWidgetApi {
    override suspend fun getWidgetData(
        userId: String,
        widgetId: String,
        widgetType: String
    ): GenericResponse<HemophiliaWidgetDataResponse> {
        return ktor.getGeneric("sdk/v1/user/$userId/widget/$widgetType/$widgetId")
    }

    override suspend fun postProfileQuestionnaire(
        userId: String,
        body: HemophiliaProfileQuestionnaireRequest
    ): GenericResponse<Unit> {
        return ktor.postGeneric("hemophilia/v1/user/$userId/prerequisites") {
            setJsonBody(body)
        }
    }
}
