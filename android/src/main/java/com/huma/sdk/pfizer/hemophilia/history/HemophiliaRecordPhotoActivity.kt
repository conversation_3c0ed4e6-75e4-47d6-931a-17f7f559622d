package com.huma.sdk.pfizer.hemophilia.history

import android.content.Context
import android.content.Intent
import androidx.annotation.Keep
import androidx.compose.runtime.Composable
import com.huma.sdk.pfizer.hemophilia.history.composable.HemophiliaRecordPhotoScreen
import com.huma.sdk.shared.fileid.FileId
import com.huma.sdk.ui.activity.compose.AbsComposableActivity

@Keep
class HemophiliaRecordPhotoActivity : AbsComposableActivity() {
    private val photoId by lazy {
        intent.getStringExtra(EXTRA_PHOTO_ID)!!
    }

    @Composable
    override fun Content() {
        HemophiliaRecordPhotoScreen(
            photoId = FileId(photoId),
            onBackClick = this@HemophiliaRecordPhotoActivity::finish,
        )
    }

    companion object {
        private const val EXTRA_PHOTO_ID = "EXTRA_PHOTO_ID"
        fun launch(context: Context, photoId: String) {
            Intent(context, HemophiliaRecordPhotoActivity::class.java).apply {
                putExtra(EXTRA_PHOTO_ID, photoId)
                context.startActivity(this)
            }
        }
    }
}
