package com.huma.sdk.pfizer.hemophilia.questionnaire.composable

import androidx.compose.runtime.Composable
import com.huma.sdk.pfizer.hemophilia.model.HemophiliaQuestionnaire
import com.huma.sdk.pfizer.hemophilia.questionnaire.composable.steps.AutocompleteTextInputContent
import com.huma.sdk.pfizer.hemophilia.questionnaire.composable.steps.BooleanChoiceContent
import com.huma.sdk.pfizer.hemophilia.questionnaire.composable.steps.DateInputContent
import com.huma.sdk.pfizer.hemophilia.questionnaire.composable.steps.MultipleChoiceContent
import com.huma.sdk.pfizer.hemophilia.questionnaire.composable.steps.NumericInputContent
import com.huma.sdk.pfizer.hemophilia.questionnaire.composable.steps.PhotoPickerContent
import com.huma.sdk.pfizer.hemophilia.questionnaire.composable.steps.ScaleChoiceContent
import com.huma.sdk.pfizer.hemophilia.questionnaire.composable.steps.SingleChoiceContent
import com.huma.sdk.pfizer.hemophilia.questionnaire.composable.steps.SubmissionContent
import com.huma.sdk.pfizer.hemophilia.questionnaire.composable.steps.TextInputContent

@Composable
fun QuestionStepContent(
    isSubmitting: Boolean,
    answersList: List<String>,
    onAnswersUpdated: (List<String>) -> Unit,
    onSubmitClick: () -> Unit,
    type: HemophiliaQuestionnaire.Type,
) {
    when (type) {
        is HemophiliaQuestionnaire.Type.AutoCompleteTextInput -> {
            AutocompleteTextInputContent(
                isSubmitting,
                answersList,
                onAnswersUpdated,
                type.multipleAnswers,
                type.autocomplete,
            )
        }

        HemophiliaQuestionnaire.Type.BooleanChoice -> {
            BooleanChoiceContent(
                isSubmitting,
                answersList,
                onAnswersUpdated,
            )
        }

        is HemophiliaQuestionnaire.Type.DateInput -> {
            DateInputContent(
                isSubmitting,
                type.maxDate,
                type.minDate,
                type.maxISODuration,
                type.minISODuration,
                type.initWithDefaultDate,
                answersList,
                onAnswersUpdated,
            )
        }

        is HemophiliaQuestionnaire.Type.ScaleChoice -> {
            ScaleChoiceContent(
                isSubmitting,
                type.lowerBound,
                type.upperBound,
                type.lowerBoundLabel,
                type.upperBoundLabel,
                answersList,
                onAnswersUpdated,
            )
        }

        is HemophiliaQuestionnaire.Type.Info -> Unit
        is HemophiliaQuestionnaire.Type.MultiChoice -> {
            MultipleChoiceContent(
                isSubmitting,
                answersList,
                onAnswersUpdated,
                type.options,
            )
        }

        is HemophiliaQuestionnaire.Type.SingleChoice -> {
            SingleChoiceContent(
                isSubmitting,
                answersList,
                onAnswersUpdated,
                type.options,
            )
        }

        is HemophiliaQuestionnaire.Type.Submission -> {
            SubmissionContent(
                isSubmitting,
                type.isSummary,
                type.summaryItems,
                type.buttonText,
                onSubmitClick,
            )
        }

        is HemophiliaQuestionnaire.Type.TextInput -> {
            TextInputContent(
                isSubmitting,
                type.placeholder,
                type.isLongText,
                answersList,
                onAnswersUpdated,
            )
        }

        is HemophiliaQuestionnaire.Type.NumericInput -> {
            NumericInputContent(
                isSubmitting,
                type.upperBound,
                type.lowerBound,
                type.maxDecimals,
                answersList,
                onAnswersUpdated,
            )
        }

        is HemophiliaQuestionnaire.Type.PhotoPicker -> {
            PhotoPickerContent(
                isSubmitting,
                type.fileValidationParams,
                answersList,
                onAnswersUpdated,
            )
        }
    }
}
