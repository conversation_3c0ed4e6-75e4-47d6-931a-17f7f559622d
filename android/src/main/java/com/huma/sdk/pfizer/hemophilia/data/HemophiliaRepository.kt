package com.huma.sdk.pfizer.hemophilia.data

import androidx.annotation.Keep
import com.github.michaelbull.result.Result
import com.github.michaelbull.result.andThen
import com.huma.sdk.core.network.commons.Error
import com.huma.sdk.core.network.converter.mapResponseToResult
import com.huma.sdk.core.utils.ext.ok
import com.huma.sdk.pfizer.hemophilia.model.HemophiliaWidgetData
import com.huma.sdk.questionnaire.core.domain.entities.questions.metadata.DetailedQuestionnaireAnswer
import com.huma.sdk.shared.user.domain.repo.UserRepo
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext

@Keep
class HemophiliaRepository(
    private val userRepository: UserRepo,
    private val hemophiliaWidgetApi: HemophiliaWidgetApi,
    private val hemophiliaWidgetDataResponseToWidgetDataConverter: HemophiliaWidgetDataResponseToWidgetDataConverter,
) {
    suspend fun getWidgetData(
        widgetId: String,
        widgetType: String,
    ): Result<HemophiliaWidgetData, Error> {
        return withContext(Dispatchers.IO) {
            userRepository.getUser(false).andThen {
                hemophiliaWidgetApi.getWidgetData(it.id, widgetId, widgetType).let {
                    mapResponseToResult(it) {
                        hemophiliaWidgetDataResponseToWidgetDataConverter(requireNotNull(it)).ok()
                    }
                }
            }
        }
    }

    suspend fun submitProfileQuestionnaire(
        answers: List<DetailedQuestionnaireAnswer>,
    ): Result<Unit, Error> {
        return withContext(Dispatchers.IO) {
            userRepository.getUser(false).andThen {
                hemophiliaWidgetApi.postProfileQuestionnaire(
                    it.id,
                    HemophiliaProfileQuestionnaireRequest(
                        answers = answers
                    )
                ).let {
                    mapResponseToResult(it) {
                        Unit.ok()
                    }
                }
            }
        }
    }
}
