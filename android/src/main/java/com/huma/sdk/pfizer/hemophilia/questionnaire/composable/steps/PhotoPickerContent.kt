package com.huma.sdk.pfizer.hemophilia.questionnaire.composable.steps

import android.content.Context
import android.net.Uri
import android.widget.Toast
import androidx.compose.animation.AnimatedContent
import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.unit.dp
import coil.compose.AsyncImagePainter
import com.github.michaelbull.result.get
import com.github.michaelbull.result.onFailure
import com.github.michaelbull.result.runCatching
import com.huma.sdk.core.utils.ext.runIf
import com.huma.sdk.core.utils.files.copyToInternalStorageSuspend
import com.huma.sdk.core.utils.files.getFilepath
import com.huma.sdk.core.utils.files.toUri
import com.huma.sdk.objectstorage.coil.rememberAsyncStoragePainter
import com.huma.sdk.objectstorage.uploadFile
import com.huma.sdk.questionnaire.R
import com.huma.sdk.questionnaire.core.domain.source.builder.page.params.FileValidationParams
import com.huma.sdk.shared.fileid.FileId
import com.huma.sdk.ui.components.base.Palette
import com.huma.sdk.ui.components.base.toComposeColor
import com.huma.sdk.ui.components.composable.button.Button
import com.huma.sdk.ui.components.composable.button.style.DefaultButtonStyle
import com.huma.sdk.ui.photo_picker.MediaQuestionBottomSheet
import com.huma.sdk.ui.utils.ext.blinkOnce
import com.huma.sdk.ui.utils.ext.throttledClickable
import kotlinx.coroutines.launch

@Composable
fun PhotoPickerContent(
    isSubmitting: Boolean,
    fileValidationParams: FileValidationParams?,
    answersList: List<String>,
    onAnswersUpdated: (List<String>) -> Unit,
) {
    val context = LocalContext.current
    var showMediaPickerBottomSheet by remember(answersList) { mutableStateOf(false) }
    var isUploading by remember(answersList) { mutableStateOf(false) }
    val coroutineScope = rememberCoroutineScope()

    var imageIdList by remember(answersList) {
        mutableStateOf(answersList)
    }

    var blinkAndSubmitAnswer by remember(answersList) {
        mutableStateOf(false)
    }

    Column(
        modifier = Modifier
            .fillMaxWidth()
    ) {
        Spacer(Modifier.height(16.dp))
        AnimatedContent(
            imageIdList,
            modifier = Modifier.align(Alignment.End)
        ) { imageIdListAnimated ->
            if (imageIdListAnimated.isNotEmpty()) {
                Box(
                    modifier = Modifier
                        .clip(RoundedCornerShape(8.dp))
                ) {
                    Image(
                        painter = rememberAsyncStoragePainter(
                            FileId(imageIdListAnimated.first()),
                            onState = {
                                when (it) {
                                    is AsyncImagePainter.State.Empty,
                                    is AsyncImagePainter.State.Loading -> Unit
                                    is AsyncImagePainter.State.Error -> {
                                        if (imageIdListAnimated != answersList) {
                                            Toast.makeText(
                                                context,
                                                com.huma.sdk.ui.R.string.common_error_generic_message,
                                                Toast.LENGTH_LONG,
                                            ).show()
                                            imageIdList = answersList
                                        }
                                    }

                                    is AsyncImagePainter.State.Success -> {
                                        if (imageIdListAnimated != answersList) {
                                            blinkAndSubmitAnswer = true
                                        }
                                    }
                                }
                            },
                            contentScale = ContentScale.Crop,
                        ),
                        contentDescription = null,
                        contentScale = ContentScale.Crop,
                        modifier = Modifier
                            .background(Palette.Base.GRAY_GALLERY.toComposeColor())
                            .runIf(blinkAndSubmitAnswer) {
                                blinkOnce {
                                    onAnswersUpdated(imageIdListAnimated)
                                }
                            }
                            .width(220.dp)
                            .height(200.dp),
                    )
                    Image(
                        painterResource(R.drawable.hsdk_ic_bin),
                        contentDescription = null,
                        alignment = Alignment.Center,
                        modifier = Modifier
                            .align(Alignment.TopEnd)
                            .padding(8.dp)
                            .throttledClickable(!isSubmitting && answersList == imageIdListAnimated) {
                                onAnswersUpdated(listOf())
                            }
                            .size(24.dp)
                            .background(Color.White, CircleShape)
                            .padding(4.dp),
                    )
                }
            } else {
                Button(
                    text = stringResource(R.string.common_action_choose_photo),
                    buttonType = DefaultButtonStyle.secondary,
                    isEnable = !isUploading && !isSubmitting,
                    isLoading = isUploading,
                    onClick = {
                        showMediaPickerBottomSheet = true
                    }
                )
            }
        }
    }

    if (showMediaPickerBottomSheet) {
        fun internalUploadFile(uri: Uri, context: Context, copyToLocal: Boolean) {
            isUploading = true
            coroutineScope.launch {
                runCatching {
                    val file = if (copyToLocal) {
                        uri.copyToInternalStorageSuspend(context)
                    } else {
                        uri.getFilepath(context)
                    }
                    val result = uploadFile(file)
                    imageIdList = listOf(result.get()!!)
                    isUploading = false
                }.onFailure { e ->
                    e.printStackTrace()
                    Toast.makeText(
                        context,
                        com.huma.sdk.ui.R.string.common_error_generic_message,
                        Toast.LENGTH_LONG,
                    ).show()
                    isUploading = false
                }
            }
        }

        MediaQuestionBottomSheet(
            supportedFormats = fileValidationParams?.supportedFileFormats.orEmpty(),
            onCameraResultReady = {
                internalUploadFile(it.toUri(), context, copyToLocal = true)
                showMediaPickerBottomSheet = false
            },
            onGalleryResultReady = {
                internalUploadFile(it, context, copyToLocal = true)
                showMediaPickerBottomSheet = false
            },
            onDismissRequest = {
                showMediaPickerBottomSheet = false
            }
        )
    }
}
