package com.huma.sdk.pfizer.hemophilia.widget.config

import android.os.Parcelable
import androidx.annotation.Keep
import com.huma.sdk.pfizer.hemophilia.model.HemophiliaBodyLocation
import com.huma.sdk.pfizer.hemophilia.model.HemophiliaInjuryType
import com.huma.sdk.questionnaire.core.domain.entities.Form
import com.huma.sdk.widget.header.source.HeaderWidgetConfig
import kotlinx.parcelize.Parcelize
import kotlinx.serialization.SerialName
import kotlinx.serialization.Serializable
import kotlinx.serialization.Transient

@Keep
@Parcelize
@Serializable
data class HemophiliaWidgetConfig(
    @SerialName("header")
    val header: HeaderWidgetConfig? = null,
    @SerialName("bodyMap")
    val bodyMap: List<BodyMapItem>? = null,
    @SerialName("description")
    val description: String? = null,
    @Transient
    val form: Form? = null,
) : Parcelable {
    @Keep
    @Parcelize
    @Serializable
    data class BodyMapItem(
        @SerialName("location")
        val location: HemophiliaBodyLocation,
        @SerialName("points")
        val points: List<BodyInjuryItem>,
    ) : Parcelable

    @Keep
    @Parcelize
    @Serializable
    data class BodyInjuryItem(
        @SerialName("name")
        val name: String,
        @SerialName("value")
        val type: HemophiliaInjuryType,
    ) : Parcelable {
        companion object {
            fun forCustom(name: String) = BodyInjuryItem(
                name = name,
                type = HemophiliaInjuryType.CUSTOM,
            )
        }
    }
}
