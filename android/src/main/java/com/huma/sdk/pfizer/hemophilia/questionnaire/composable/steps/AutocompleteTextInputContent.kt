package com.huma.sdk.pfizer.hemophilia.questionnaire.composable.steps

import android.app.Activity
import android.content.Context
import android.content.Intent
import androidx.activity.compose.ManagedActivityResultLauncher
import androidx.activity.compose.rememberLauncherForActivityResult
import androidx.activity.result.ActivityResult
import androidx.activity.result.contract.ActivityResultContracts
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.unit.dp
import com.huma.sdk.questionnaire.R
import com.huma.sdk.questionnaire.core.domain.entities.Autocomplete
import com.huma.sdk.questionnaire.core.domain.entities.questions.metadata.MultipleAnswers
import com.huma.sdk.questionnaire.core.domain.flow.autocomplete.ui.AutocompleteTextActivity
import com.huma.sdk.ui.components.composable.base_text.BaseText
import com.huma.sdk.ui.components.composable.input.IconRes
import com.huma.sdk.ui.components.composable.input.InputText
import com.huma.sdk.ui.components.composable.input.InputTextStyle
import com.huma.sdk.ui.utils.ext.throttledClickable

@Composable
fun AutocompleteTextInputContent(
    isSubmitting: Boolean,
    answersList: List<String>,
    onAnswersUpdated: (List<String>) -> Unit,
    multipleAnswers: MultipleAnswers,
    autocomplete: Autocomplete,
) {
    val context = LocalContext.current
    val autocompleteTextActivity = rememberLauncherForActivityResult(
        ActivityResultContracts.StartActivityForResult()
    ) {
        it.handle(answersList, onAnswersUpdated)
    }

    Column(
        modifier = Modifier
            .fillMaxWidth()
    ) {
        if (answersList.isNotEmpty()) {
            answersList.forEachIndexed { index, answer ->
                if (index != 0) {
                    Spacer(Modifier.height(10.dp))
                }
                InputText(
                    value = answer,
                    style = InputTextStyle.OUTLINE,
                    isInputEnabled = false,
                    onValueChange = {},
                    endIconRes = IconRes.DrawableRes(R.drawable.hsdk_ic_cross_circle),
                    onEndIconClickAction = {
                        if (isSubmitting) {
                            return@InputText
                        }
                        onAnswersUpdated(answersList.filter { it != answer })
                    },
                    onInputClickOnlyWhenInputDisabled = {
                        if (isSubmitting) {
                            return@InputText
                        }
                        autocompleteTextActivity.launch(context, autocomplete, index)
                    }
                )
            }
        } else {
            InputText(
                value = "",
                style = InputTextStyle.OUTLINE,
                isInputEnabled = false,
                onValueChange = {},
                onInputClickOnlyWhenInputDisabled = {
                    if (isSubmitting) {
                        return@InputText
                    }
                    autocompleteTextActivity.launch(context, autocomplete)
                }
            )
        }
        if (multipleAnswers.enabled) {
            if (multipleAnswers.maxAnswers == 0 || multipleAnswers.maxAnswers > answersList.size) {
                Spacer(Modifier.height(10.dp))
                BaseText(
                    text = stringResource(R.string.common_action_add_more),
                    underline = true,
                    startDrawable = painterResource(R.drawable.hsdk_ic_plus),
                    startDrawablePadding = 12,
                    modifier = Modifier.throttledClickable(enabled = !isSubmitting) {
                        autocompleteTextActivity.launch(context, autocomplete)
                    }
                )
            }
        }
    }
}

private fun ActivityResult.handle(
    answersList: List<String>,
    onAnswersUpdated: (List<String>) -> Unit,
) {
    if (resultCode != Activity.RESULT_OK || data == null) {
        return
    }
    val answerData = data!!.getStringExtra(
        AutocompleteTextActivity.AUTOCOMPLETE_DATA_SEARCH_RESULT
    ) as String
    val answerText = data!!.getStringExtra(
        AutocompleteTextActivity.AUTOCOMPLETE_TEXT_SEARCH_RESULT
    ) as String
    val editedItemIndex = data!!.getIntExtra(
        AutocompleteTextActivity.INDEX_ITEM_FOR_EDITING,
        -1,
    )

    if (editedItemIndex != -1) {
        onAnswersUpdated(
            answersList.mapIndexed { idx, current ->
                if (idx == editedItemIndex) answerData else current
            }
        )
    } else {
        onAnswersUpdated(answersList + answerData.ifEmpty { answerText })
    }
}

private fun ManagedActivityResultLauncher<Intent, ActivityResult>.launch(
    context: Context,
    autocomplete: Autocomplete,
    editIndex: Int = AutocompleteTextActivity.DEFAULT_INDEX,
) {
    AutocompleteTextActivity.getListBuilderModeIntent(
        context,
        editIndex,
        arrayListOf(),
        autocomplete,
    ).also {
        launch(it)
    }
}
