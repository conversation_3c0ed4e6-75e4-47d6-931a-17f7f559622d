package com.huma.sdk.pfizer.hemophilia.history

import android.content.Context
import android.content.Intent
import androidx.annotation.Keep
import androidx.compose.runtime.Composable
import com.huma.sdk.pfizer.hemophilia.history.composable.HemophiliaRecordDetailScreen
import com.huma.sdk.pfizer.hemophilia.module.HemophiliaModuleResult
import com.huma.sdk.ui.activity.compose.AbsComposableActivity

@Keep
class HemophiliaRecordDetailActivity : AbsComposableActivity() {
    private val record: HemophiliaModuleResult by lazy {
        intent.getParcelableExtra(EXTRA_RECORD)!!
    }

    private val bodyInjuryName: String by lazy {
        intent.getStringExtra(EXTRA_BODY_INJURY_NAME)!!
    }

    @Composable
    override fun Content() {
        HemophiliaRecordDetailScreen(
            record = record,
            bodyInjuryName = bodyInjuryName,
            onOpenBodyLocationClick = { bodyLocation ->
                HemophiliaRecordLocationActivity.launch(
                    this@HemophiliaRecordDetailActivity,
                    bodyLocation,
                    bodyInjuryName,
                )
            },
            onOpenPhotoClick = { photo ->
                HemophiliaRecordPhotoActivity.launch(
                    this@HemophiliaRecordDetailActivity,
                    photo,
                )
            },
            onBackClick = this@HemophiliaRecordDetailActivity::finish,
        )
    }

    companion object {
        private const val EXTRA_RECORD = "EXTRA_RECORD"
        private const val EXTRA_BODY_INJURY_NAME = "EXTRA_BODY_INJURY_NAME"
        fun launch(
            context: Context,
            record: HemophiliaModuleResult,
            injuryName: String,
        ) {
            Intent(context, HemophiliaRecordDetailActivity::class.java).apply {
                putExtra(EXTRA_RECORD, record)
                putExtra(EXTRA_BODY_INJURY_NAME, injuryName)
                context.startActivity(this)
            }
        }
    }
}
