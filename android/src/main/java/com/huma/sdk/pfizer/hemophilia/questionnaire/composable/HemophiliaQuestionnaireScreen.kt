package com.huma.sdk.pfizer.hemophilia.questionnaire.composable

import androidx.compose.animation.core.LinearEasing
import androidx.compose.animation.core.tween
import androidx.compose.foundation.gestures.animateScrollBy
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.PaddingValues
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.heightIn
import androidx.compose.foundation.layout.statusBarsPadding
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.itemsIndexed
import androidx.compose.foundation.lazy.rememberLazyListState
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.ui.Modifier
import androidx.compose.ui.hapticfeedback.HapticFeedbackType
import androidx.compose.ui.platform.LocalConfiguration
import androidx.compose.ui.platform.LocalHapticFeedback
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.unit.dp
import com.huma.sdk.core.utils.ext.runIf
import com.huma.sdk.pfizer.R
import com.huma.sdk.pfizer.hemophilia.model.HemophiliaQuestionnaire
import com.huma.sdk.ui.components.base.Palette
import com.huma.sdk.ui.components.composable.base_text.BaseText
import com.huma.sdk.ui.components.composable.toolbar.TopNavBar
import com.huma.sdk.ui.components.composable.toolbar.style.DefaultTopNavBarStyle
import com.huma.sdk.ui.humastyle.HumaTypeStyler
import com.huma.sdk.ui.utils.ext.throttledClickable

@Composable
fun HemophiliaQuestionnaireScreen(
    title: CharSequence,
    steps: List<HemophiliaQuestionnaire.Step>,
    isSubmitting: Boolean,
    onAnswersUpdated: (newAnswerList: List<String>, step: HemophiliaQuestionnaire.Step) -> Unit,
    onSubmitClick: () -> Unit,
    onBackClick: () -> Unit,
) {
    val scrollState = rememberLazyListState()

    LaunchedEffect(steps.lastOrNull()) {
        while (scrollState.animateScrollBy(1000f, tween(easing = LinearEasing)) != 0f) {
            // Do nothing
        }
    }

    val haptic = LocalHapticFeedback.current

    TopNavBar(
        title = title,
        topNavBarType = DefaultTopNavBarStyle.base.copy(
            titleStyle = DefaultTopNavBarStyle.base.titleStyle.copy(
                typeStyle = HumaTypeStyler.title2,
            )
        ),
        showBackButton = true,
        hasLargeHeader = false,
        onBackClick = onBackClick,
        modifier = Modifier.statusBarsPadding(),
    ) {
        LazyColumn(
            state = scrollState,
            contentPadding = PaddingValues(horizontal = 24.dp, vertical = 70.dp),
        ) {
            itemsIndexed(steps) { index, step ->
                Column(
                    modifier = Modifier
                        .fillMaxWidth()
                ) {
                    Spacer(Modifier.height(70.dp))
                    step.header?.Content()
                    Column(
                        modifier = Modifier
                            .fillMaxWidth()
                            .runIf(index == steps.lastIndex) {
                                heightIn(min = (LocalConfiguration.current.screenHeightDp * 0.5).dp)
                            }
                    ) {
                        QuestionStepContent(
                            isSubmitting = isSubmitting,
                            answersList = step.answersList,
                            onAnswersUpdated = {
                                haptic.performHapticFeedback(HapticFeedbackType.LongPress)
                                onAnswersUpdated(it, step)
                            },
                            onSubmitClick = onSubmitClick,
                            type = step.type,
                        )
                        if (!step.required && index == steps.lastIndex && step.type.isQuestion) {
                            Spacer(Modifier.height(16.dp))
                            BaseText(
                                text = stringResource(R.string.plugin_hemophilia_skip_the_question),
                                style = HumaTypeStyler.body,
                                underline = true,
                                textColor = Palette.Base.GRAY_ABBEY,
                                modifier = Modifier.throttledClickable(!isSubmitting) {
                                    onAnswersUpdated(listOf(), step)
                                }
                            )
                        }
                    }
                }
            }
        }
    }
}
