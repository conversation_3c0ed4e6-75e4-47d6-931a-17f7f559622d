package com.huma.sdk.pfizer.hemophilia.widget.viewmodel

import androidx.annotation.Keep
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.github.michaelbull.result.Ok
import com.huma.sdk.pfizer.hemophilia.data.HemophiliaRepository
import com.huma.sdk.pfizer.hemophilia.model.HemophiliaWidgetData
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.launch

@Keep
class HemophiliaWidgetViewModel(
    private val hemophiliaRepository: HemophiliaRepository,
) : ViewModel() {
    private val _state = MutableStateFlow<State>(State.Loading)
    val state: StateFlow<State>
        get() = _state

    fun refresh(
        id: String,
        type: String,
    ) {
        viewModelScope.launch {
            _state.value = State.Loading
            val result = hemophiliaRepository.getWidgetData(id, type)
            if (result is Ok) {
                _state.value = result.value.let { State.Ready(it) }
            } else {
                _state.value = State.Failed
            }
        }
    }

    sealed interface State {
        data object Failed : State
        data object Loading : State
        data class Ready(
            val data: HemophiliaWidgetData,
        ) : State
    }
}
