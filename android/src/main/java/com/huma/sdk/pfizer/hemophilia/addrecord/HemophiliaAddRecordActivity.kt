package com.huma.sdk.pfizer.hemophilia.addrecord

import android.content.Context
import android.content.Intent
import androidx.annotation.Keep
import androidx.compose.runtime.Composable
import com.huma.sdk.pfizer.hemophilia.addrecord.composable.HemophiliaAddRecordScreen
import com.huma.sdk.pfizer.hemophilia.widget.config.HemophiliaWidgetConfig
import com.huma.sdk.ui.activity.compose.AbsComposableActivity

@Keep
class HemophiliaAddRecordActivity : AbsComposableActivity() {
    private val bodyMap: List<HemophiliaWidgetConfig.BodyMapItem> by lazy {
        intent.getParcelableArrayListExtra(EXTRA_BODY_MAP_ITEMS)!!
    }

    private val selectedBodyMapItem: HemophiliaWidgetConfig.BodyMapItem? by lazy {
        intent.getParcelableExtra(EXTRA_SELECTED_BODY_MAP_ITEM)
    }

    @Composable
    override fun Content() {
        HemophiliaAddRecordScreen(
            bodyMap,
            selectedBodyMapItem,
            onBackClick = this::finish,
        )
    }

    companion object {
        private const val EXTRA_SELECTED_BODY_MAP_ITEM = "EXTRA_SELECTED_BODY_LOCATION"
        private const val EXTRA_BODY_MAP_ITEMS = "EXTRA_BODY_MAP"
        fun launch(
            context: Context,
            bodyMap: List<HemophiliaWidgetConfig.BodyMapItem>,
            location: HemophiliaWidgetConfig.BodyMapItem?,
        ) {
            Intent(context, HemophiliaAddRecordActivity::class.java).apply {
                putParcelableArrayListExtra(EXTRA_BODY_MAP_ITEMS, ArrayList(bodyMap))
                putExtra(EXTRA_SELECTED_BODY_MAP_ITEM, location)
                context.startActivity(this)
            }
        }
    }
}
