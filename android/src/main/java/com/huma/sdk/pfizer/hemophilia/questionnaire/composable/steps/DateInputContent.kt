package com.huma.sdk.pfizer.hemophilia.questionnaire.composable.steps

import android.view.ViewGroup
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.viewinterop.AndroidView
import com.huma.sdk.questionnaire.widget.DateInputView
import org.threeten.bp.LocalDate
import org.threeten.bp.Period

@Composable
fun DateInputContent(
    isSubmitting: Boolean,
    maxDate: LocalDate?,
    minDate: LocalDate?,
    maxISODuration: Period?,
    minISODuration: Period?,
    initWithDefaultDate: <PERSON><PERSON><PERSON>,
    answersList: List<String>,
    onAnswersUpdated: (List<String>) -> Unit,
) {
    Column(
        modifier = Modifier
            .fillMaxWidth()
    ) {
        AndroidView(
            factory = {
                DateInputView(it).apply {
                    layoutParams = ViewGroup.LayoutParams(
                        ViewGroup.LayoutParams.MATCH_PARENT,
                        ViewGroup.LayoutParams.MATCH_PARENT,
                    )
                }
            },
            update = { view ->
                if (initWithDefaultDate) {
                    view.defaultDatePickerDate = LocalDate.now()
                } else {
                    view.defaultDatePickerDate = null
                }
                view.isEnabled = !isSubmitting
                view.date = answersList.firstOrNull()?.let { LocalDate.parse(it) }
                view.onDateSetListener = DateInputView.OnDateSetListener {
                    view.date?.toString()?.also {
                        onAnswersUpdated(listOf(it))
                    }
                }
                maxDate?.also {
                    view.setMaxDate(it)
                } ?: maxISODuration?.also {
                    view.setMaxDate(LocalDate.now() + it)
                }
                minDate?.also {
                    view.setMinDate(it)
                } ?: minISODuration?.also {
                    view.setMinDate(LocalDate.now() + it)
                }
            },
            modifier = Modifier
                .fillMaxWidth()
        )
    }
}
