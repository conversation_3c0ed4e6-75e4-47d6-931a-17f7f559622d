package com.huma.sdk.pfizer.hemophilia.data

import androidx.annotation.Keep
import com.huma.sdk.core.utils.commons.converters.Converter
import com.huma.sdk.pfizer.hemophilia.model.HemophiliaBodyLocation
import com.huma.sdk.pfizer.hemophilia.model.HemophiliaBodyMapColorItem
import com.huma.sdk.pfizer.hemophilia.model.HemophiliaLegendItem
import com.huma.sdk.pfizer.hemophilia.model.HemophiliaWidgetData

@Keep
class HemophiliaWidgetDataResponseToWidgetDataConverter :
    Converter<HemophiliaWidgetDataResponse, HemophiliaWidgetData> {
    override fun invoke(source: HemophiliaWidgetDataResponse): HemophiliaWidgetData {
        return with(source) {
            HemophiliaWidgetData(
                title = title,
                description = description,
                primaryCTAText = primaryCTAText,
                secondaryCTAText = secondaryCTAText,
                setupState = HemophiliaWidgetData.SetupState.entries.first { it.name == state },
                legend = legend?.mapNotNull { toLegendItem(it) }.orEmpty(),
                bodyMapColor = bodyMapColor?.map { toBodyNapItem(it) }.orEmpty(),
            )
        }
    }

    private fun toLegendItem(
        dto: HemophiliaWidgetDataResponse.LegendItemResponse,
    ): HemophiliaLegendItem? {
        return HemophiliaLegendItem(
            color = dto.color ?: return null,
            label = dto.label ?: return null,
        )
    }

    private fun toBodyNapItem(
        dto: HemophiliaWidgetDataResponse.BodyMapItemResponse,
    ): HemophiliaBodyMapColorItem {
        return HemophiliaBodyMapColorItem(
            location = HemophiliaBodyLocation.entries.first { it.name == dto.location },
            color = dto.color,
        )
    }
}
