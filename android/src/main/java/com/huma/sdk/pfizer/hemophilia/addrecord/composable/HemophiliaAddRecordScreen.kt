package com.huma.sdk.pfizer.hemophilia.addrecord.composable

import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.statusBarsPadding
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Modifier
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import com.huma.sdk.pfizer.R
import com.huma.sdk.pfizer.hemophilia.composable.DEFAULT_BODY_MAP_ITEMS_2
import com.huma.sdk.pfizer.hemophilia.composable.HemophiliaBodyMap
import com.huma.sdk.pfizer.hemophilia.questionnaire.HemophiliaQuestionnaireActivity
import com.huma.sdk.pfizer.hemophilia.questionnaire.HemophiliaQuestionnaireParam
import com.huma.sdk.pfizer.hemophilia.widget.config.HemophiliaWidgetConfig
import com.huma.sdk.ui.components.composable.button.Button
import com.huma.sdk.ui.components.composable.button.style.DefaultButtonStyle
import com.huma.sdk.ui.components.composable.toolbar.TopNavBar

@Composable
@Preview(showBackground = true)
private fun Preview() {
    HemophiliaAddRecordScreen(
        bodyMap = listOf(),
        preSelectedLocation = null,
        onBackClick = {},
    )
}

@Composable
fun HemophiliaAddRecordScreen(
    bodyMap: List<HemophiliaWidgetConfig.BodyMapItem>,
    preSelectedLocation: HemophiliaWidgetConfig.BodyMapItem?,
    onBackClick: () -> Unit = {},
) {
    val context = LocalContext.current
    var showAddNewBleedBottomSheet by remember(preSelectedLocation) {
        mutableStateOf(
            preSelectedLocation
        )
    }
    var showAddCustomLocationBottomSheet by remember { mutableStateOf(false) }
    TopNavBar(
        title = stringResource(R.string.plugin_hemophilia_add_new_bleed),
        showBackButton = true,
        onBackClick = onBackClick,
        modifier = Modifier
            .fillMaxSize()
            .statusBarsPadding()
    ) {
        Spacer(Modifier.height(24.dp))
        HemophiliaBodyMap(
            bodyParts = DEFAULT_BODY_MAP_ITEMS_2,
            onTouchPointClick = { selected ->
                bodyMap.firstOrNull {
                    it.location == selected
                }?.let {
                    showAddNewBleedBottomSheet = it
                }
            },
            selectedTouchPoint = showAddNewBleedBottomSheet?.location,
            modifier = Modifier.height(500.dp),
        )
        Spacer(Modifier.weight(1f))
        Button(
            text = stringResource(R.string.plugin_hemophilia_add_custom_location),
            buttonType = DefaultButtonStyle.secondary,
            onClick = {
                showAddCustomLocationBottomSheet = true
            },
            modifier = Modifier
                .padding(24.dp)
        )
    }

    showAddNewBleedBottomSheet?.let { bodyMapItem ->
        HemophiliaAddNewBleedBottomSheet(
            bodyInjuryItems = bodyMapItem.points,
            onItemClick = { item ->
                HemophiliaQuestionnaireActivity.launch(
                    param = HemophiliaQuestionnaireParam.HemophiliaJournal(
                        item,
                        bodyMapItem.location,
                    ),
                    context = context,
                )
                showAddNewBleedBottomSheet = null
            },
            onDismissRequest = {
                showAddNewBleedBottomSheet = null
            }
        )
    }

    if (showAddCustomLocationBottomSheet) {
        HemophiliaAddCustomLocationBottomSheet(
            onConfirmClick = { name ->
                HemophiliaQuestionnaireActivity.launch(
                    param = HemophiliaQuestionnaireParam.HemophiliaJournal(
                        HemophiliaWidgetConfig.BodyInjuryItem.forCustom(name)
                    ),
                    context = context,
                )
                showAddCustomLocationBottomSheet = false
            },
            onDismissRequest = {
                showAddCustomLocationBottomSheet = false
            }
        )
    }
}
