package com.huma.sdk.pfizer.hemophilia.data

import androidx.annotation.Keep
import kotlinx.serialization.SerialName
import kotlinx.serialization.Serializable

@Keep
@Serializable
data class HemophiliaWidgetDataResponse(
    val title: String? = null,
    val description: String? = null,
    @SerialName("primaryCTAtext")
    val primaryCTAText: String? = null,
    @SerialName("secondaryCTAtext")
    val secondaryCTAText: String? = null,
    val state: String? = null,
    val legend: List<LegendItemResponse>? = null,
    val bodyMapColor: List<BodyMapItemResponse>? = null,
) {
    @Keep
    @Serializable
    data class LegendItemResponse(
        val color: String? = null,
        val label: String? = null,
    )

    @Keep
    @Serializable
    data class BodyMapItemResponse(
        val location: String? = null,
        val color: String? = null,
    )
}
