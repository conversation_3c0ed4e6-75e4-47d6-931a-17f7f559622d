package com.huma.sdk.pfizer.hemophilia.questionnaire

import android.content.Context
import android.content.Intent
import android.os.Bundle
import android.widget.Toast
import androidx.annotation.Keep
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.getValue
import androidx.compose.ui.platform.LocalContext
import androidx.lifecycle.compose.LocalLifecycleOwner
import androidx.lifecycle.compose.collectAsStateWithLifecycle
import androidx.lifecycle.flowWithLifecycle
import com.huma.sdk.pfizer.hemophilia.questionnaire.composable.HemophiliaQuestionnaireScreen
import com.huma.sdk.questionnaire.R
import com.huma.sdk.ui.activity.compose.AbsComposableActivity
import com.huma.sdk.ui.utils.ext.getExtra
import org.koin.androidx.viewmodel.ext.android.viewModel
import org.koin.core.parameter.parametersOf

@Keep
class HemophiliaQuestionnaireActivity : AbsComposableActivity(routerConsumer = true) {
    private val param: HemophiliaQuestionnaireParam by lazy {
        intent.getExtra(EXTRA_QUESTIONNAIRE_PARAM)!!
    }

    private val viewModel by viewModel<HemophiliaQuestionnaireViewModel> {
        parametersOf(param)
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        onRouterConsumerValueChanged()
    }

    @Composable
    override fun Content() {
        val steps by viewModel.steps.collectAsStateWithLifecycle()
        val isSubmitting by viewModel.isSubmitting.collectAsStateWithLifecycle()

        val lifecycle = LocalLifecycleOwner.current.lifecycle
        val context = LocalContext.current
        LaunchedEffect(Unit) {
            viewModel.events.flowWithLifecycle(lifecycle).collect {
                when (it) {
                    HemophiliaQuestionnaireViewModel.Events.Error -> {
                        Toast.makeText(
                            context,
                            R.string.common_error_generic_message,
                            Toast.LENGTH_LONG,
                        ).show()
                    }

                    HemophiliaQuestionnaireViewModel.Events.NavigateBack -> finish()
                }
            }
        }

        HemophiliaQuestionnaireScreen(
            title = viewModel.pageTitle,
            steps = steps,
            isSubmitting = isSubmitting,
            onAnswersUpdated = viewModel::onAnswersUpdated,
            onSubmitClick = viewModel::onSubmitClick,
            onBackClick = this@HemophiliaQuestionnaireActivity::finish,
        )
    }

    companion object {
        private const val EXTRA_QUESTIONNAIRE_PARAM = "EXTRA_QUESTIONNAIRE_PARAM"

        fun launch(
            param: HemophiliaQuestionnaireParam,
            context: Context,
        ) {
            Intent(context, HemophiliaQuestionnaireActivity::class.java).apply {
                putExtra(EXTRA_QUESTIONNAIRE_PARAM, param)
                context.startActivity(this)
            }
        }
    }
}
