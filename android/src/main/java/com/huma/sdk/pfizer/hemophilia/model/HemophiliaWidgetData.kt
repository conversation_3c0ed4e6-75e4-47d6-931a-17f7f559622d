package com.huma.sdk.pfizer.hemophilia.model

import androidx.annotation.Keep

@Keep
data class HemophiliaWidgetData(
    val title: String? = null,
    val description: String? = null,
    val primaryCTAText: String? = null,
    val secondaryCTAText: String? = null,
    val setupState: SetupState,
    val legend: List<HemophiliaLegendItem>,
    val bodyMapColor: List<HemophiliaBodyMapColorItem>,
) {
    @Keep
    enum class SetupState {
        ACTIVE,
        SETUP_REQUIRED,
    }
}
