package com.huma.sdk.pfizer.hemophilia.questionnaire.composable.steps

import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import com.huma.sdk.core.utils.ext.indexOfFirstOrNull
import com.huma.sdk.pfizer.hemophilia.model.HemophiliaQuestionnaire
import com.huma.sdk.ui.components.base.ChipColor
import com.huma.sdk.ui.components.base.Palette
import com.huma.sdk.ui.components.composable.chip.choice.ChoiceChips
import com.huma.sdk.ui.components.composable.chip.choice.Orientation
import com.huma.sdk.ui.components.composable.chip.choice.SelectionType
import com.huma.sdk.ui.components.composable.chip.choice.style.ChoiceChipColors
import com.huma.sdk.ui.components.composable.chip.choice.style.ChoiceChipDimensions
import com.huma.sdk.ui.components.composable.chip.choice.style.ChoiceChipType
import com.huma.sdk.ui.humastyle.HumaTypeStyler

@Composable
fun MultipleChoiceContent(
    isSubmitting: Boolean,
    answersList: List<String>,
    onAnswersUpdated: (List<String>) -> Unit,
    options: List<HemophiliaQuestionnaire.Option>,
) {
    ChoiceChips(
        items = options.map { it.label },
        orientation = Orientation.HORIZONTAL_FLOW,
        choiceChipType = multipleChoiceChipsType,
        selectionType = SelectionType.MULTIPLE,
        isEnabled = !isSubmitting,
        preSelectedIndices = answersList.mapNotNull { answer ->
            options.indexOfFirstOrNull { it.value == answer }
        },
        onSelectionChanged = {
            onAnswersUpdated(it.map { options.get(it).value })
        },
        modifier = Modifier.fillMaxWidth(),
    )
}

private val multipleChoiceChipsType = ChoiceChipType(
    colors = ChoiceChipColors(
        background = ChipColor(
            selected = Palette.Base.GRAY_ABBEY,
            unselected = Palette.Base.GRAY_GALLERY,
        ),
        text = ChipColor(
            selected = Palette.Primary.Text.secondary,
            unselected = Palette.Primary.Text.primary,
        ),
        ripple = Palette.Primary.action,
        stroke = ChipColor(
            selected = Palette.Base.TRANSPARENT,
            unselected = Palette.Base.TRANSPARENT,
        )
    ),
    dimensions = ChoiceChipDimensions(
        heightDp = 32,
        cornerRadiusDp = 8,
        strokeWidthDp = 1,
        margin = 8
    ),
    typeStyle = HumaTypeStyler.headline2
)
