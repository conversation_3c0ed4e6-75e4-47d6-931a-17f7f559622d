package com.huma.sdk.pfizer.hemophilia.questionnaire

import android.app.Application
import androidx.annotation.Keep
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.github.michaelbull.result.Ok
import com.huma.sdk.core.utils.commons.navigation.Screen
import com.huma.sdk.core.utils.ext.indexOfFirstOrNull
import com.huma.sdk.core.utils.ext.runIf
import com.huma.sdk.module_kit.HumaModuleKitManager
import com.huma.sdk.module_kit.display.input.ui.data.output.ModuleInputResult
import com.huma.sdk.module_kit.display.input.ui.data.output.ModuleInputSubmitParams
import com.huma.sdk.pfizer.R
import com.huma.sdk.pfizer.hemophilia.data.HemophiliaRepository
import com.huma.sdk.pfizer.hemophilia.model.HemophiliaBodyLocation
import com.huma.sdk.pfizer.hemophilia.model.HemophiliaInjuryType
import com.huma.sdk.pfizer.hemophilia.model.HemophiliaQuestionnaire
import com.huma.sdk.pfizer.hemophilia.module.HemophiliaModule
import com.huma.sdk.pfizer.hemophilia.module.HemophiliaModuleValue
import com.huma.sdk.pfizer.hemophilia.module.primitive.HemophiliaRecordExtraData
import com.huma.sdk.questionnaire.core.domain.entities.DefaultFormPageItemFormat
import com.huma.sdk.questionnaire.core.domain.entities.Form
import com.huma.sdk.questionnaire.core.domain.entities.FormPage
import com.huma.sdk.questionnaire.core.domain.entities.FormPageType
import com.huma.sdk.questionnaire.core.domain.entities.FormSelectionCriteria
import com.huma.sdk.questionnaire.core.domain.entities.SubmissionPage
import com.huma.sdk.questionnaire.core.domain.entities.questions.metadata.DetailedQuestionnaireAnswer
import com.huma.sdk.ui.humastyle.HumaTypeStyler
import com.huma.sdk.ui.utils.ext.htmlToAnnotatedString
import kotlinx.coroutines.channels.Channel
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.receiveAsFlow
import kotlinx.coroutines.flow.update
import kotlinx.coroutines.launch
import java.util.UUID
import com.huma.sdk.questionnaire.core.domain.entities.Condition as OldCondition
import com.huma.sdk.questionnaire.core.domain.entities.Logic as OldLogic
import com.huma.sdk.questionnaire.core.domain.entities.Rule as OldRule

@Keep
class HemophiliaQuestionnaireViewModel(
    private val param: HemophiliaQuestionnaireParam,
    private val hemophiliaRepository: HemophiliaRepository,
    private val context: Application,
) : ViewModel() {
    sealed interface Events {
        data object Error : Events
        data object NavigateBack : Events
    }

    private val _events = Channel<Events>(capacity = Channel.UNLIMITED)
    val events = _events.receiveAsFlow()

    private val _steps: MutableStateFlow<List<HemophiliaQuestionnaire.Step>>
    val steps: StateFlow<List<HemophiliaQuestionnaire.Step>>
        get() = _steps

    private val _isSubmitting = MutableStateFlow(false)
    val isSubmitting: StateFlow<Boolean>
        get() = _isSubmitting

    private val form: Form = when (param) {
        is HemophiliaQuestionnaireParam.HemophiliaJournal -> param.bodyMapItem.let {
            HemophiliaJournalQuestionnaireBuilder.forAddNewBleed(context, it.name)
        }

        is HemophiliaQuestionnaireParam.HemophiliaProfile -> param.form
    }

    val pageTitle: CharSequence
        get() = when (param) {
            is HemophiliaQuestionnaireParam.HemophiliaJournal -> context.getString(
                R.string.plugin_hemophilia_journal_questionnaire_title,
                form.name.orEmpty(),
            ).htmlToAnnotatedString(context)

            is HemophiliaQuestionnaireParam.HemophiliaProfile -> form.name.orEmpty()
        }

    init {
        val stepsList = mutableListOf<HemophiliaQuestionnaire.Step>()
        for (page in form.pages) {
            val step = page.toStep()
            stepsList.add(step)
            if (page.type == FormPageType.QUESTION) {
                break
            }
        }
        _steps = MutableStateFlow(stepsList)
    }

    fun onAnswersUpdated(newAnswerList: List<String>, step: HemophiliaQuestionnaire.Step) {
        if (isSubmitting.value) {
            return
        }
        _steps.update { steps ->
            val indexOfStep = steps.indexOfFirstOrNull { it.id == step.id } ?: return
            val newStep = step.copy(answersList = newAnswerList)
            val newList = steps.subList(0, indexOfStep) + newStep

            if (newAnswerList.isEmpty() && step.required) {
                return@update newList
            }

            var jumpToId: String? = null
            form.pages.forEachIndexed { index, formPage ->
                if (formPage.getId() == step.questionId && index < form.pages.lastIndex) {
                    jumpToId = form.pages.get(index + 1).getId()
                }
            }

            step.logic?.rules?.forEach {
                if (it.conditionSet.isConditionPass(newList + newStep)) {
                    jumpToId = it.jumpToQuestionId
                }
            }

            var nextStep: HemophiliaQuestionnaire.Step? = null
            form.pages.forEach {
                if (it.getId() == jumpToId) {
                    nextStep = it.toStep()
                }
            }
            if (nextStep == null) {
                nextStep = form.submissionPage?.toStep(newList)
            }

            newList.runIf(nextStep != null) {
                this + nextStep!!
            }
        }
    }

    fun onSubmitClick() {
        when (param) {
            is HemophiliaQuestionnaireParam.HemophiliaJournal -> {
                submitForHemophiliaJournal(
                    param.bodyLocation,
                    param.bodyMapItem.type,
                    param.bodyMapItem.name,
                )
            }

            is HemophiliaQuestionnaireParam.HemophiliaProfile -> {
                submitForHemophiliaProfile()
            }
        }
    }

    private fun submitForHemophiliaJournal(
        bodyLocation: HemophiliaBodyLocation?,
        bodyInjuryType: HemophiliaInjuryType,
        bodyInjuryTypeName: String,
    ) {
        val record = _steps.value.let { steps ->
            val accidentDate = steps.firstOrNull {
                HemophiliaJournalQuestionnaireBuilder.ACCIDENT_DATE_ID == it.questionId
            }?.answersList?.firstOrNull()
            val reason = steps.firstOrNull {
                HemophiliaJournalQuestionnaireBuilder.REASON_ID == it.questionId
            }?.answersList?.firstOrNull()
            val severity = steps.firstOrNull {
                HemophiliaJournalQuestionnaireBuilder.SEVERITY_ID == it.questionId
            }?.answersList?.firstOrNull()
            val scale = steps.firstOrNull {
                HemophiliaJournalQuestionnaireBuilder.SCALE_ID == it.questionId
            }?.answersList?.firstOrNull()?.toIntOrNull()
            val treatment = steps.firstOrNull {
                HemophiliaJournalQuestionnaireBuilder.TREATMENT_ID == it.questionId
            }?.answersList?.firstOrNull()
            val note = steps.firstOrNull {
                HemophiliaJournalQuestionnaireBuilder.NOTE_ID == it.questionId
            }?.answersList?.firstOrNull()
            val photo = steps.firstOrNull {
                HemophiliaJournalQuestionnaireBuilder.PHOTO_ID == it.questionId
            }?.answersList?.firstOrNull()

            HemophiliaModuleValue.HemophiliaJournalRecord(
                bodyLocation = bodyLocation?.name,
                bodyPartInjury = bodyInjuryType.name,
                customBodyPart = bodyInjuryTypeName.takeIf {
                    bodyInjuryType == HemophiliaInjuryType.CUSTOM
                },
                extraData = HemophiliaRecordExtraData(
                    accidentDate,
                    reason,
                    note,
                    photo,
                    scale,
                    severity,
                    treatment,
                )
            )
        }
        HumaModuleKitManager
            .getInstance()
            .findModules(HemophiliaModule::class)
            .first()
            .onModuleInputSubmit(
                ModuleInputResult(HemophiliaModuleValue(record)),
                ModuleInputSubmitParams(
                    onSubmit = {
                        _events.trySend(Events.NavigateBack)
                    },
                    onFailure = {
                        _events.trySend(Events.Error)
                    },
                    loadingScreen = Screen.Loading,
                    showThankYou = false,
                    createFeedbackScreen = null,
                )
            )
    }

    private fun submitForHemophiliaProfile() {
        _steps.value.mapNotNull {
            when (it.type) {
                is HemophiliaQuestionnaire.Type.AutoCompleteTextInput -> {
                    DetailedQuestionnaireAnswer(
                        questionId = it.questionId,
                        format = DefaultFormPageItemFormat.AUTOCOMPLETE_TEXT.name,
                        answerList = it.answersList,
                    )
                }

                HemophiliaQuestionnaire.Type.BooleanChoice -> {
                    DetailedQuestionnaireAnswer(
                        questionId = it.questionId,
                        format = DefaultFormPageItemFormat.BOOLEAN.name,
                        value = it.answersList.firstOrNull(),
                    )
                }

                is HemophiliaQuestionnaire.Type.DateInput -> {
                    DetailedQuestionnaireAnswer(
                        questionId = it.questionId,
                        format = DefaultFormPageItemFormat.DATE.name,
                        answerList = it.answersList,
                    )
                }

                HemophiliaQuestionnaire.Type.Info -> null
                is HemophiliaQuestionnaire.Type.MultiChoice -> {
                    DetailedQuestionnaireAnswer(
                        questionId = it.questionId,
                        format = DefaultFormPageItemFormat.TEXTCHOICE.name,
                        selectionCriteria = FormSelectionCriteria.MULTIPLE.name,
                        selectedChoices = it.answersList,
                    )
                }

                is HemophiliaQuestionnaire.Type.NumericInput -> {
                    DetailedQuestionnaireAnswer(
                        questionId = it.questionId,
                        format = DefaultFormPageItemFormat.NUMERIC.name,
                        answerList = it.answersList,
                    )
                }

                is HemophiliaQuestionnaire.Type.SingleChoice -> {
                    DetailedQuestionnaireAnswer(
                        questionId = it.questionId,
                        format = DefaultFormPageItemFormat.TEXTCHOICE.name,
                        selectionCriteria = FormSelectionCriteria.SINGLE.name,
                        selectedChoices = it.answersList,
                    )
                }

                is HemophiliaQuestionnaire.Type.Submission -> null
                is HemophiliaQuestionnaire.Type.TextInput -> {
                    DetailedQuestionnaireAnswer(
                        questionId = it.questionId,
                        format = DefaultFormPageItemFormat.TEXT.name,
                        answerList = it.answersList,
                    )
                }

                is HemophiliaQuestionnaire.Type.PhotoPicker -> {
                    DetailedQuestionnaireAnswer(
                        questionId = it.questionId,
                        format = DefaultFormPageItemFormat.PHOTO.name,
                        answerText = it.answersList.joinToString(),
                    )
                }

                is HemophiliaQuestionnaire.Type.ScaleChoice -> {
                    DetailedQuestionnaireAnswer(
                        questionId = it.questionId,
                        format = DefaultFormPageItemFormat.SCALE.name,
                        value = it.answersList.firstOrNull(),
                    )
                }
            }?.copy(
                questionText = it.header?.text.orEmpty(),
                question = it.header?.text.orEmpty(),
            )
        }.also { answers ->
            viewModelScope.launch {
                _isSubmitting.value = true
                val result = hemophiliaRepository.submitProfileQuestionnaire(answers)
                if (result is Ok) {
                    _events.send(Events.NavigateBack)
                } else {
                    _events.send(Events.Error)
                }
                _isSubmitting.value = false
            }
        }
    }

    private fun FormPage.getId() = (items?.firstOrNull()?.id ?: id).orEmpty()

    private fun SubmissionPage.toStep(
        previousSteps: List<HemophiliaQuestionnaire.Step>,
    ): HemophiliaQuestionnaire.Step {
        val summaryItems = previousSteps.mapNotNull {
            HemophiliaQuestionnaire.SummaryItem(
                summaryTitle = it.summaryTitle ?: return@mapNotNull null,
                summaryText = it.getAnswerText(context),
            )
        }.let {
            if (param is HemophiliaQuestionnaireParam.HemophiliaJournal) {
                listOf(
                    HemophiliaQuestionnaire.SummaryItem(
                        summaryTitle = context.getString(R.string.common_record),
                        summaryText = param.bodyMapItem.name,
                    )
                ) + it
            } else it
        }

        return HemophiliaQuestionnaire.Step(
            id = UUID.randomUUID().toString(),
            questionId = id.orEmpty(),
            logic = null,
            required = false,
            answersList = emptyList(),
            summaryTitle = null,
            header = HemophiliaQuestionnaire.Header(
                text = text.orEmpty(),
                description = description.orEmpty(),
                titleStyle = if (isSummary) {
                    HumaTypeStyler.title1Bold
                } else {
                    HumaTypeStyler.title2
                }
            ),
            type = HemophiliaQuestionnaire.Type.Submission(
                buttonText = buttonText,
                isSummary = isSummary,
                summaryItems = summaryItems,
            ),
        )
    }

    private fun FormPage.toStep(
        id: String = UUID.randomUUID().toString(),
        answerList: List<String> = emptyList(),
    ): HemophiliaQuestionnaire.Step {
        val item = items?.firstOrNull()
        val header = HemophiliaQuestionnaire.Header(
            text = text ?: item?.text.orEmpty(),
            description = description ?: item?.description.orEmpty(),
        )
        val type = when {
            type == FormPageType.INFO -> HemophiliaQuestionnaire.Type.Info

            item?.format == DefaultFormPageItemFormat.TEXTCHOICE &&
                item.selectionCriteria == FormSelectionCriteria.SINGLE -> {
                HemophiliaQuestionnaire.Type.SingleChoice.create(item)
            }

            item?.format == DefaultFormPageItemFormat.TEXTCHOICE &&
                item.selectionCriteria == FormSelectionCriteria.MULTIPLE -> {
                HemophiliaQuestionnaire.Type.MultiChoice.create(item)
            }

            item?.format == DefaultFormPageItemFormat.NUMERIC -> {
                HemophiliaQuestionnaire.Type.NumericInput.create(item)
            }

            item?.format == DefaultFormPageItemFormat.TEXT -> {
                HemophiliaQuestionnaire.Type.TextInput.create(item)
            }

            item?.format == DefaultFormPageItemFormat.BOOLEAN -> {
                HemophiliaQuestionnaire.Type.BooleanChoice
            }

            item?.format == DefaultFormPageItemFormat.AUTOCOMPLETE_TEXT -> {
                HemophiliaQuestionnaire.Type.AutoCompleteTextInput.create(item)
            }

            item?.format == DefaultFormPageItemFormat.DATE -> {
                HemophiliaQuestionnaire.Type.DateInput.create(item)
            }

            item?.format == DefaultFormPageItemFormat.SCALE -> {
                HemophiliaQuestionnaire.Type.ScaleChoice.create(item)
            }

            item?.format == DefaultFormPageItemFormat.PHOTO -> {
                HemophiliaQuestionnaire.Type.PhotoPicker.create(item)
            }

            else -> {
                throw IllegalStateException("Cannot convert this form page: $item")
            }
        }
        return HemophiliaQuestionnaire.Step(
            id = id,
            questionId = getId(),
            logic = item?.logic?.toLogic(),
            required = item?.required == true,
            type = type,
            answersList = answerList,
            summaryTitle = item?.summaryTitle,
            header = header,
        )
    }

    private fun OldLogic.toLogic(): HemophiliaQuestionnaire.Logic {
        return HemophiliaQuestionnaire.Logic(
            rules?.map {
                it.toRule()
            }.takeIf {
                isEnabled
            }.orEmpty()
        )
    }

    private fun OldRule.toRule(): HemophiliaQuestionnaire.Logic.Rule {
        return HemophiliaQuestionnaire.Logic.Rule(
            jumpToId,
            (allOf?.let { allOf ->
                HemophiliaQuestionnaire.Logic.ConditionSet.AllOf(
                    allOf.map {
                        it.toCondition()
                    }
                )
            } ?: anyOf?.let { anyOf ->
                    HemophiliaQuestionnaire.Logic.ConditionSet.AnyOf(
                        anyOf.map {
                            it.toCondition()
                        }
                    )
                })!!
        )
    }

    private fun OldCondition.toCondition(): HemophiliaQuestionnaire.Logic.Condition {
        return (eq?.let {
            HemophiliaQuestionnaire.Logic.Condition.IsEqualTo(
                questionId,
                it,
            )
        } ?: neq?.let {
                HemophiliaQuestionnaire.Logic.Condition.IsNotEqualTo(
                    questionId,
                    it,
                )
            })!!
    }
}
