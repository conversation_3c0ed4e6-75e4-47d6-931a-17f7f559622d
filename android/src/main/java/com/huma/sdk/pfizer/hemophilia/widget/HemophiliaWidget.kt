package com.huma.sdk.pfizer.hemophilia.widget

import android.content.Context
import androidx.annotation.Keep
import androidx.fragment.app.Fragment
import com.huma.sdk.core.di.koin.HumaSdkKoinComponent
import com.huma.sdk.core.network.serialization.AnySerializer
import com.huma.sdk.core.utils.ext.fromJsonAny
import com.huma.sdk.pfizer.hemophilia.widget.config.HemophiliaWidgetConfig
import com.huma.sdk.questionnaire.core.domain.entities.Form
import com.huma.sdk.widget.kit.WidgetTab
import com.huma.sdk.widget.kit.WidgetTabNavigation
import kotlinx.serialization.json.Json
import kotlinx.serialization.json.JsonElement
import kotlinx.serialization.json.jsonObject
import org.koin.core.component.inject

@Keep
class HemophiliaWidget(
    id: String?,
    type: String?,
    order: Int?,
    private val config: JsonElement?,
) : WidgetTab, HumaSdkKoinComponent {
    private val json: Json by inject()

    override val id = id ?: ""
    override val type = type ?: TYPE
    override val order: Int = order ?: 0

    override val fragment: (Context, WidgetTabNavigation?) -> Fragment = { ctx, nav ->
        HemophiliaWidgetFragment.newInstance(this.id, this.type, widgetConfig)
    }

    private val widgetConfig: HemophiliaWidgetConfig by lazy {
        val form = config?.jsonObject?.get("form")?.let {
            json.decodeFromJsonElement(AnySerializer(), it).fromJsonAny<Form>()
        }
        json.decodeFromJsonElement(
            AnySerializer(),
            config!!
        ).fromJsonAny<HemophiliaWidgetConfig>().copy(form = form)
    }

    companion object {
        const val TYPE = "com.pfizer.widget.hemophilia_journal"
    }
}
