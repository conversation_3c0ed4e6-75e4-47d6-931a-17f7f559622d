package com.huma.sdk.pfizer.hemophilia.history.composable

import androidx.compose.foundation.Image
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.statusBarsPadding
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.unit.dp
import com.huma.sdk.objectstorage.coil.rememberAsyncStoragePainter
import com.huma.sdk.pfizer.R
import com.huma.sdk.shared.fileid.FileId
import com.huma.sdk.ui.components.composable.toolbar.TopNavBar

@Composable
fun HemophiliaRecordPhotoScreen(
    photoId: FileId,
    onBackClick: () -> Unit,
) {
    TopNavBar(
        title = stringResource(R.string.file_question_type_photo),
        showBackButton = true,
        onBackClick = onBackClick,
        modifier = Modifier
            .fillMaxSize()
            .statusBarsPadding()
    ) {
        Spacer(Modifier.height(8.dp))
        Image(
            painter = rememberAsyncStoragePainter(photoId, contentScale = ContentScale.Crop),
            contentScale = ContentScale.Crop,
            contentDescription = null,
            modifier = Modifier
                .clip(RoundedCornerShape(8.dp))
                .fillMaxWidth()
        )
    }
}
