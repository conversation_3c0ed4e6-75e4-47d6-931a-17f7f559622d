package com.huma.sdk.pfizer.hemophilia.widget.composable

import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.statusBarsPadding
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.unit.dp
import com.huma.sdk.pfizer.hemophilia.composable.DEFAULT_BODY_MAP_ITEMS_1
import com.huma.sdk.pfizer.hemophilia.composable.HemophiliaBodyMap
import com.huma.sdk.pfizer.hemophilia.model.HemophiliaBodyLocation
import com.huma.sdk.pfizer.hemophilia.model.HemophiliaWidgetData
import com.huma.sdk.pfizer.hemophilia.widget.viewmodel.HemophiliaWidgetViewModel
import com.huma.sdk.widget.header.presentation.HeaderWidgetView
import com.huma.sdk.widget.header.source.HeaderWidgetConfig

@Composable
fun HemophiliaWidgetScreen(
    headerConfig: HeaderWidgetConfig?,
    state: HemophiliaWidgetViewModel.State,
    onSetupClick: () -> Unit = {},
    onRetryClick: () -> Unit = {},
    onAddRecordClick: (HemophiliaBodyLocation?) -> Unit = {},
    onViewHistoryClick: () -> Unit = {},
) {
    Column(
        modifier = Modifier
            .statusBarsPadding()
            .fillMaxSize()
    ) {
        HeaderWidgetView(headerConfig?.copy(showLogo = true))
        Spacer(Modifier.height(16.dp))

        val isActive = state is HemophiliaWidgetViewModel.State.Ready &&
            state.data.setupState == HemophiliaWidgetData.SetupState.ACTIVE
        val bodyParts = if (isActive) {
            (state as HemophiliaWidgetViewModel.State.Ready).data.bodyMapColor
        } else {
            DEFAULT_BODY_MAP_ITEMS_1
        }
        HemophiliaBodyMap(
            bodyParts = bodyParts,
            onTouchPointClick = { selected ->
                onAddRecordClick(selected)
            },
            isEnabled = isActive,
            modifier = Modifier.weight(1f),
        )
        Spacer(Modifier.height(8.dp))
        HemophiliaWidgetBottomSection(
            state,
            onSetupClick,
            onRetryClick,
            onAddRecordClick = {
                onAddRecordClick(null)
            },
            onViewHistoryClick,
        )
        Spacer(Modifier.height(8.dp))
    }
}
