package com.huma.sdk.pfizer.hemophilia.data

import androidx.annotation.Keep
import com.huma.sdk.questionnaire.core.domain.entities.questions.metadata.DetailedQuestionnaireAnswer
import kotlinx.serialization.Serializable

@Keep
@Serializable
data class HemophiliaProfileQuestionnaireRequest(
    val id: String? = null,
    val answers: List<DetailedQuestionnaireAnswer>,
    val updateDateTime: String? = null,
    val createDateTime: String? = null,
)
