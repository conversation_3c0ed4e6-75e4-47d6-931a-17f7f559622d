package com.huma.sdk.pfizer.hemophilia.questionnaire

import android.os.Parcelable
import androidx.annotation.Keep
import com.huma.sdk.pfizer.hemophilia.model.HemophiliaBodyLocation
import com.huma.sdk.pfizer.hemophilia.widget.config.HemophiliaWidgetConfig
import com.huma.sdk.questionnaire.core.domain.entities.Form
import kotlinx.parcelize.Parcelize

@Parcelize
@Keep
sealed interface HemophiliaQuestionnaireParam : Parcelable {
    @Keep
    data class HemophiliaProfile(
        val form: Form
    ) : HemophiliaQuestionnaireParam

    @Keep
    data class HemophiliaJournal(
        val bodyMapItem: HemophiliaWidgetConfig.BodyInjuryItem,
        val bodyLocation: HemophiliaBodyLocation? = null,
    ) : HemophiliaQuestionnaireParam
}
