package com.huma.sdk.pfizer.hemophilia.addrecord.composable

import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.material3.ModalBottomSheet
import androidx.compose.material3.rememberModalBottomSheetState
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import com.huma.sdk.pfizer.R
import com.huma.sdk.ui.components.composable.base_text.BaseText
import com.huma.sdk.ui.components.composable.button.Button
import com.huma.sdk.ui.components.composable.button.style.DefaultButtonStyle
import com.huma.sdk.ui.components.composable.input.InputText
import com.huma.sdk.ui.components.composable.input.InputTextStyle
import com.huma.sdk.ui.humastyle.HumaTypeStyler

@Preview(showBackground = true)
@Composable
private fun Preview() {
    HemophiliaAddCustomLocationBottomSheetContent(onConfirmClick = {})
}

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun HemophiliaAddCustomLocationBottomSheet(
    onConfirmClick: (name: String) -> Unit,
    onDismissRequest: () -> Unit,
) {
    ModalBottomSheet(
        onDismissRequest = onDismissRequest,
        sheetState = rememberModalBottomSheetState(skipPartiallyExpanded = true),
        shape = RoundedCornerShape(topEnd = 13.dp, topStart = 13.dp),
        containerColor = Color.White,
        dragHandle = null,
    ) {
        HemophiliaAddCustomLocationBottomSheetContent(onConfirmClick)
    }
}

@Composable
private fun HemophiliaAddCustomLocationBottomSheetContent(
    onConfirmClick: (name: String) -> Unit,
) {
    var input by remember { mutableStateOf("") }
    Column(
        modifier = Modifier
            .padding(horizontal = 24.dp)
    ) {
        Spacer(Modifier.height(12.dp))
        BaseText(
            text = stringResource(R.string.plugin_hemophilia_add_custom_location),
            style = HumaTypeStyler.title2Bold,
        )
        BaseText(
            text = stringResource(R.string.plugin_hemophilia_add_custom_location_description),
            style = HumaTypeStyler.body,
        )

        Spacer(Modifier.height(24.dp))

        InputText(
            value = input,
            style = InputTextStyle.OUTLINE,
            placeholder = stringResource(R.string.plugin_hemophilia_type_in_body_part),
            singleLine = true,
            onValueChange = {
                input = it
            },
        )

        Spacer(Modifier.height(300.dp))

        Button(
            text = stringResource(R.string.common_action_confirm),
            buttonType = DefaultButtonStyle.primary,
            isEnable = input.isNotBlank(),
            onClick = {
                onConfirmClick(input.trim())
            }
        )
    }
}
