package com.huma.sdk.pfizer.hemophilia.module.converter

import androidx.annotation.Keep
import com.huma.sdk.core.utils.commons.converters.Converter
import com.huma.sdk.module_kit.display.input.ui.data.output.Value
import com.huma.sdk.module_kit.factory.ModuleConfigId
import com.huma.sdk.pfizer.hemophilia.module.HemophiliaModuleValue
import com.huma.sdk.pfizer.hemophilia.module.primitive.HemophiliaModulePrimitive

@Keep
class HemophiliaModuleValueToPrimitiveConverter(
    private val moduleConfigId: ModuleConfigId,
    private val deploymentId: String,
    private val version: Int,
    private val deviceName: String,
) : Converter<List<Value<*>>, List<HemophiliaModulePrimitive>> {
    override fun invoke(source: List<Value<*>>): List<HemophiliaModulePrimitive> {
        return source.filterIsInstance<HemophiliaModuleValue>().let {
            it.map { hemophiliaModuleValue ->
                val hemophiliaModuleReading = hemophiliaModuleValue.value
                HemophiliaModulePrimitive(
                    deviceName = deviceName,
                    version = version,
                    deploymentId = deploymentId,
                    moduleId = moduleConfigId,
                    bodyLocation = hemophiliaModuleReading.bodyLocation,
                    bodyPartInjury = hemophiliaModuleReading.bodyPartInjury,
                    customBodyPart = hemophiliaModuleReading.customBodyPart,
                    extraData = hemophiliaModuleReading.extraData,
                )
            }
        }
    }
}
