package com.huma.sdk.roche.pre_eclampsia.widget.data

import androidx.annotation.Keep
import com.huma.sdk.core.network.GenericResponse
import com.huma.sdk.core.network.ktor.getGeneric
import com.huma.sdk.core.network.ktor.postGeneric
import com.huma.sdk.core.network.ktor.setJsonBody
import io.ktor.client.HttpClient
import kotlinx.serialization.json.JsonElement

@Keep
class PreeclampsiaRiskWidgetApiImpl(
    private val ktor: HttpClient,
) : PreeclampsiaRiskWidgetApi {
    override suspend fun getWidgetData(
        userId: String,
        widgetId: String,
        widgetType: String,
    ): GenericResponse<JsonElement> {
        return ktor.getGeneric("sdk/v1/user/$userId/widget/$widgetType/$widgetId")
    }

    override suspend fun updatePregnancyDate(
        userId: String,
        body: UpdatePregnancyDateRequest,
    ): GenericResponse<Unit> {
        return ktor.postGeneric("/api/cds/v1/user/$userId/pregnancy") {
            setJsonBody(body)
        }
    }
}
