package com.huma.sdk.roche.pre_eclampsia.widget.data

import androidx.annotation.Keep
import com.github.michaelbull.result.Result
import com.github.michaelbull.result.andThen
import com.huma.sdk.core.network.commons.Error
import com.huma.sdk.core.network.converter.mapResponseToResult
import com.huma.sdk.shared.user.domain.repo.UserRepo
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext
import kotlinx.serialization.json.JsonElement
import org.threeten.bp.LocalDate

@Keep
class PreeclampsiaRiskWidgetRepository(
    private val userRepo: UserRepo,
    private val api: PreeclampsiaRiskWidgetApi,
) {
    suspend fun getWidgetData(
        widgetId: String,
        widgetType: String,
    ): Result<JsonElement, Error> = withContext(Dispatchers.IO) {
        userRepo.getUser(false).andThen {
            api.getWidgetData(
                it.id,
                widgetId,
                widgetType,
            ).let { response ->
                mapResponseToResult(response)
            }
        }
    }

    suspend fun updatePregnancyDate(
        newDate: LocalDate,
    ): Result<Unit, Error> {
        return userRepo.getUser(false).andThen {
            api.updatePregnancyDate(
                it.id,
                UpdatePregnancyDateRequest(pregnancyDate = newDate.toString()),
            ).let {
                mapResponseToResult(it)
            }
        }
    }
}
