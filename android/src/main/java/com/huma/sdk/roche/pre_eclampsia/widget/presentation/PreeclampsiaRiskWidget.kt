package com.huma.sdk.roche.pre_eclampsia.widget.presentation

import android.content.Context
import android.content.Intent
import android.widget.Toast
import androidx.annotation.Keep
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.unit.dp
import androidx.lifecycle.compose.collectAsStateWithLifecycle
import com.github.michaelbull.result.andThen
import com.huma.sdk.core.HumaFoundationManager
import com.huma.sdk.core.di.DispatcherName
import com.huma.sdk.core.di.koin.HumaSdkKoinComponent
import com.huma.sdk.core.utils.ext.ok
import com.huma.sdk.core.utils.flags.StartActivityExtras.setExpectedTabWidget
import com.huma.sdk.module_kit.HumaModuleKitManager
import com.huma.sdk.module_kit.ModuleWithInput
import com.huma.sdk.module_kit.analytics.sources.HumaModuleEventSource
import com.huma.sdk.roche.pre_eclampsia.module.PreeclampsiaRiskModule
import com.huma.sdk.roche.pre_eclampsia.widget.composable.PreeclampsiaRiskActiveStateContent
import com.huma.sdk.roche.pre_eclampsia.widget.composable.PreeclampsiaRiskSetupDateBottomSheet
import com.huma.sdk.roche.pre_eclampsia.widget.composable.PreeclampsiaRiskSetupStateContent
import com.huma.sdk.roche.pre_eclampsia.widget.composable.PreeclampsiaRiskWidgetCardLoadingContent
import com.huma.sdk.roche.pre_eclampsia.widget.data.PreeclampsiaRiskWidgetData
import com.huma.sdk.roche.pre_eclampsia.widget.data.PreeclampsiaRiskWidgetRepository
import com.huma.sdk.roche.pre_eclampsia.widget.source.PreeclampsiaRiskWidgetConfig
import com.huma.sdk.shared.deployment.domain.entity.Deployment
import com.huma.sdk.shared.deployment.storage.DeploymentStorage
import com.huma.sdk.ui.R
import com.huma.sdk.ui.components.composable.snackbar.style.DefaultSnackbarStyle
import com.huma.sdk.ui.snackbar_provider.LocalSnackBarProvider
import com.huma.sdk.ui.snackbar_provider.SnackBarProvider
import com.huma.sdk.widget.kit.Widget
import com.patrykandpatrick.vico.core.extension.orZero
import kotlinx.coroutines.CoroutineDispatcher
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.launch
import kotlinx.serialization.json.Json
import kotlinx.serialization.json.JsonElement
import kotlinx.serialization.json.decodeFromJsonElement
import org.koin.core.component.inject
import org.koin.core.qualifier.named
import org.threeten.bp.LocalDate

@Keep
class PreeclampsiaRiskWidget(
    id: String?,
    type: String?,
    order: Int?,
    private val config: JsonElement?,
) : Widget, HumaSdkKoinComponent {
    override val id: String = id ?: ""
    override val type: String = type ?: TYPE
    override val order: Int = order ?: Int.MAX_VALUE

    private val mainDispatcher: CoroutineDispatcher by inject(named(DispatcherName.Main))
    private val coroutineScope = CoroutineScope(mainDispatcher)
    private val repository: PreeclampsiaRiskWidgetRepository by inject()

    private val context by inject<Context>()

    private val json: Json by inject()

    val widgetConfig: PreeclampsiaRiskWidgetConfig by lazy {
        json.decodeFromJsonElement(config!!)
    }

    private val state = MutableStateFlow<WidgetDataState>(WidgetDataState.Loading)
    private val isUpdatingPregnancyDate = MutableStateFlow(false)

    @Composable
    override fun WidgetCard() {
        val snackBarProvider = LocalSnackBarProvider.current
        val context = LocalContext.current
        val state by state.collectAsStateWithLifecycle()
        val isUpdatingPregnancyDate by isUpdatingPregnancyDate.collectAsStateWithLifecycle()
        var showSetupDateBottomSheet by remember { mutableStateOf(false) }
        Column(
            modifier = Modifier
                .fillMaxWidth()
                .padding(horizontal = 24.dp, vertical = 16.dp)
                .background(Color.White, RoundedCornerShape(18.dp))
                .padding(16.dp)
        ) {
            when (state) {
                WidgetDataState.Loading -> {
                    PreeclampsiaRiskWidgetCardLoadingContent()
                }

                is WidgetDataState.Ready -> {
                    val data = (state as WidgetDataState.Ready).data
                    when (data.state) {
                        PreeclampsiaRiskWidgetData.SetupState.SETUP_REQUIRED -> {
                            with(data) {
                                PreeclampsiaRiskSetupStateContent(
                                    title = title.orEmpty(),
                                    subtitle = subtitle.orEmpty(),
                                    description = description.orEmpty(),
                                    primaryCTAText = primaryCTAText.orEmpty(),
                                    isUpdatingPregnancyDate = isUpdatingPregnancyDate,
                                    onPrimaryCTAClick = {
                                        if (DeploymentStorage.deployment.isWelcomeFlowOngoing) {
                                            snackBarProvider.showWelcomeWidgetNotCompletedToast(context)
                                            return@PreeclampsiaRiskSetupStateContent
                                        }

                                        showSetupDateBottomSheet = true
                                    }
                                )
                            }
                        }

                        PreeclampsiaRiskWidgetData.SetupState.ACTIVE -> {
                            with(data) {
                                PreeclampsiaRiskActiveStateContent(
                                    title = title.orEmpty(),
                                    subtitle = subtitle.orEmpty(),
                                    description = description.orEmpty(),
                                    progress = progress.orZero / 100,
                                    progressText = progressText.orEmpty(),
                                    message = message?.text.orEmpty(),
                                    messageType = message?.type ?: PreeclampsiaRiskWidgetData.MessageType.INFO,
                                    imageUrl = imageUrl.orEmpty(),
                                    primaryCTAText = primaryCTAText.orEmpty(),
                                    secondaryCTAText = secondaryCTAText.orEmpty(),
                                    isUpdatingPregnancyDate = isUpdatingPregnancyDate,
                                    onPrimaryCTAClick = {
                                        if (DeploymentStorage.deployment.isWelcomeFlowOngoing) {
                                            snackBarProvider.showWelcomeWidgetNotCompletedToast(context)
                                            return@PreeclampsiaRiskActiveStateContent
                                        }

                                        val module = HumaModuleKitManager.getInstance()
                                            .findModule<ModuleWithInput>(
                                                moduleId = PreeclampsiaRiskModule.MODULE_ID
                                            )
                                        module?.openInput(
                                            context = context,
                                            source = HumaModuleEventSource.TRACK,
                                            onSubmit = module::onModuleInputSubmit
                                        )
                                    },
                                    onSecondaryCTAClick = {
                                        if (DeploymentStorage.deployment.isWelcomeFlowOngoing) {
                                            snackBarProvider.showWelcomeWidgetNotCompletedToast(context)
                                            return@PreeclampsiaRiskActiveStateContent
                                        }

                                        showSetupDateBottomSheet = true
                                    }
                                )
                            }
                        }
                    }
                }
            }
        }

        if (showSetupDateBottomSheet) {
            PreeclampsiaRiskSetupDateBottomSheet(
                onDateSelected = {
                    updatePregnancyDate(it)
                    showSetupDateBottomSheet = false
                },
                onDismissRequest = {
                    showSetupDateBottomSheet = false
                }
            )
        }
    }

    private fun SnackBarProvider.showWelcomeWidgetNotCompletedToast(context: Context) {
        showSnackBar(
            snackbarType = DefaultSnackbarStyle.error,
            text = context.getString(R.string.widget_welcome_error_toast_message),
            action = context.getString(R.string.widget_welcome_error_toast_action),
            onActionClick = {
                HumaFoundationManager.getInstance().startActivityClass?.let {
                    Intent(context, it)
                        .setExpectedTabWidget(Deployment.WELCOME_WIDGET)
                        .also {
                            context.startActivity(it)
                        }
                }
            },
        )
    }

    override fun onViewed() {
        coroutineScope.launch {
            onRefresh()
        }
    }

    override suspend fun onRefresh() {
        state.value = WidgetDataState.Loading
        val (result) = repository.getWidgetData(
            widgetId = id,
            widgetType = type,
        ).andThen {
            json.decodeFromJsonElement<PreeclampsiaRiskWidgetData>(it).ok()
        }
        if (result != null) {
            state.value = WidgetDataState.Ready(result)
        } else {
            Toast.makeText(
                context,
                R.string.common_error_generic_message,
                Toast.LENGTH_LONG,
            ).show()
        }
    }

    private fun updatePregnancyDate(newDate: LocalDate) {
        coroutineScope.launch {
            isUpdatingPregnancyDate.value = true
            val (result) = repository.updatePregnancyDate(newDate)
            if (result != null) {
                onRefresh()
            } else {
                Toast.makeText(
                    context,
                    R.string.common_error_generic_message,
                    Toast.LENGTH_LONG,
                ).show()
            }
            isUpdatingPregnancyDate.value = false
        }
    }

    @Keep
    sealed interface WidgetDataState {
        data object Loading : WidgetDataState
        data class Ready(
            val data: PreeclampsiaRiskWidgetData,
        ) : WidgetDataState
    }

    companion object {
        const val TYPE = "com.roche.widget.pre_eclampsia_risk"
    }
}
