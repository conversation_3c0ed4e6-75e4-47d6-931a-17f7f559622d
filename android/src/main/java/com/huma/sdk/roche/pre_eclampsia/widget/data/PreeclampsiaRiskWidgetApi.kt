package com.huma.sdk.roche.pre_eclampsia.widget.data

import androidx.annotation.Keep
import com.huma.sdk.core.network.GenericResponse
import kotlinx.serialization.json.JsonElement

@Keep
interface PreeclampsiaRiskWidgetApi {
    suspend fun getWidgetData(
        userId: String,
        widgetId: String,
        widgetType: String,
    ): GenericResponse<JsonElement>

    suspend fun updatePregnancyDate(
        userId: String,
        body: UpdatePregnancyDateRequest,
    ): GenericResponse<Unit>
}
