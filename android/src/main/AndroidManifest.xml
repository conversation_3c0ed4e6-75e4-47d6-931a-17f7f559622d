<?xml version="1.0" encoding="utf-8"?>
<manifest xmlns:android="http://schemas.android.com/apk/res/android">

    <application>
        <activity
            android:name="com.huma.sdk.pfizer.hemophilia.questionnaire.HemophiliaQuestionnaireActivity"
            android:theme="@style/HumaAppTheme" />

        <activity
            android:name="com.huma.sdk.pfizer.hemophilia.addrecord.HemophiliaAddRecordActivity"
            android:theme="@style/HumaAppTheme" />

        <activity
            android:name="com.huma.sdk.pfizer.hemophilia.history.HemophiliaHistoryActivity"
            android:theme="@style/HumaAppTheme" />
        <activity
            android:name="com.huma.sdk.pfizer.hemophilia.history.HemophiliaAllRecordsActivity"
            android:theme="@style/HumaAppTheme" />
        <activity
            android:name="com.huma.sdk.pfizer.hemophilia.history.HemophiliaRecordDetailActivity"
            android:theme="@style/HumaAppTheme" />
        <activity
            android:name="com.huma.sdk.pfizer.hemophilia.history.HemophiliaRecordLocationActivity"
            android:theme="@style/HumaAppTheme" />
        <activity
            android:name="com.huma.sdk.pfizer.hemophilia.history.HemophiliaRecordPhotoActivity"
            android:theme="@style/HumaAppTheme" />
    </application>
</manifest>