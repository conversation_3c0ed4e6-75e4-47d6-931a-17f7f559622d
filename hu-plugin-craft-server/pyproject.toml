[project]
name = "hu_plugin_craft_server"
version = "1.1.0"
description = "Add your description here"
readme = "README.md"
requires-python = "~=3.11"
dependencies = [
    "django>=5.1.5",
    "django-filter>=25.1",
    "djangorestframework>=3.15.2",
    "framework",
    "markdown>=3.7",
    "sdk",
    "ruff>=0.9.7",
    "model-bakery>=1.20.4",
]

[dependency-groups]
dev = [
    "black==23.12.0",
    "coverage==7.3.4",
    "freezegun==1.4.0",
    "parameterized==0.9.0",
    "pre-commit==3.6.0",
    "pytest-cov==4.1.0",
    "pytest-django==4.10.0",
    "yamllint==1.33.0",
]

[tool.uv.sources]
framework = { git = "https://github.com/huma-engineering/huma-server-platform.git", rev = "v2.4.3" }
#sdk = { path = "libs/huma-server-sdk" }
sdk = { path = "../../huma-server-sdk" }

[tool.ruff]
line-length = 120
indent-width = 4
target-version = "py311"

[tool.ruff.lint]
select = [
  "F",
  "TID",
]
ignore = [
  "S101", # Use of assert detected https://docs.astral.sh/ruff/rules/assert/
  "RUF012", # Mutable class attributes should be annotated with `typing.ClassVar`
  "SIM102", # sometimes it's better to nest
  "UP038", # Checks for uses of isinstance/issubclass that take a tuple
          # of types for comparison.
          # Deactivated because it can make the code slow:
          # https://github.com/astral-sh/ruff/issues/7871
]
# Allow fix for all enabled rules (when `--fix`) is provided.
fixable = ["ALL"]
unfixable = []
# The fixes in extend-unsafe-fixes will require
# provide the `--unsafe-fixes` flag when fixing.
extend-unsafe-fixes = [
    "UP038"
]

[tool.ruff.lint.per-file-ignores]
"__init__.py" = ["F401"]
