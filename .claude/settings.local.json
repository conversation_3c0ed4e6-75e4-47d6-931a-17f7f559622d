{"permissions": {"allow": ["Bash(rg:*)", "Bash(run_tests_slow:*)", "Bash(find:*)", "Bash(grep:*)", "Bash(python -m pytest libs/hu-plugin-billing/billing/components/export/tests/IntegrationTests/monthly_report_export_tests.py::MonthlyReportExportTestCase::test_export_for_calendar_period_calculations__first_period_completed_others_not_started -v)", "Bash(ls:*)", "Bash(python -m pytest libs/huma-server-plugins/huma_plugins/components/ai_engine/tests/UnitTests/text_ai_service_tests.py -v)", "Bash(python -m pytest libs/huma-server-plugins/huma_plugins/components/ai_engine/tests/UnitTests/text_ai_service_tests.py::test_generated_message_too_long_raises_exception -v)", "Bash(python -m pytest huma_plugins/components/ai_engine/tests/UnitTests/text_ai_service_tests.py::TextGenAIServiceTestCase::test_generated_message_too_long_raises_exception -v)"], "deny": []}}