//
//  HemoJournalWidgetConfig.swift
//  HemoPlugin
//
//  Created by <PERSON> on 17/03/2025.
//  Copyright © 2025 Huma Therapeutics Ltd. All rights reserved.
//

import HumaFoundation

struct HemoJournalWidgetConfig: Codable, Hashable, Equatable {
    let header: Header
    let bodyMap: [BleedData]
    let jointData: BleedData
    let nonJointData: BleedData

    enum CodingKeys: CodingKey {
        case header
        case bodyMap
    }

    init(from decoder: Decoder) throws {
        let container = try decoder.container(keyedBy: CodingKeys.self)
        header = try container.decode(Header.self, forKey: .header)
        bodyMap = try container.decode([BleedData].self, forKey: .bodyMap)

        guard let jointData = bodyMap.first(where: { $0.bleedType == .joints }) else {
            throw DecodingError.dataCorruptedError(
                forKey: .bodyMap,
                in: container,
                debugDescription: "No BleedData with bleedType '.joints' found in bodyMap."
            )
        }
        self.jointData = jointData

        guard let nonJointData = bodyMap.first(where: { $0.bleedType == .nonJoints }) else {
            throw DecodingError.dataCorruptedError(
                forKey: .bodyMap,
                in: container,
                debugDescription: "No BleedData with bleedType '.nonJoints' found in bodyMap."
            )
        }
        self.nonJointData = nonJointData
    }

}

extension HemoJournalWidgetConfig {
    struct Header: Codable, Hashable, Equatable {
        let title: String
        let icon: String
    }

    struct BleedData: Codable, Equatable, Hashable, Identifiable {
        var id = UUID()
        let bleedType: BleedType
        let title: String
        let description: String
        let locations: [BodyLocation]

        enum CodingKeys: CodingKey {
            case bleedType
            case title
            case description
            case locations
        }
    }

    struct BodyLocation: Codable, Hashable, Equatable, Identifiable {
        var id = UUID()
        let location: BodyLocationType
        let points: [BodyPartData]

        enum CodingKeys: CodingKey {
            case location
            case points
        }
    }

    struct BodyPartData: Codable, Hashable, Equatable, Identifiable {
        var id = UUID()
        let name: String
        let value: BodyPartType

        enum CodingKeys: CodingKey {
            case name
            case value
        }
    }
}

enum BleedType: String, Codable, Equatable, Hashable {
    case joints = "JOINTS"
    case nonJoints = "NON_JOINTS"
}

enum BodyPartType: String, Codable, CaseIterable {
    case jointRightWrist = "JOINT_RIGHT_WRIST"
    case muscleRightArm = "MUSCLE_RIGHT_ARM"
    case surfaceRightArm = "SURFACE_RIGHT_ARM"
    case jointLeftWrist = "JOINT_LEFT_WRIST"
    case muscleLeftArm = "MUSCLE_LEFT_ARM"
    case surfaceLeftArm = "SURFACE_LEFT_ARM"
    case jointRightElbow = "JOINT_RIGHT_ELBOW"
    case jointLeftElbow = "JOINT_LEFT_ELBOW"
    case jointRightShoulder = "JOINT_RIGHT_SHOULDER"
    case jointLeftShoulder = "JOINT_LEFT_SHOULDER"
    case jointRightHip = "JOINT_RIGHT_HIP"
    case jointLeftHip = "JOINT_LEFT_HIP"
    case muscleRightLeg = "MUSCLE_RIGHT_LEG"
    case muscleLeftLeg = "MUSCLE_LEFT_LEG"
    case surfaceRightLeg = "SURFACE_RIGHT_LEG"
    case surfaceLeftLeg = "SURFACE_LEFT_LEG"
    case jointRightKnee = "JOINT_RIGHT_KNEE"
    case jointLeftKnee = "JOINT_LEFT_KNEE"
    case jointRightAnkle = "JOINT_RIGHT_ANKLE"
    case jointLeftAnkle = "JOINT_LEFT_ANKLE"
    case gumsBleed = "GUMS_BLEED"
    case noseBleed = "NOSE_BLEED"
    case menstrual = "MENSTRUAL"
    case bloodInStool = "BLOOD_IN_STOOL"
    case bloodInUrine = "BLOOD_IN_URINE"
    case custom = "CUSTOM"

    var description: String {
        switch self {
        case .jointRightWrist:
            return "Wrist (right)"
        case .muscleRightArm:
            return "Muscle Arm (right)"
        case .surfaceRightArm:
            return "Surface Arm (right)"
        case .jointLeftWrist:
            return "Wrist (left)"
        case .muscleLeftArm:
            return "Muscle Arm (left)"
        case .surfaceLeftArm:
            return "Surface Arm (left)"
        case .jointRightElbow:
            return "Elbow (right)"
        case .jointLeftElbow:
            return "Elbow (left)"
        case .jointRightShoulder:
            return "Shoulder (right)"
        case .jointLeftShoulder:
            return "Shoulder (left)"
        case .jointRightHip:
            return "Hip (right)"
        case .jointLeftHip:
            return "Hip (left)"
        case .muscleRightLeg:
            return "Muscle Leg (right)"
        case .muscleLeftLeg:
            return "Muscle Leg (left)"
        case .surfaceRightLeg:
            return "Surface Leg (right)"
        case .surfaceLeftLeg:
            return "Surface Leg (left)"
        case .jointRightKnee:
            return "Knee (right)"
        case .jointLeftKnee:
            return "Knee (left)"
        case .jointRightAnkle:
            return "Ankel (right)"
        case .jointLeftAnkle:
            return "Ankel (left)"
        case .gumsBleed:
            return "Gum Bleed"
        case .noseBleed:
            return "Nose Bleed"
        case .menstrual:
            return "Manstural"
        case .bloodInStool:
            return "Blood In Stool"
        case .bloodInUrine:
            return "Blood In Urine"
        case .custom:
            return "Custom"
        }
    }
}

enum BodyLocationType: String, Codable, CaseIterable {
    case rightWrist = "RIGHT_WRIST"
    case leftWrist = "LEFT_WRIST"
    case rightElbow = "RIGHT_ELBOW"
    case leftElbow = "LEFT_ELBOW"
    case rightShoulder = "RIGHT_SHOULDER"
    case leftShoulder = "LEFT_SHOULDER"
    case rightHip = "RIGHT_HIP"
    case leftHip = "LEFT_HIP"
    case rightKnee = "RIGHT_KNEE"
    case leftKnee = "LEFT_KNEE"
    case rightAnkle = "RIGHT_ANKLE"
    case leftAnkle = "LEFT_ANKLE"
    case other = "OTHER"

    var title: String {
        switch self {
        case .rightWrist: return "Right wrist"
        case .leftWrist: return "Left wrist"
        case .rightElbow: return "Right elbow"
        case .leftElbow: return "Left elbow"
        case .rightShoulder: return "Right shoulder"
        case .leftShoulder: return "Left shoulder"
        case .rightHip: return "Right hip"
        case .leftHip: return "Left hip"
        case .rightKnee: return "Right knee"
        case .leftKnee: return "Left knee"
        case .rightAnkle: return "Right ankle"
        case .leftAnkle: return "Left ankle"
        case .other: return "Other"
        }
    }
}
