//
//  HemoJournalWidget.swift
//  HemoPlugin
//
//  Created by <PERSON> on 17/03/2025.
//  Copyright © 2025 Huma Therapeutics Ltd. All rights reserved.
//

import UIKit
import SwiftUI
import HumaFoundation
import HumaWidgetKit

public final class HemoJournalWidget: AnyWidget {

    // MARK: - Public Properties
    public static var discoveryData: WidgetDiscoveryData? {
        .idPrefix("com.pfizer.widget.hemophilia_journal")
    }

    public let info: WidgetConfig.Info
    public var renderMode: WidgetRenderMode { .singlePage(buildFlow()) }

    // MARK: - Events

    public var onStartFlow: TriggeredEvent<AnyCoordinator>? = TriggeredEvent<AnyCoordinator>()
    @Triggered
    public var onUpdate: Event<Int>
    @Triggered
    public var onResize: Event<VoidCompletion>

    // MARK: - Private Properties

    private let resolver: Resolver
    private let navigator: AnyNavigator
    private var config: WidgetConfig
    private var widgetConfig: <PERSON><PERSON><PERSON><PERSON><PERSON>WidgetConfig
    private let onboardingManager: AnyLateOnboardingManager
    private weak var coordinator: HemoJournalWidgetCoordinator?
    public var snackBarAnchorView: UIView? { navigator.controller.topViewController?.view }

    // MARK: - Init

    public init(context: WidgetInitContext) throws {
        self.config = context.config
        self.widgetConfig = try context.config.decodeBody().orThrow()
        self.resolver = context.resolver
        self.onboardingManager = context.resolver.resolve()
        self.info = context.config.info
        self.navigator = context.navigator
    }

    public func refreshWidget(force: Bool) { }

    public func handleConfigDidChange(config: WidgetConfig) {
        guard let newConfig: HemoJournalWidgetConfig = try? config.decodeBody(),
              newConfig != widgetConfig else {
            return
        }
        self.config = config
        widgetConfig = newConfig
    }
}

private extension HemoJournalWidget {
    func buildFlow() -> AnyCoordinator {
        if let coordinator {
            return coordinator
        } else {
            let coordinator = HemoJournalWidgetCoordinator(
                navigator: navigator,
                resolver: resolver,
                widgetConfig: config
            )
            self.coordinator = coordinator
            return coordinator
        }
    }
}
