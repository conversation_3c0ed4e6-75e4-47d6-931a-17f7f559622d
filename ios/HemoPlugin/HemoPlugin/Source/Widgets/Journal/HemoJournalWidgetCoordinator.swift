//
//  HemoJournalWidgetCoordinator.swift
//  HemoPlugin
//
//  Created by <PERSON> on 17/03/2025.
//  Copyright © 2025 Huma Therapeutics Ltd. All rights reserved.
//

import HumaFoundation
import SwiftUI

final class HemoJournalWidgetCoordinator: BaseCoordinator {

    private let resolver: Resolver
    private let widgetConfig: WidgetConfig
    private var config: HemoJournalWidgetConfig? {
        try? widgetConfig.decodeBody()
    }

    public init(
        navigator: AnyNavigator,
        resolver: Resolver,
        widgetConfig: WidgetConfig
    ) {
        self.resolver = resolver
        self.widgetConfig = widgetConfig
        super.init(navigator: navigator)
    }

    final override func start(animated: Bool) -> Completable? {
        guard let controller = makeController() else { return nil }
        push(controller)
        return controller
    }
}

private extension HemoJournalWidgetCoordinator {
    func makeController() -> UIViewController? {
        let configurationRepository: AnyDeploymentConfigurationRepository = resolver.resolve()
        guard let config = self.config, let deploymentConfiguration = configurationRepository.configuration else { return nil }

        let model = HemoJournalCardViewModel(
            widgetConfigInfo: widgetConfig.info,
            config: config,
            repository: HemoJournalRepository(networking: resolver.resolve()),
            fileRepository: resolver.resolve(),
            medicationRepository: MedicaitionRepository(networking: resolver.resolve()),
            moduleResultSubmitRepository: resolver.resolve(),
            moduleResultRepository: resolver.resolve(),
            deploymentConfig: deploymentConfiguration,
            userRepository: resolver.resolve()
        )

        let view = HemoJournalCardView(viewModel: model)
        let container = UIHostingController(rootView: view)
        let controller = ViewController()
        controller.navigationBarPreference = .hidden
        controller.addFullscreenChild(container)
        return controller
    }
}

extension ViewController {
    func addFullscreenChild(_ child: UIViewController) {
        addChild(child)
        view.addSubview(child.view)
        child.view.translatesAutoresizingMaskIntoConstraints = false
        NSLayoutConstraint.activateFullLayoutConstraint(view: child.view)
        child.didMove(toParent: self)
    }
}
