//
// DetailsViewModel.swift
// HemoPlugin

// Created by <PERSON> on 01/05/2025.
// Copyright © 2025 Huma Therapeutics Ltd. All rights reserved.
//

import SwiftUI
import HumaFoundation

protocol AnyDetailsViewModel: ObservableObject {
    associatedtype BodyMapVM: AnyBodyMapViewModel

    var moduleConfig: ModuleConfig? { get }
    var bodyMapViewModel: BodyMapVM { get }
    var navigateToBleedLog: Bool { get set }
    var navigateToBleedHistory: Bool { get set }
    var moduleResults: [ModuleResult<HemoJournalPrimitive>] { get set }
    var fileRepository: AnyFileRepository { get }

}

final class DetailsViewModel: AnyDetailsViewModel {

    typealias BodyMapVM = BodyMapViewModel

    @Published var moduleConfig: ModuleConfig?
    @Published var bodyMapViewModel: BodyMapVM
    @Published var navigateToBleedLog: Bool = false
    @Published var navigateToBleedHistory: Bool = false
    @Published var moduleResults: [ModuleResult<HemoJournalPrimitive>] = []

    var medicationRepository: AnyMedicaitionRepository
    var moduleResultRepository: AnyModuleResultObserveRepository
    var deploymentConfig: AnyConfiguration
    var userRepository: AnyUserObserveRepository
    var fileRepository: AnyFileRepository

    init(
        bodyMapViewModel: BodyMapVM,
        medicationRepository: AnyMedicaitionRepository,
        moduleResultRepository: AnyModuleResultObserveRepository,
        deploymentConfig: AnyConfiguration,
        userRepository: AnyUserObserveRepository,
        fileRepository: AnyFileRepository
    ) {
        self.medicationRepository = medicationRepository
        self.moduleResultRepository = moduleResultRepository
        self.userRepository = userRepository
        self.fileRepository = fileRepository
        self.deploymentConfig = deploymentConfig
        self.bodyMapViewModel = bodyMapViewModel
        self.moduleConfig = deploymentConfig.moduleConfig(for: "HemophiliaJournal")?.config

        loadData()
    }
}

private extension DetailsViewModel {
    func loadData() {
        guard let inputConfig = self.moduleConfig?.inputConfig else { return }
        moduleResultRepository
            .getModuleResults(
                moduleID: inputConfig.moduleID,
                moduleConfigID: inputConfig.moduleConfigID,
                primitiveType: HemoJournalPrimitive.self
            ) { result in
            switch result {
            case .success(let moduleResult):
                self.moduleResults = moduleResult
            case .failure(let error):
                print(error)
            }
        }
    }
}
