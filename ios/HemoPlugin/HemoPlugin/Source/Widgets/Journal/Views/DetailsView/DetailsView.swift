//
//  DetailsView.swift
//  HemoPlugin
//
//  Created by <PERSON> on 21/02/2025.
//  Copyright © 2025 Huma Therapeutics Ltd. All rights reserved.
//

import SwiftUI

struct DetailsView<ViewModel: AnyDetailsViewModel>: View {
    // MARK: - Properties
    @StateObject var viewModel: ViewModel
    @State var selectedBleedLog: ModuleResult<HemoJournalPrimitive>?

    // MARK: - Body
    var body: some View {
        ZStack(alignment: .center) {
            // Navigation links
            navigationLinks

            // Main content
            mainContent
        }
        .navigationBarBackButtonHidden(true)
        .navigationBarHidden(true)
    }

    // MARK: - Main Content
    private var mainContent: some View {

        VStack(alignment: .center, spacing: Dimensions.spacing) {
            // Header
            HeaderView(title: "Bleed log", style: .stacked)
            ScrollView(.vertical, showsIndicators: false) {
                // Body map
                BodyMapView(viewModel: viewModel.bodyMapViewModel)
                    .frame(minHeight: 400)

                // Bleed Histroy
                moduleResultView

                // Show bleed history
                Button("Show bleed history") {
                    viewModel.navigateToBleedHistory = true
                }
                .pillStyle(.large, fullWidth: true)
                .padding(.bottom, Dimensions.spacing)

                // About view
                aboutView
            }
        }
        .padding(.vertical, Dimensions.verticalPadding)
        .padding(.horizontal, Dimensions.horizontalPadding)
    }
}

// MARK: - Private Views
private extension DetailsView {
    var moduleResultView: some View {
        let moduleResults = viewModel.moduleResults.prefix(3)
        let bleedItems = moduleResults.map {
            BleedItem(
                id: $0.id.rawValue,
                title: $0.primitive.bodyPartInjury.description,
                date: $0.startDateTime,
                description: $0.primitive.extraData.note
            )
        }

        return ForEach(bleedItems, id: \.id) { item in
            BleedHistoryRow(item: item) { bleedItem in
                if let primitive = moduleResults.first(where: { $0.id.rawValue == bleedItem.id }) {
                    selectedBleedLog = primitive
                    viewModel.navigateToBleedLog = true
                }
            }
        }
    }

    var aboutView: some View {
        VStack(alignment: .leading, spacing: Dimensions.spacing) {
            Text(HumaStrings.detailSectionAbout)
                .font(.bold)
                .foregroundColor(.charcoalGrey)

            if let about = viewModel.moduleConfig?.about {
                Text(about)
                    .font(.default)
                    .foregroundColor(.charcoalGrey)
            }
        }
    }
}

// MARK: - Navigation
private extension DetailsView {
    var navigationLinks: some View {
        VStack {
            if let seletedBleedLog = self.selectedBleedLog {
                NavigationLink("", isActive: $viewModel.navigateToBleedLog) {
                    BleedDetailsView(viewModel: .init(result: seletedBleedLog, fileRepository: viewModel.fileRepository))
                }
            }

            NavigationLink("", isActive: $viewModel.navigateToBleedHistory) {
                BleedHistoryListView(
                    viewModel: .init(items: viewModel.moduleResults, fileRepository: viewModel.fileRepository)
                )
            }
        }
    }
}
