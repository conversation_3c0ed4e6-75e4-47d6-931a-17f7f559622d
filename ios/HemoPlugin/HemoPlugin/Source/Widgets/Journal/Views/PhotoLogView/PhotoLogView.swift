//
// PhotoLogView.swift
// HemoPlugin

// Created by <PERSON> on 01/05/2025.
// Copyright © 2025 Huma Therapeutics Ltd. All rights reserved.
//

import SwiftUI

// MARK: - View
struct PhotoLogView: View {
    @ObservedObject var viewModel: PhotoLogViewModel

    // MARK: - Body
    var body: some View {
        VStack(alignment: .center, spacing: Dimensions.spacing) {
            // Header
            HeaderView(title: "Photo", style: .stacked)

            // Photos scroll view
            photosView
        }
        .onAppear(perform: {
            Task {
                await viewModel.loadPhotos()
            }
        })
        .padding(.horizontal, Dimensions.horizontalPadding)
        .navigationBarBackButtonHidden(true)
        .navigationBarHidden(true)
    }
}

private extension PhotoLogView {
    var photosView: some View {
        ScrollView(showsIndicators: false) {
            ForEach(viewModel.photosUrls, id: \.absoluteString ) { url in
                HumaSUIImageView(content: .link(url), radius: .value(Dimensions.cornerRadius), placeholder: .none)
                    .frame(maxWidth: .infinity)
            }
        }
    }
}
