//
// PhotoLogViewModel.swift
// HemoPlugin

// Created by <PERSON> on 01/05/2025.
// Copyright © 2025 Huma Therapeutics Ltd. All rights reserved.
//

import HumaFoundation

// MARK: - ViewModel
class PhotoLogViewModel: ObservableObject {
    @Published private(set) var photosUrls: [URL] = []

    let photos: [String]
    let fileRepository: AnyFileRepository

    init(photos: [String], fileRepository: AnyFileRepository) {
        self.photos = photos
        self.fileRepository = fileRepository
    }

    func loadPhotos() async {
        // Create a temporary array to collect URLs
        var tempUrls: [URL] = []
        
        for photoID in photos {
            do {
                let photoUrl = try await fileRepository.generateURLAsync(fileID: photoID)
                tempUrls.append(photoUrl)
            } catch {
                print("Error loading photo: \(error)")
            }
        }
        
        // Update the published property on the main thread
        await MainActor.run {
            self.photosUrls = tempUrls
        }
    }
}

extension AnyFileRepository {
    func generateURLAsync(fileID: String) async throws -> URL {
        try await withCheckedThrowingContinuation { continuation in
            generateURL(forFileID: fileID) { result in
                switch result {
                case .success(let url):
                    continuation.resume(returning: url)
                case .failure(let error):
                    continuation.resume(throwing: error)
                }
            }
        }
    }
}
