//
//  BodyMapView.swift
//  HemoPlugin
//
//  Created by <PERSON> on 17/03/2025.
//  Copyright © 2025 Huma Therapeutics Ltd. All rights reserved.
//

import SwiftUI

struct BodyMapView<ViewModel: AnyBodyMapViewModel>: View {
    @ObservedObject var viewModel: ViewModel
    @State var showSheet: Bool? = false

    var body: some View {
        VStack(spacing: 8) {
            GeometryReader { geometry in
                HStack {
                    Spacer()
                    bodyMapSideView(initial: "R", geometry: geometry)
                    bodyMapImageView(geometry: geometry)
                    bodyMapSideView(initial: "L", geometry: geometry)
                    Spacer()
                }
            }

            if let legend = viewModel.legend {
                LegendViewList(legend: legend)
            }

            Text(viewModel.legendText)
                .font(.xxSmall)
        }
        .frame(minHeight: 200)
    }

    // MARK: - Helper Views

    // CircularInitialView for Left/Right sides
    private func bodyMapSideView(initial: String, geometry: GeometryProxy) -> some View {
        CircularInitialView(initial: initial)
            .padding()
            .padding(.bottom, geometry.safeAreaInsets.bottom + 50)
            .aligned(to: .bottom)
    }

    // BodyMap Image View with Overlays
    private func bodyMapImageView(geometry: GeometryProxy) -> some View {
        Image("bodymap", bundle: HemoPluginBundle.bundle)
            .resizable()
            .scaledToFit()
            .overlay(
                GeometryReader { imageGeometry in
                    ForEach(viewModel.jointData.locations, id: \.id) { point in
                        bodyMapPointButton(bodyLocation: point, imageGeometry: imageGeometry)
                    }
                }
            )
    }

    // Button for each body map point
    private func bodyMapPointButton(bodyLocation: HemoJournalWidgetConfig.BodyLocation, imageGeometry: GeometryProxy) -> some View {
        Button(action: {
            viewModel.onBodyPointSelectd(bodyLocation: bodyLocation)
            showSheet = true
        }) {
            ZStack {
                Circle()
                    .fill(viewModel.getColorForLocation(bodyLocation))
                    .overlay(
                        Circle()
                            .stroke(Color.white, lineWidth: 2)
                            .padding(2)
                    )
                    .frame(square: 20)

                // Checkmark if selected
                if let selectedBodyPoint = viewModel.selectedBodyPoint, selectedBodyPoint.id == bodyLocation.id {
                    Image("tick-charcoal", bundle: HemoPluginBundle.bundle)
                }
            }
        }
        .position(
            x: bodyLocation.location.position.x * imageGeometry.size.width,
            y: bodyLocation.location.position.y * imageGeometry.size.height
        )
        .disabled(!viewModel.allowInteraction)
    }
}

// MARK: - Legend View List
struct LegendViewList: View {
    var legend: [LegendItem]

    var body: some View {
        HStack {
            ForEach(legend) { item in
                HStack {
                    Circle()
                        .fill(Color(hex: item.color))
                        .frame(width: 8, height: 8)
                    Text(item.label)
                        .font(.xxSmall)
                }
            }
        }
        .padding(.horizontal)
    }
}

// MARK: - Preview

#Preview {
    BodyMapView(viewModel: PreviewModels.bodyMapVM)
}
