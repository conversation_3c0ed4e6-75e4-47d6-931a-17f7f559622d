//
//  HemoProfileQuestionnaireSource.swift
//  HemoPlugin
//
//  Created by <PERSON> on 17/03/2025.
//  Copyright © 2025 Huma Therapeutics Ltd. All rights reserved.
//

import Foundation
import UIKit
import Combine
import HumaFoundation

class HemoProfileQuestionnaireSource: AnyQuestionSource {
    private let repository: AnyMedicaitionRepository
    private var cmsMedications: [CMSMedicationResponse.MedicationItem] = []
    var didSubmitQuestionnaire: TriggeredEvent<Void> = .init()

    init(repository: AnyMedicaitionRepository) {
        self.repository = repository
        getMedications()
    }

    // Add enum for Question IDs
    enum QuestionID: String {
        case weight = "HEMOPHILIA_WEIGHT_ID"
        case hemophiliaType = "HEMOPHILIA_TYPE_ID"
        case hemophiliaSeverity = "HEMOPHILIA_SEVERITY_ID"
        case inhibitorHistory = "HEMOPHILIA_ACTIVE_ANTIBODY_ID"
        case bleedCount = "HEMOPHILIA_BLEEDS_COUNT_ID"
        case treatedBleedCount = "HEMOPHILIA_TREATMENT_COUNT_ID"
        case targetJoints = "HEMOPHILIA_TARGET_JOINTS_CONDITION_ID"
        case targetJointLocations = "HEMOPHILIA_TARGET_JOINTS_ID"
        case onProphylacticTreatment = "HEMOPHILIA_PROPHYLACTIC_CONDITION_ID"
        case prophylacticTreatment = "HEMOPHILIA_PROPHYLACTIC_ID"
        case dosage = "HEMOPHILIA_PROPHYLACTIC_DOSAGE_ID"
        case medicationFrequency = "HEMOPHILIA_PROPHYLACTIC_FREQUENCY_ID"
        case timeOfDay = "HEMOPHILIA_PROPHYLACTIC_TIME_OF_DAY_ID"
        case setReminder = "HEMOPHILIA_PROPHYLACTIC_REMINDER_ID"
        case nextDose = "HEMOPHILIA_PROPHYLACTIC_NEXT_DOSAGE_ID"
        case factorTreatment = "HEMOPHILIA_PROPHYLACTIC_DAYS_OF_WEEK_ID"
        case diagnoses = "HEMOPHILIA_DIAGNOSED_ID"
        case specificDays = "HEMOPHILIA_ON_DEMAND_ID"
        case dayInterval = "HEMOPHILIA_PROPHYLACTIC_DAYS_INTERVAL_ID"
    }

    func loadQuestions() -> [BaseQuestion] {
        return [
            BaseQuestion(
                id: QuestionID.weight.rawValue,
                title: "What is your weight?",
                subtitle: nil,
                placeholder: "Enter your weight",
                accessoryString: "lb", // TODO: - Pass correct unit from user preference
                type: .numeric,
                isMandatory: true,
                validationRules: [
                    .required(message: "Please enter your weight"),
                    .numeric(message: "Weight must be a valid number")
                ],
                nextQuestionID: QuestionID.hemophiliaType.rawValue,
                branchingLogic: nil
            ),
            BaseQuestion(
                id: QuestionID.hemophiliaType.rawValue,
                title: "Please indicate the type of your Hemophilia",
                subtitle: nil,
                placeholder: nil,
                accessoryString: nil,
                type: .singleChoice,
                isMandatory: true,
                validationRules: [.required(message: "Please select your hemophilia type")],
                nextQuestionID: QuestionID.hemophiliaSeverity.rawValue,
                branchingLogic: nil
            ),
            BaseQuestion(
                id: QuestionID.hemophiliaSeverity.rawValue,
                title: "Please indicate the severity of your Hemophilia",
                subtitle: "Baseline clotting factor activity (factor VIII(8) or factor IX (9) in blood) is used to determine hemophilia severity.<br><strong>Mild:</strong> Greater than 5% but less than 40%<br><strong>Moderate:</strong> 1% to 5%<br><strong>Severe:</strong> Less than 1%",
                placeholder: nil,
                accessoryString: nil,
                type: .singleChoice,
                isMandatory: true,
                validationRules: [.required(message: "Please select your hemophilia severity")],
                nextQuestionID: QuestionID.inhibitorHistory.rawValue,
                branchingLogic: nil
            ),
            BaseQuestion(
                id: QuestionID.inhibitorHistory.rawValue,
                title: "Do you have an inhibitor or a history of an inhibitor?",
                subtitle: "An inhibitor in hemophilia is when the body’s immune system blocks the replacement clotting factor, making it harder to control bleeding.",
                placeholder: nil,
                accessoryString: nil,
                type: .singleChoice,
                isMandatory: true,
                validationRules: [.required(message: "Please select an option about your inhibitor history")],
                nextQuestionID: QuestionID.bleedCount.rawValue,
                branchingLogic: nil
            ),
            BaseQuestion(
                id: QuestionID.bleedCount.rawValue,
                title: "How many bleeds have you had in the past 6 months?",
                subtitle: nil,
                placeholder: "Enter number of bleeds",
                accessoryString: nil,
                type: .numeric,
                isMandatory: false,
                validationRules: [
                    .required(message: "Please enter the number of bleeds"),
                    .numeric(message: "Number of bleeds must be a valid number")
                ],
                nextQuestionID: QuestionID.treatedBleedCount.rawValue,
                branchingLogic: nil
            ),
            BaseQuestion(
                id: QuestionID.treatedBleedCount.rawValue,
                title: "How many of those bleeds were treated?",
                subtitle: nil,
                placeholder: "Enter number of treated bleeds",
                accessoryString: nil,
                type: .numeric,
                isMandatory: false,
                validationRules: [
                    .required(message: "Please enter the number of treated bleeds"),
                    .numeric(message: "Number of treated bleeds must be a valid number")
                ],
                nextQuestionID: QuestionID.targetJoints.rawValue,
                branchingLogic: nil
            ),
            BaseQuestion(
                id: QuestionID.targetJoints.rawValue,
                title: "Do you have target joints?",
                subtitle: "A joint is considered a target joint if it experiences bleeding 3 or more times within a six-month period.",
                placeholder: nil,
                accessoryString: nil,
                type: .booleanChoice,
                isMandatory: true,
                validationRules: [.required(message: "Please select whether you have target joints")],
                nextQuestionID: nil,
                branchingLogic: ["YES": QuestionID.targetJointLocations.rawValue, "NO": QuestionID.onProphylacticTreatment.rawValue]
            ),
            BaseQuestion(
                id: QuestionID.targetJointLocations.rawValue,
                title: "Which are your target joints?",
                subtitle: "Please select all that apply",
                placeholder: nil,
                accessoryString: nil,
                type: .multipleChoice(.default),
                isMandatory: true,
                validationRules: [.required(message: "Please select at least one target joint")],
                nextQuestionID: QuestionID.onProphylacticTreatment.rawValue,
                branchingLogic: nil
            ),
            BaseQuestion(
                id: QuestionID.onProphylacticTreatment.rawValue,
                title: "Are you on prophylactic treatment?",
                subtitle: nil,
                placeholder: nil,
                accessoryString: nil,
                type: .booleanChoice,
                isMandatory: true,
                validationRules: [.required(message: "Please select whether you are on prophylactic treatment")],
                nextQuestionID: nil,
                branchingLogic: ["YES": QuestionID.prophylacticTreatment.rawValue, "NO": QuestionID.factorTreatment.rawValue]
            ),
            BaseQuestion(
                id: QuestionID.prophylacticTreatment.rawValue,
                title: "Please indicate your current prophylactic treatment for hemophilia.",
                subtitle: nil,
                placeholder: nil,
                accessoryString: nil,
                type: .autocompleteSearch,
                isMandatory: true,
                validationRules: [.required(message: "Please select your current prophylactic treatment")],
                nextQuestionID: QuestionID.dosage.rawValue,
                branchingLogic: nil
            ),
            BaseQuestion(
                id: QuestionID.dosage.rawValue,
                title: "Please enter your dosage",
                subtitle: nil,
                placeholder: nil,
                accessoryString: nil,
                type: .valueUnit,
                isMandatory: true,
                validationRules: [.required(), .numeric()],
                nextQuestionID: QuestionID.medicationFrequency.rawValue,
                branchingLogic: nil
            ),
            BaseQuestion(
                id: QuestionID.medicationFrequency.rawValue,
                title: "How often do you take your medication?",
                subtitle: nil,
                placeholder: nil,
                accessoryString: nil,
                type: .singleChoice,
                isMandatory: true,
                validationRules: [.required()],
                nextQuestionID: nil,
                branchingLogic: [
                    "EVERY_DAY": QuestionID.timeOfDay.rawValue,
                    "SPECIFIC_DAYS": QuestionID.specificDays.rawValue,
                    "DAYS_INTERVAL": QuestionID.dayInterval.rawValue,
                    "AS_NEEDED": QuestionID.setReminder.rawValue
                ]
            ),
            BaseQuestion(
                id: QuestionID.timeOfDay.rawValue,
                title: "Time of day",
                subtitle: nil,
                placeholder: nil,
                accessoryString: nil,
                type: .time,
                isMandatory: true,
                validationRules: [.required()],
                nextQuestionID: QuestionID.setReminder.rawValue,
                branchingLogic: nil
            ),
            BaseQuestion(
                id: QuestionID.setReminder.rawValue,
                title: "Do you want to set a reminder for this medication?",
                subtitle: nil,
                placeholder: nil,
                accessoryString: nil,
                type: .booleanChoice,
                isMandatory: true,
                validationRules: [.required()],
                nextQuestionID: QuestionID.nextDose.rawValue,
                branchingLogic: nil
            ),
            BaseQuestion(
                id: QuestionID.nextDose.rawValue,
                title: "When is your next dose?",
                subtitle: nil,
                placeholder: nil,
                accessoryString: nil,
                type: .date(range: Date()...Date.distantFuture),
                isMandatory: true,
                validationRules: [.required()],
                nextQuestionID: QuestionID.factorTreatment.rawValue,
                branchingLogic: nil
            ),
            BaseQuestion(
                id: QuestionID.factorTreatment.rawValue,
                title: "What is your factor treatment for on-demand/breakthrough bleeds?",
                subtitle: nil,
                placeholder: nil,
                accessoryString: nil,
                type: .autocompleteSearch,
                isMandatory: true,
                validationRules: [.required()],
                nextQuestionID: QuestionID.diagnoses.rawValue,
                branchingLogic: nil
            ),
            BaseQuestion(
                id: QuestionID.diagnoses.rawValue,
                title: "Have you been diagnosed with any of the following?",
                subtitle: "Please select all that apply",
                placeholder: nil,
                accessoryString: nil,
                type: .multipleChoice(.default),
                isMandatory: true,
                validationRules: [.required()],
                nextQuestionID: nil, // End of questionnaire
                branchingLogic: nil
            ),
            BaseQuestion(
                id: QuestionID.specificDays.rawValue,
                title: "Select specific days?",
                subtitle: "Please select all that apply",
                placeholder: nil,
                accessoryString: nil,
                type: .multipleChoice(.circle),
                isMandatory: true,
                validationRules: [.required()],
                nextQuestionID: QuestionID.timeOfDay.rawValue,
                branchingLogic: nil
            ),
            BaseQuestion(
                id: QuestionID.dayInterval.rawValue,
                title: "Day interval?",
                subtitle: "Please select an option",
                placeholder: "Choose intervals",
                accessoryString: nil,
                type: .picker,
                isMandatory: true,
                validationRules: [.required()],
                nextQuestionID: QuestionID.timeOfDay.rawValue,
                branchingLogic: nil
            )
        ]
    }

    func getOptions(for questionID: String) -> [OptionItem] {
        switch questionID {
        case QuestionID.hemophiliaType.rawValue:
            return HemophiliaType.allCases.map { .init(value: $0.rawValue, title: $0.title) }
        case QuestionID.hemophiliaSeverity.rawValue:
            return HemophiliaSeverity.allCases.map { .init(value: $0.rawValue, title: $0.title) }
        case QuestionID.inhibitorHistory.rawValue:
            return InhibitorHistory.allCases.map { .init(value: $0.rawValue, title: $0.title) }
        case QuestionID.targetJointLocations.rawValue:
            return BodyLocationType.allCases.map { locationType in
                .init(value: locationType.rawValue, title: locationType.title)
            }
        case QuestionID.medicationFrequency.rawValue:
            return MedicationFrequency.allCases.map { .init(value: $0.rawValue, title: $0.title) }
        case QuestionID.diagnoses.rawValue:
            return DiagnosisType.allCases.map { .init(value: $0.rawValue, title: $0.title) }
        case QuestionID.specificDays.rawValue:
            return [
                .init(value: WeekDay.monday.rawValue, title: HumaStrings.commonDaysMondayShort),
                .init(value: WeekDay.tuesday.rawValue, title: HumaStrings.commonDaysTuesdayShort),
                .init(value: WeekDay.wednesday.rawValue, title: HumaStrings.commonDaysWednesdayShort),
                .init(value: WeekDay.thursday.rawValue, title: HumaStrings.commonDaysThursdayShort),
                .init(value: WeekDay.friday.rawValue, title: HumaStrings.commonDaysFridayShort),
                .init(value: WeekDay.saturday.rawValue, title: HumaStrings.commonDaysSaturdayShort),
                .init(value: WeekDay.sunday.rawValue, title: HumaStrings.commonDaysSundayShort)
            ]
        case QuestionID.dayInterval.rawValue:
            return makeIntervals()
                .map {
                    .init(value: "\($0)_DAYS", title: $0)
                }
        case QuestionID.dosage.rawValue:
            return MedicationDosageV2.Unit.allCases.map {
                .init(value: $0.rawValue, title: $0.localizedDescription)
            }
        default: return []
        }
    }

    func searchAutocompleteOptions(for searchTerm: String, questionID: String) -> [CMSMedicationResponse.MedicationItem] {
        switch questionID {
        case QuestionID.prophylacticTreatment.rawValue:
            if searchTerm.isEmpty {
                return cmsMedications.filter {
                        $0.tags.contains("Prophylactic")
                    }
            } else {
                return cmsMedications.filter {
                    $0.title.lowercased().contains(searchTerm.lowercased())
                     && $0.tags.contains("Prophylactic")
                }
            }
        case QuestionID.factorTreatment.rawValue:
            if searchTerm.isEmpty {
                return cmsMedications.filter {
                        $0.tags.contains("OnDemand")
                    }
            } else {
                return cmsMedications.filter {
                    $0.title.lowercased().contains(searchTerm.lowercased())
                     && $0.tags.contains("OnDemand")
                }
            }
        default:
            return []
        }
    }

    func onSubmitQuestionnaire(answers: [String : Answer]) async throws -> CreateObjectResponse {
        let targetJoints = self.getAnswer(for: QuestionID.targetJointLocations.rawValue, answers: answers)?.value as? [OptionItem] ?? []
        let answerBodies = answers.compactMapValues { $0.answerBody }.map({ $0.value })
        let prophylacticMedication = getProphylacticMedication(for: answers)
        let onDemandMedication = getOnDemandMedication(for: answers)
        let requestBody = HemoProfileQuestionnaireRequest(
            answers: answerBodies,
            targetJoints: targetJoints.map(\.value),
            prophylacticMedication: prophylacticMedication,
            asNeededMedication: onDemandMedication
        )

        let response = try await repository.submitHemoProfileQuestionnaire(requestBody: requestBody)
        didSubmitQuestionnaire.trigger()
        return response
    }
}

// MARK: - Private Methods
private extension HemoProfileQuestionnaireSource {
    func getProphylacticMedication(for answers: [String : Answer]) -> MedicationV2? {
        guard let prophylacticTreatmentAnswer = getAnswer(for: QuestionID.prophylacticTreatment.rawValue, answers: answers),
              let cmsMedication = prophylacticTreatmentAnswer.value as? CMSMedicationResponse.MedicationItem,
              let dosageAnswer = getAnswer(for: QuestionID.dosage.rawValue, answers: answers),
              let dosage = dosageAnswer.value as? MedicationDosageV2,
              let frequencyAnswer = getAnswer(for: QuestionID.medicationFrequency.rawValue, answers: answers),
              let frequencyOption = frequencyAnswer.value as? OptionItem,
              let frequency = MedicationFrequency(rawValue: frequencyOption.value),
              let timeOfDayAnswer = getAnswer(for: QuestionID.timeOfDay.rawValue, answers: answers),
              let timeOfDay = timeOfDayAnswer.value as? [Date],
              let reminderAnswer = getAnswer(for: QuestionID.setReminder.rawValue, answers: answers),
              let reminder = reminderAnswer.value as? OptionItem else {
            return nil
        }

        let daysOfWeek = getAnswer(for: QuestionID.specificDays.rawValue, answers: answers)?.value as? [OptionItem] ?? []
        let daysIntervalOptionItem = getAnswer(for: QuestionID.dayInterval.rawValue, answers: answers)?.value as? OptionItem
        let daysInterval = daysIntervalOptionItem?.value ?? "1_DAY"
        let daysIntervalValue = daysInterval.components(separatedBy: "_").first ?? "1"

        let medicationSchedule = buildMedicationSchedule(
            frequency: frequency,
            timeOfDay: timeOfDay,
            daysOfWeek: daysOfWeek.compactMap({ WeekDay(rawValue: $0.value)}),
            daysInterval: Int(daysIntervalValue) ?? 1
        )

        return MedicationV2(
            name: cmsMedication.title,
            dosage: dosage,
            schedule: medicationSchedule,
            isNotificationEnabled: reminder.value == BoolQuestion.yes.rawValue,
            customUnit: nil,
            tags: ["Prophylactic"]
        )
    }

    func getOnDemandMedication(for answers: [String : Answer]) -> MedicationV2? {
        guard let prophylacticTreatmentAnswer = self.getAnswer(for: QuestionID.prophylacticTreatment.rawValue, answers: answers),
              let cmsMedication = prophylacticTreatmentAnswer.value as? CMSMedicationResponse.MedicationItem else {
            return nil
        }

        var dosage: MedicationDosageV2? = nil
        var customUnit: String? = nil
        if let cmsUnit = cmsMedication.unit, let cmsDosage = cmsMedication.dosage?.double {
            let medicationUnit: MedicationDosageV2.Unit = .init(rawValue: cmsUnit) ?? .custom
            dosage = MedicationDosageV2(value: cmsDosage, unit: medicationUnit)
            customUnit = medicationUnit == .custom ? cmsUnit : nil
        }

        return MedicationV2(
            name: cmsMedication.title,
            dosage: dosage,
            schedule: .asNeeded,
            isNotificationEnabled: false,
            customUnit: customUnit,
            tags: ["OnDemand"]
        )
    }

    func getAnswer(for questionID: String, answers: [String : Answer]) -> Answer? {
        answers.first(where: { $0.key == questionID })?.value
    }

    func getMedications() {
        Task {
            do {
                self.cmsMedications = try await repository.getCMSMedication().items
            } catch {
                print("### \(error.localizedDescription)")
                self.cmsMedications = []
            }
        }
    }

    func buildMedicationSchedule(frequency: MedicationFrequency, timeOfDay: [Date], daysOfWeek: [WeekDay], daysInterval: Int) -> MedicationScheduleV2 {
        var times: [ISODuration] { timeOfDay.map(ISODuration.timeDuration(from:)) }
        
        switch frequency {
        case .everyDay:
            return .everyDay(times: times)
        case .specificDays:
            return .specific(days: daysOfWeek, times: times)
        case .daysInterval:
            return .interval(duration: "P\(daysInterval)D", times: times)
        case .asNeeded:
            return .asNeeded
        }
    }

    func makeIntervals() -> [String] {
        (1...365).map(Strings.moduleMedicationDays)
    }
}

// MARK: - Enums 
extension HemoProfileQuestionnaireSource {
    enum MedicationFrequency: String, CaseIterable {
        case everyDay = "EVERY_DAY"
        case specificDays = "SPECIFIC_DAYS"
        case daysInterval = "DAYS_INTERVAL"
        case asNeeded = "AS_NEEDED"

        var title: String {
            switch self {
            case .everyDay: return HumaStrings.medicationv2EveryDay
            case .specificDays: return HumaStrings.medicationv2SpecificDay
            case .daysInterval: return HumaStrings.medicationv2DaysInterval
            case .asNeeded: return HumaStrings.medicationv2AsNeeded
            }
        }
    }

    enum DiagnosisType: String, CaseIterable {
        case arthropathy = "ARTHROPATHY"
        case chronicPain = "CHRONIC_PAIN"
        case hiv = "HIV"
        case hepatitisC = "HEPATITIS_C"
        case hepatitisB = "HEPATITIS_B"
        case jointReplacement = "PREVIOUS_JOINT_REPLACEMENT"
        case heartDisease = "HEART_DISEASE"
        case diabetes = "DIABETES"
        case other = "OTHER"
        case none = "NONE"

        var title: String {
            switch self {
            case .arthropathy: return "Arthropathy (joint swelling/stiffness)"
            case .chronicPain: return "Chronic pain"
            case .hiv: return "HIV"
            case .hepatitisC: return "Hepatitis C"
            case .hepatitisB: return "Hepatitis B"
            case .jointReplacement: return "Previous joint replacement"
            case .heartDisease: return "Heart disease"
            case .diabetes: return "Diabetes (Type 1 or 2)"
            case .other: return "Other"
            case .none: return "None"
            }
        }
    }

    enum HemophiliaType: String, CaseIterable {
        case typeA = "HEMOPHILIA_A"
        case typeB = "HEMOPHILIA_B"

        var title: String {
            switch self {
            case .typeA: return "Hemophilia A"
            case .typeB: return "Hemophilia B"
            }
        }
    }

    enum HemophiliaSeverity: String, CaseIterable {
        case mild = "MILD"
        case moderate = "MODERATE"
        case severe = "SEVERE"
        case notSure = "NOT_SURE"

        var title: String {
            switch self {
            case .mild: return "Mild"
            case .moderate: return "Moderate"
            case .severe: return "Severe"
            case .notSure: return "Not sure"
            }
        }
    }

    enum InhibitorHistory: String, CaseIterable {
        case never = "NEVER_HAD_AN_INHIBITOR"
        case past = "HAD_AN_INHIBITOR_IN_THE_PAST"
        case active = "HAVE_AN_ACTIVE_INHIBITOR"
        case notSure = "NOT_SURE"

        var title: String {
            switch self {
            case .never: return "Never had an inhibitor"
            case .past: return "Had an inhibitor in the past"
            case .active: return "Have an active inhibitor"
            case .notSure: return "Not sure"
            }
        }
    }

    enum BoolQuestion: String, CaseIterable {
        case yes = "YES"
        case no = "NO"

        var title: String {
            switch self {
            case .yes: return HumaStrings.commonActionYes
            case .no: return HumaStrings.commonActionNo
            }
        }
    }
}
