//
//  HemoJournalCardViewModel.swift
//  HemoPlugin
//
//  Created by <PERSON> on 21/02/2025.
//  Copyright © 2025 Huma Therapeutics Ltd. All rights reserved.
//

import Combine
import HumaFoundation

final class HemoJournalCardViewModel: AnyHemoJournalCardViewModel {

    // MARK: - Type Aliases
    typealias HeaderVM = HeaderViewModel
    typealias BodyMapVM = BodyMapViewModel
    typealias SetupProfileVM = SetupProfileCardViewModel
    typealias ActiveProfileVM = ActiveProfileCardViewModel

    // MARK: - Published Properties
    @Published private(set) var config: HemoJournalWidgetConfig
    @Published private(set) var content: ContentState<Void> = .loading
    @Published var navigateToQuestionnaire: Bool = false
    @Published var navigateToAddBleed: Bool = false
    @Published var navigateToProfileQuestionnaire: Bool = false
    @Published var navigateToDetails: Bool = false
    @Published var showSheet: Bool? = nil

    // MARK: - View Models
    var headerViewModel: HeaderVM?
    var bodyMapViewModel: BodyMapVM?
    var bodyMapViewModelEditable: BodyMapViewModel?
    var setupProfileViewModel: SetupProfileVM?
    var activeProfileViewModel: ActiveProfileCardViewModel?
    var addBleedVM: AddBleedViewModel?
    var detailsVM: DetailsViewModel?

    internal lazy var bleedTypeViewModel: ReusableListViewModel = makeBleedTypeViewModel()
    internal lazy var nonJointBleedViewModel: ReusableListViewModel = makeNonJointBleedViewModel()
    var reusableListViewModel: ReusableListViewModel {
        selectedBleedType?.bleedType == .nonJoints ? nonJointBleedViewModel : bleedTypeViewModel
    }
    var profileQuestionnaireSource: HemoProfileQuestionnaireSource { HemoProfileQuestionnaireSource(repository: medicationRepository) }
    var addBleedQuestionnaireSource: AddBleedQuestionnaireSource? { makeAddBleedQuestionnaireSource() }

    // MARK: - Properties
    var selectedBleedType: HemoJournalWidgetConfig.BleedData?
    var selectedNonJointBleed: HemoJournalWidgetConfig.BodyPartData?
    var selectedJointBleed: HemoJournalWidgetConfig.BodyPartData?
    var customBodyPart: String?

    // MARK: - Private Properties
    private var isLoadingData = false
    private let widgetConfigInfo: WidgetConfig.Info
    private let repository: AnyHemoJournalRepository
    private let fileRepository: AnyFileRepository
    private let medicationRepository: AnyMedicaitionRepository
    private let moduleResultSubmitRepository: AnyModuleResultSubmitRepository
    private let moduleResultRepository: AnyModuleResultObserveRepository
    private let deploymentConfig: AnyConfiguration
    private let userRepository: AnyUserObserveRepository

    // MARK: - Events
    var onTap: TriggeredEvent<Void> = .init()

    // MARK: - Initialization
    init(
        widgetConfigInfo: WidgetConfig.Info,
        config: HemoJournalWidgetConfig,
        repository: AnyHemoJournalRepository,
        fileRepository: AnyFileRepository,
        medicationRepository: AnyMedicaitionRepository,
        moduleResultSubmitRepository: AnyModuleResultSubmitRepository,
        moduleResultRepository: AnyModuleResultObserveRepository,
        deploymentConfig: AnyConfiguration,
        userRepository: AnyUserObserveRepository,
        content: ContentState<Void> = .loading
    ) {
        self.widgetConfigInfo = widgetConfigInfo
        self.config = config
        self.content = content
        self.repository = repository
        self.fileRepository = fileRepository
        self.medicationRepository = medicationRepository
        self.moduleResultSubmitRepository = moduleResultSubmitRepository
        self.moduleResultRepository = moduleResultRepository
        self.deploymentConfig = deploymentConfig
        self.userRepository = userRepository
    }

    // MARK: - Data Loading
    func loadData() async {
        guard !isLoadingData else { return }
        isLoadingData = true

        do {
            let newResponse = try await repository.getWidgetData(
                widgetType: widgetConfigInfo.type,
                widgetID: widgetConfigInfo.id
            )

            headerViewModel = HeaderViewModel(
                title: config.header.title,
                logoImageName: config.header.icon,
                fileRepository: fileRepository
            )

            switch newResponse.state {
            case .active:
                activeProfileViewModel = ActiveProfileCardViewModel(
                    primaryButtonTitle: newResponse.primaryCTAtext,
                    secondaryButtonTitle: newResponse.secondaryCTAtext
                )

                activeProfileViewModel?.onPrimaryButtonTapped.addObserver(self) { observer in
                    observer.navigateToProfileQuestionnaire = true
                }

                activeProfileViewModel?.onSecondaryButtonTapped.addObserver(self, using: { observer in
                    observer.navigateToDetails = true
                })
                setupProfileViewModel = nil
            case .setup:
                setupProfileViewModel = SetupProfileCardViewModel(
                    headline: newResponse.title,
                    bodyText: newResponse.description,
                    primaryButtonTitle: newResponse.primaryCTAtext
                )

                setupProfileViewModel?.onPrimaryButtonTapped.addObserver(self) {
                    if newResponse.state == .setup {
                        $0.navigateToProfileQuestionnaire = true
                    } else {
                        $0.navigateToAddBleed = true
                    }
                }
                activeProfileViewModel = nil
            }

            let bodyMapViewModel = BodyMapViewModel(
                bodyMapColor: newResponse.bodyMapColor ?? [],
                legend: newResponse.legend,
                jointData: config.jointData,
                legendText: "Data shown from the past 6 months",
                selectedBodyPoint: nil
            )
            self.bodyMapViewModel = bodyMapViewModel

            let bodyMapViewModelEditable = BodyMapViewModel(
                bodyMapColor: newResponse.bodyMapColor ?? [],
                legend: newResponse.legend,
                jointData: config.jointData,
                allowInteraction: true, 
                legendText: "Tap on a body part to add a bleed",
                selectedBodyPoint: nil
            )

            addBleedVM = .init(
                bodyMapViewModel: bodyMapViewModelEditable,
                medicationRepository: medicationRepository,
                moduleResultSubmitRepository: moduleResultSubmitRepository,
                deploymentConfig: deploymentConfig,
                userRepository: userRepository,
                fileRepository: fileRepository
            )

            addBleedVM?.onSubmitQuestionnaire.addObserver(self) { observer in
                observer.navigateToAddBleed = false
            }

            detailsVM = .init(
                bodyMapViewModel: bodyMapViewModel,
                medicationRepository: medicationRepository,
                moduleResultRepository: moduleResultRepository,
                deploymentConfig: deploymentConfig,
                userRepository: userRepository,
                fileRepository: fileRepository
            )


            Task { @MainActor in
                content = .results(())
                isLoadingData = false
            }
        } catch {
            Task { @MainActor in
                isLoadingData = false
                content = .empty
            }
        }
    }

}

private extension HemoJournalCardViewModel {
    func makeBleedTypeViewModel() -> ReusableListViewModel {
        let items: [ReusableListViewModel.Item] = config.bodyMap.map {
            .init(id: $0.id, title: $0.title, description: $0.description)
        }
        return .init(
            title: "Add new bleed",
            items: items,
            onItemSelected: { [weak self] item in
                self?.showSheet = false
                if let selectedItem = self?.config.bodyMap.first(where: { $0.id == item.id }) {
                    self?.selectedBleedType = selectedItem
                    switch selectedItem.bleedType {
                    case .joints:
                        self?.navigateToAddBleed = true
                    case .nonJoints:
                        executeOnMainThread(after: 0.2) { [weak self] in
                            self?.showSheet = true
                        }
                    }
                }
            }
        )
    }

    func makeNonJointBleedViewModel() -> ReusableListViewModel {
        let nonJointPoints = config.nonJointData.locations.first { $0.location == .other }?.points ?? []
        let items: [ReusableListViewModel.Item] = nonJointPoints.map ({
            .init(id: $0.id, title: $0.name, description: nil)
        })
        return .init(
            title: "Add non-joint bleed",
            items: items,
            onItemSelected: { [weak self] item in
                self?.showSheet = false
                if let selectedItem = nonJointPoints.first(where: { $0.id == item.id }) {
                    self?.selectedNonJointBleed = selectedItem
                    if selectedItem.value != .custom {
                        self?.navigateToQuestionnaire = true
                    } else {
                        executeOnMainThread(after: 0.2) { [weak self] in
                            self?.showSheet = true
                        }
                    }
                }
            }
        )
    }

    func makeAddBleedQuestionnaireSource() -> AddBleedQuestionnaireSource? {
        guard let selectedNonJointBleed = self.selectedNonJointBleed else { return nil }
        let source = AddBleedQuestionnaireSource(
            bodyLocation: .other,
            bodyPart: selectedNonJointBleed.value,
            repository: medicationRepository,
            moduleResultSubmitRepository: moduleResultSubmitRepository,
            deploymentConfiguration: deploymentConfig,
            userRepository: userRepository,
            fileRepository: fileRepository,
            customBodyPart: customBodyPart
        )

        source.didSubmitQuestionnaire.addObserver(self) { observer in
//            observer.onSubmitQuestionnaire.trigger()
            observer.selectedNonJointBleed = nil
        }

        return source
    }
}
