//
//  AnyHemoJournalCardViewModel.swift
//  HemoPlugin
//
//  Created by <PERSON> on 21/02/2025.
//  Copyright © 2025 Huma Therapeutics Ltd. All rights reserved.
//

import SwiftUI
import HumaFoundation

// MARK: - Interface

protocol AnyHemoJournalCardViewModel: ObservableObject {
    associatedtype HeaderVM: AnyHeaderViewModel
    associatedtype BodyMapVM: AnyBodyMapViewModel
    associatedtype SetupProfileVM: AnySetupProfileCardViewModel
    associatedtype ActiveProfileVM: AnyActiveProfileCardViewModel
    associatedtype DetailsVM: AnyDetailsViewModel

    // Navigation properties
    var navigateToQuestionnaire: Bool { get set }
    var navigateToAddBleed: Bool { get set }
    var navigateToProfileQuestionnaire: Bool { get set }
    var navigateToDetails: Bool { get set }

    // Configuration and content
    var config: HemoJournalWidgetConfig { get }
    var content: ContentState<Void> { get }
    var showSheet: Bool? { get set }

    // Events
    var onTap: TriggeredEvent<Void> { get }
    
    // View Models
    var headerViewModel: HeaderVM? { get }
    var bodyMapViewModel: BodyMapVM? { get }
    var bodyMapViewModelEditable: BodyMapVM? { get }
    var setupProfileViewModel: SetupProfileVM? { get }
    var activeProfileViewModel: ActiveProfileVM? { get }
    var reusableListViewModel: ReusableListViewModel { get }
    var profileQuestionnaireSource: HemoProfileQuestionnaireSource { get }
    var addBleedQuestionnaireSource: AddBleedQuestionnaireSource? { get }
    var addBleedVM: AddBleedViewModel? { get }
    var detailsVM: DetailsVM? { get }

    // Properties
    var selectedBleedType: HemoJournalWidgetConfig.BleedData? { get }
    var selectedNonJointBleed: HemoJournalWidgetConfig.BodyPartData? { get }
    var selectedJointBleed: HemoJournalWidgetConfig.BodyPartData? { get }
    var customBodyPart: String? { get set }

    // Methods
    func loadData() async
}

// MARK: - Bundle

class HemoPluginBundle: NSObject {}

extension HemoPluginBundle {
    static var bundle: Bundle {
        Bundle(for: self)
    }
}
