//
//  HemoJournalCardView.swift
//  HemoPlugin
//
//  Created by <PERSON> on 21/02/2025.
//  Copyright © 2025 Huma Therapeutics Ltd. All rights reserved.
//

import SwiftUI

struct HemoJournalCardView<ViewModel: AnyHemoJournalCardViewModel>: View {
    @StateObject var viewModel: ViewModel
    
    // MARK: - Body

    var body: some View {
        return NavigationView {
            ZStack(alignment: .center) {
                navigationLinks

                loadingView
                    .transition(.opacity)
                    .animation(.default, value: viewModel.content.isLoading)
                    .visible(viewModel.content.isLoading)

                if let result = viewModel.content.result {
                    contentView(response: result)
                        .transition(.opacity)
                        .animation(.default, value: viewModel.content.shouldShowResults)
                        .visible(viewModel.content.shouldShowResults)
                        .padding(.horizontal, Dimensions.horizontalPadding)
                        .padding(.bottom, Dimensions.verticalPadding)
                }
            }
            .onAppear() {
                Task { await viewModel.loadData() }
            }
            .halfSheet(
                showSheet: $viewModel.showSheet,
                content: {
                    if viewModel.selectedNonJointBleed?.value == .custom {
                        CustomBodyPartView {
                            viewModel.showSheet = false
                            viewModel.customBodyPart = $0
                            viewModel.navigateToQuestionnaire = true
                        }
                        .frame(maxWidth: .infinity, maxHeight: .infinity)
                        .background(.white)
                    } else {
                        ReusableListView(viewModel: viewModel.reusableListViewModel)
                            .frame(maxWidth: .infinity, maxHeight: .infinity)
                            .background(.white)
                    }
                },
                onDismiss: {

                })
            }
        }

}

// MARK: - Private Helpers

private extension HemoJournalCardView {

    func contentView(response: Void) -> some View {
        VStack(alignment: .center, spacing: 8) {

            if let viewModel = viewModel.headerViewModel {
                JournalHeaderView(viewModel: viewModel)
                    .padding(.top, 40)
            }

            if let viewModel = viewModel.bodyMapViewModel {
                BodyMapView(viewModel: viewModel)
            }

            if let viewModel = viewModel.setupProfileViewModel {
                SetupProfileCardView(viewModel: viewModel)
            }

            if let viewModel = viewModel.activeProfileViewModel {
                ActiveProfileCardView(viewModel: viewModel)
            }
        }
    }

    var loadingView: some View {
        LoadingView()
    }

    var navigationLinks: some View {
        VStack {
            if let addBleedVM = viewModel.addBleedVM {
                NavigationLink("", isActive: $viewModel.navigateToAddBleed) {
                    AddBleedView(viewModel: addBleedVM)
                }
            }

            if let addBleedQuestionnaireSource = viewModel.addBleedQuestionnaireSource {
                NavigationLink("", isActive: $viewModel.navigateToQuestionnaire) {
                    QuestionnaireView(
                        source: addBleedQuestionnaireSource,
                        title: "Add bleed <strong>\(viewModel.selectedNonJointBleed?.name ?? "")</strong>"
                    )
                }
            }

            NavigationLink("", isActive: $viewModel.navigateToProfileQuestionnaire) {
                QuestionnaireView(source: viewModel.profileQuestionnaireSource, title: "Hemophilia profile")
            }

            if let viewModel = viewModel.detailsVM {
                NavigationLink("", isActive: $viewModel.navigateToDetails) {
                    DetailsView(viewModel: viewModel)
                }
            }
        }
    }
}

// MARK: - Preview

// The problem is that #Preview can't use a static property from a generic struct
// Move the preview creation to a non-generic context
struct HemoJournalCardViewPreview: View {
    var body: some View {
        ZStack {
            HemoJournalCardView(
                viewModel: HemoJournalCardViewModel.previewActive
            )
        }
    }
}

#Preview {
    HemoJournalCardViewPreview()
}
