//
//  AddBleedQuestionnaireSource.swift
//  HemoPlugin
//
//  Created by <PERSON> on 01/05/2025.
//  Copyright © 2025 Huma Therapeutics Ltd. All rights reserved.
//

import Foundation
import UIKit
import Combine
import HumaFoundation

class AddBleedQuestionnaireSource: AnyQuestionSource {
    let repository: AnyMedicaitionRepository
    private var cmsMedications: [CMSMedicationResponse.MedicationItem] = []
    let moduleResultSubmitRepository: AnyModuleResultSubmitRepository
    let userRepository: AnyUserObserveRepository
    private let fileRepository: AnyFileRepository
    let deploymentConfiguration: AnyConfiguration?
    let bodyLocation: BodyLocationType
    let bodyPart: BodyPartType
    let customBodyPart: String?
    var didSubmitQuestionnaire: TriggeredEvent<Void> = .init()

    init(
        bodyLocation: BodyLocationType,
        bodyPart: BodyPartType,
        repository: AnyMedicaitionRepository,
        moduleResultSubmitRepository: AnyModuleResultSubmitRepository,
        deploymentConfiguration: AnyConfiguration,
        userRepository: AnyUserObserveRepository,
        fileRepository: AnyFileRepository,
        customBodyPart: String?
    ) {
        self.bodyLocation = bodyLocation
        self.bodyPart = bodyPart
        self.repository = repository
        self.moduleResultSubmitRepository = moduleResultSubmitRepository
        self.deploymentConfiguration = deploymentConfiguration
        self.userRepository = userRepository
        self.fileRepository = fileRepository
        self.customBodyPart = customBodyPart
        getMedications()
    }

    // Add enum for Question IDs
    enum QuestionID: String {
        case bleedSeverity = "BLEED_SEVERITY"
        case bleedReason = "BLEED_REASON"
        case bleedTreated = "BLEED_TREATED"
        case bleedTreatment = "BLEED_TREATMENT"
        case bleedFactorCount = "BLEED_FACTOR_COUNT"
        case bleedDate = "BLEED_DATE"
        case painScale = "PAIN_SCALE"
        case bleedNotes = "BLEED_NOTES"
        case bleepPhotos = "BLEED_PHOTOS"
    }

    func loadQuestions() -> [BaseQuestion] {
        return [
            BaseQuestion(
                id: QuestionID.bleedSeverity.rawValue,
                title: "Severity of the bleed",
                subtitle: nil,
                placeholder: nil,
                accessoryString: nil,
                type: .singleChoice,
                isMandatory: true,
                validationRules: [.required(message: "Please select your hemophilia severity")],
                nextQuestionID: QuestionID.bleedReason.rawValue,
                branchingLogic: nil
            ),
            BaseQuestion(
                id: QuestionID.bleedReason.rawValue,
                title: "Reason for the bleed",
                subtitle: nil,
                placeholder: nil,
                accessoryString: nil,
                type: .singleChoice,
                isMandatory: true,
                validationRules: [.required(message: "Please select your hemophilia severity")],
                nextQuestionID: QuestionID.bleedTreated.rawValue,
                branchingLogic: nil
            ),
            BaseQuestion(
                id: QuestionID.bleedTreated.rawValue,
                title: "Did you treat the bleed?",
                subtitle: nil,
                placeholder: nil,
                accessoryString: nil,
                type: .booleanChoice,
                isMandatory: true,
                validationRules: [.required(message: "Please select whether you have target joints")],
                nextQuestionID: nil,
                branchingLogic: [
                    "YES": QuestionID.bleedTreatment.rawValue,
                    "NO": QuestionID.bleedFactorCount.rawValue
                ]

            ),
            BaseQuestion(
                id: QuestionID.bleedTreatment.rawValue,
                title: "What treatment was used to treat the bleed?",
                subtitle: nil,
                placeholder: nil,
                accessoryString: nil,
                type: .autocompleteSearch,
                isMandatory: true,
                validationRules: [.required(message: "Please select your current prophylactic treatment")],
                nextQuestionID: QuestionID.bleedFactorCount.rawValue,
                branchingLogic: nil
            ),
            BaseQuestion(
                id: QuestionID.bleedFactorCount.rawValue,
                title: "How many total factor units were used to treat the bleed?",
                subtitle: nil,
                placeholder: "Number of factor units",
                accessoryString: nil,
                type: .numeric,
                isMandatory: true,
                validationRules: [
                    .required(message: "Please enter the number of factor units"),
                    .numeric(message: "Number of factor units must be a valid number")
                ],
                nextQuestionID: QuestionID.bleedDate.rawValue,
                branchingLogic: nil
            ),
            BaseQuestion(
                id: QuestionID.bleedDate.rawValue,
                title: "Please confirm date of the bleed",
                subtitle: nil,
                placeholder: nil,
                accessoryString: nil,
                type: .date(range: Date.distantPast...Date()),
                isMandatory: true,
                validationRules: [.required()],
                nextQuestionID: QuestionID.painScale.rawValue,
                branchingLogic: nil
            ),
            BaseQuestion(
                id: QuestionID.painScale.rawValue,
                title: "Pain scale",
                subtitle: "On a scale from 0 to 10, please indicate the severity of the pain you have experienced with the bleed, 10 being the worst pain imaginable.",
                placeholder: "Number of factor units",
                accessoryString: nil,
                type: .slider,
                isMandatory: true,
                validationRules: [.required()],
                nextQuestionID: QuestionID.bleedNotes.rawValue,
                branchingLogic: nil
            ),
            BaseQuestion(
                id: QuestionID.bleedNotes.rawValue,
                title: "Would you like to add any notes?",
                subtitle: "Additional information about the bleed can be useful for your care team.",
                placeholder: "Enter your notes here.",
                accessoryString: nil,
                type: .multilineText,
                isMandatory: false,
                validationRules: [],
                nextQuestionID: QuestionID.bleepPhotos.rawValue,
                branchingLogic: nil
            ),
            BaseQuestion(
                id: QuestionID.bleepPhotos.rawValue,
                title: "Would you like to add the photo?",
                subtitle: "A photo of the bleed can help your care team better understand what’s happening and guide treatment decisions.",
                placeholder: "Choose or take photo",
                accessoryString: nil,
                type: .photo,
                isMandatory: false,
                validationRules: [],
                nextQuestionID: nil,
                branchingLogic: nil
            )
        ]
    }

    func getOptions(for questionID: String) -> [OptionItem] {
        switch questionID {
        case QuestionID.bleedSeverity.rawValue:
            return BleedSeverity.allCases.map { severity in
                    .init(value: severity.rawValue, title: severity.title)
            }
        case QuestionID.bleedReason.rawValue:
            return BleedReason.allCases.map { reason in
                    .init(value: reason.rawValue, title: reason.title)
            }
        default: return []
        }
    }

    func searchAutocompleteOptions(for searchTerm: String, questionID: String) -> [CMSMedicationResponse.MedicationItem] {
        switch questionID {
        case QuestionID.bleedTreatment.rawValue:
            if searchTerm.isEmpty {
                return cmsMedications.filter {
                    $0.tags.contains("OnDemand")
                }
            } else {
                return cmsMedications.filter {
                    $0.title.lowercased().contains(searchTerm.lowercased())
                    && $0.tags.contains("OnDemand")
                }
            }
        default:
            return []
        }
    }

    func onSubmitQuestionnaire(answers: [String : Answer]) async throws -> CreateObjectResponse {
        guard let inputConfig = deploymentConfiguration?.moduleConfig(for: "HemophiliaJournal")?.config.inputConfig,
              let userID = userRepository.currentUser?.userID,
              let bleedSeverityOption = getAnswer(for: QuestionID.bleedSeverity.rawValue, answers: answers)?.value as? OptionItem,
              let bleedSeverity = BleedSeverity(rawValue: bleedSeverityOption.value),
              let bleedReasonOption = getAnswer(for: QuestionID.bleedReason.rawValue, answers: answers)?.value as? OptionItem,
              let bleedReason = BleedReason(rawValue: bleedReasonOption.value),
              let bleedDate = getAnswer(for: QuestionID.bleedDate.rawValue, answers: answers)?.value as? Date,
              let bleedPainScale = getAnswer(for: QuestionID.painScale.rawValue, answers: answers)?.value as? Double else {
            throw RepositoryError.addFailed(reason: nil)
        }

        let bleedTreatment = getAnswer(
            for: QuestionID.bleedTreatment.rawValue,
            answers: answers
        )?.value as? CMSMedicationResponse.MedicationItem

        let bleedNotes = getAnswer(for: QuestionID.bleedNotes.rawValue, answers: answers)?.value as? String

        let bleedPhotos = getAnswer(for: QuestionID.bleepPhotos.rawValue, answers: answers)?.value as? [Data]

        let bleedFactor = getAnswer(for: QuestionID.bleedFactorCount.rawValue, answers: answers)?.value as? String

        // Upload photos and collect file IDs
        var photoIDs: [String] = []
        if let photos = bleedPhotos, !photos.isEmpty {
            for photo in photos {
                do {
                    let fileID = try await fileRepository.uploadAsync(file: photo)
                    photoIDs.append(fileID)
                } catch {
                    print("Failed to upload photo: \(error.localizedDescription)")
                    // Continue with remaining photos even if one fails
                }
            }
        }

        var treatment: HemoJournalPrimitive.Treatment?
        if let bleedTreatment = bleedTreatment {
            treatment = .init(
                id: bleedTreatment.id,
                enabled: true,
                name: bleedTreatment.title,
                codingList: [],
                userId: userID,
                deploymentId: deploymentConfiguration?.deploymentID ?? "",
                dosage: bleedTreatment.dosage,
                unit: bleedTreatment.unit,
                isNotificationEnabled: false,
                schedule: .init(asNeeded: true),
                submitterUserType: "USER",
                tags: bleedTreatment.tags
            )
        }

        let primitive = HemoJournalPrimitive(
            bodyLocation: bodyLocation,
            bodyPartInjury: bodyPart,
            customBodyPart: customBodyPart,
            extraData: .init(
                accidentDate: bleedDate.iso8601ShortDateUTC,
                reason: bleedReason,
                note: bleedNotes,
                photos: photoIDs.isEmpty ? nil : photoIDs,
                scale: bleedPainScale.integer,
                severity: bleedSeverity,
                treatment: treatment,
                factorUnits: bleedFactor?.intValue ?? 0
            )
        )
        let moduleResult = ModuleResult(moduleConfigID: inputConfig.moduleID, value: primitive)
        try await moduleResultSubmitRepository.submitResults([moduleResult], inputConfig: inputConfig)
        didSubmitQuestionnaire.trigger()
        return PreviewModels.createObjectResponse
    }

    private func getAnswer(for questionID: String, answers: [String : Answer]) -> Answer? {
        answers.first(where: { $0.key == questionID })?.value
    }

    private func getMedications() {
        Task {
            do {
                self.cmsMedications = try await repository.getCMSMedication().items
            } catch {
                print(error)
                self.cmsMedications = []
            }
        }
    }

    func buildMedicationSchedule(value: String, timeOfDay: [Date], daysOfWeek: [WeekDay], daysInterval: Int) -> MedicationScheduleV2 {
        var times: [ISODuration] { timeOfDay.map(ISODuration.timeDuration(from:)) }

        switch value {
        case "EVERY_DAY":
            return .everyDay(times: times)
        case "SPECIFIC_DAYS":
            return .specific(days: daysOfWeek, times: times)
        case "DAYS_INTERVAL":
            return .interval(duration: "P\(daysInterval)D", times: times)
        case "AS_NEEDED":
            return .asNeeded
        default:
            return .asNeeded
        }
    }

    func makeFrequencyOptions() -> [String] {
        [
            HumaStrings.medicationFrequencyOne,
            HumaStrings.medicationFrequencyTwo,
            HumaStrings.timesPerDurationOther(3),
            HumaStrings.timesPerDurationOther(4),
            HumaStrings.timesPerDurationOther(5),
            HumaStrings.timesPerDurationOther(6),
            HumaStrings.timesPerDurationOther(7),
            HumaStrings.timesPerDurationOther(8),
            HumaStrings.timesPerDurationOther(9),
            HumaStrings.timesPerDurationOther(10)
        ]
    }
}

extension AnyFileRepository {
    func uploadAsync(file: Data) async throws -> String {
        try await withCheckedThrowingContinuation { continuation in
            uploadFile(with: file, filename: UUID().uuidString + ".jpeg") { result in
                switch result {
                case .success(let fileID):
                    continuation.resume(returning: fileID)
                case .failure(let error):
                    continuation.resume(throwing: error)
                }
            }
        }
    }
}

protocol AnyHemoJournalPrimitive: AnyCodablePrimitive {
    var bodyLocation: BodyLocationType { get }
    var bodyPartInjury: BodyPartType { get }
    var customBodyPart: String? { get }
    var extraData: HemoJournalPrimitive.ExtraData { get }
}

struct HemoJournalPrimitive: AnyCodablePrimitive, AnyHemoJournalPrimitive {

    struct ExtraData: Codable {
        let accidentDate: String
        let reason: BleedReason
        let note: String?
        let photos: [String]?
        let scale: Int
        let severity: BleedSeverity
        let treatment: Treatment?
        let factorUnits: Int?
    }

    struct Treatment: Codable {
        let id: String
        let enabled: Bool
        let name: String
        let codingList: [String]
        let userId: String
        let deploymentId: String
        let dosage: Int?
        let unit: String?
        let isNotificationEnabled: Bool
        let schedule: Schedule
        let submitterUserType: String
        let tags: [String]?
    }

    struct Schedule: Codable {
        let asNeeded: Bool
    }

    static var primitiveName: String { "HemophiliaJournal" }
    var bodyLocation: BodyLocationType
    var bodyPartInjury: BodyPartType
    var customBodyPart: String?
    var extraData: ExtraData

}

// Add enums for options
enum BleedSeverity: String, Codable, CaseIterable {
    case mild = "MILD"
    case moderate = "MODERATE"
    case severe = "SEVERE"

    var title: String {
        switch self {
        case .mild: return "Mild"
        case .moderate: return "Moderate"
        case .severe: return "Severe"
        }
    }
}

enum BleedReason: String, Codable, CaseIterable {
    case spontaneous = "SPONTANEOUS"
    case activity = "ACTIVITY"
    case nonActivity = "NON_ACTIVITY"
    case postActivity = "POST_ACTIVITY"
    case surgery = "SURGERY"
    case other = "OTHER"

    var title: String {
        switch self {
        case .spontaneous: return "Spontaneous"
        case .activity: return "Injury related to activity"
        case .nonActivity: return "Injury not related to activity"
        case .postActivity: return "Post-activity"
        case .surgery: return "Surgery/procedure"
        case .other: return "Other"
        }
    }
}
