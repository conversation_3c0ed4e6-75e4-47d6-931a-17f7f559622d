//
// CustomBodyPartView.swift
// HemoPlugin

// Created by <PERSON> on 01/05/2025.
// Copyright © 2025 Huma Therapeutics Ltd. All rights reserved.
//

import SwiftUI

struct CustomBodyPartView: View {
    
    @State var customBodyPart: String?
    let onSubmit: (String) -> Void

    var body: some View {
        VStack(alignment: .leading) {
            Text("Add custom location")
                .font(.bold)
                .dynamicTypeSize(.medium)
                .padding(.vertical, 14)

            Text("Describe the location of the body you experience bleed (fingers, surface bleed, etc)")
                .font(.default)
                .dynamicTypeSize(.medium)
                .padding(.vertical, 24)

            HumaSUITextField(text: $customBodyPart, placeholder: "Type in body part")

            Spacer(minLength: 200)

            Button(HumaStrings.commonActionConfirm){
                onSubmit(customBodyPart ?? "")
            }
            .pillStyle(.largeFill, fullWidth: true)
        }
        .background(Color.white)
        .padding(.horizontal, 24)
    }
}
