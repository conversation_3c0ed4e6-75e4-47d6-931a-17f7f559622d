//
// BleedDetailsView.swift
// HemoPlugin

// Created by <PERSON> on 01/05/2025.
// Copyright © 2025 Huma Therapeutics Ltd. All rights reserved.
//

import SwiftUI
import Combine

// MARK: - View
struct BleedDetailsView: View {
    @ObservedObject var viewModel: BleedDetailsViewModel

    var body: some View {
        ZStack {
            // Navigation links
            navigationLinks

            // Main content
            mainContent
        }
        .navigationBarBackButtonHidden(true)
        .navigationBarHidden(true)
    }
}

private extension BleedDetailsView {

    private var mainContent: some View {
        VStack(alignment: .leading, spacing: Dimensions.spacing) {
            // Header - directly accessing result
            HeaderView(title: viewModel.result.primitive.bodyPartInjury.description, style: .stacked)

            // Details section
            detailsSection
        }
        .padding(.horizontal, Dimensions.horizontalPadding)
    }

    private var detailsSection: some View {
        ScrollView(showsIndicators: false) {
            VStack(alignment: .leading, spacing: Dimensions.spacing) {
                makeRow(title: "Date of bleed", value: viewModel.result.startDateTime.iso8601ShortDate)

                if let name = viewModel.result.primitive.extraData.treatment?.name {
                    makeRow(title: "Treatment", value: name)
                }

                makeRow(title: "Reason for the bleed", value: viewModel.result.primitive.extraData.reason.title)

                makeRow(title: "Severity of the bleed", value: viewModel.result.primitive.extraData.severity.title)

                makeRow(title: "Pain scale of the bleed", value: viewModel.result.primitive.extraData.scale.string)

                if let note = viewModel.result.primitive.extraData.note {
                    makeRow(title: "Note", value: note)
                }

                Text("Details")
                    .font(.bold)
                    .foregroundColor(.charcoalGrey)
                    .padding(.vertical, Dimensions.spacingMedium)

                makeClickableRow(title: "Location", value: viewModel.result.primitive.bodyLocation.title, action: {
                    viewModel.showBodyMapDetails()
                })

                if let photos = viewModel.result.primitive.extraData.photos, !photos.isEmpty {
                    makeClickableRow(title: "Photo", value: "Photo attached", action: {
                        viewModel.showPhotoDetails()
                    })
                }

                Spacer()
            }
        }
    }

    func makeRow(title: String, value: String) -> some View {
        VStack(alignment: .leading, spacing: Dimensions.spacingMedium) {
            Text(title)
                .font(.bold)
                .foregroundColor(.charcoalGrey)
            Text(value)
                .font(.default)
                .foregroundColor(.charcoalGrey)
            Divider()
                .background(Color.veryLightGrey)
        }
    }

    func makeClickableRow(title: String, value: String, action: (() -> Void)?) -> some View {
        VStack(alignment: .leading, spacing: Dimensions.spacingMedium) {
            HStack {
                VStack(alignment: .leading, spacing: Dimensions.spacing) {
                    Text(title)
                        .font(.default)
                        .foregroundColor(.charcoalGrey)
                    Text(value)
                        .font(.xSmall)
                        .foregroundColor(.charcoalGrey)
                }
                Spacer()
                Image(HumaAssets.commonChevronRightRoundedEdge.name, bundle: HumaFoundationBundle.bundle)

            }
            .contentShape(Rectangle())
            .onTapGesture {
                action?()
            }
            Divider()
                .background(Color.veryLightGrey)
        }

    }
}

// MARK: - Navigation
private extension BleedDetailsView {
    var navigationLinks: some View {
        VStack {
            NavigationLink("", isActive: $viewModel.navigateToBodyMap) {
                BodyMapLogView(bodyLocation: viewModel.result.primitive.bodyLocation)
            }

            if let photos = viewModel.result.primitive.extraData.photos {
                NavigationLink("", isActive: $viewModel.navigateToPhotos) {
                    PhotoLogView(viewModel: .init(photos: photos, fileRepository: viewModel.fileRepository))
                }
            }
        }
    }
}
