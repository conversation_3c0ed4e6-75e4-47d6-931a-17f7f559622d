//
// BleedHistoryViewModel.swift
// HemoPlugin

// Created by <PERSON> on 01/05/2025.
// Copyright © 2025 Huma Therapeutics Ltd. All rights reserved.
//

import HumaFoundation

// MARK: - ViewModel
class BleedHistoryViewModel: ObservableObject {
    @Published var items: [ModuleResult<HemoJournalPrimitive>] = []
    @Published var selectedBleedLog: ModuleResult<HemoJournalPrimitive>?
    @Published var navigateToBleedLog = false
    let fileRepository: AnyFileRepository
    
    var bleedItems: [BleedItem] {
        items.map {
            BleedItem(
                id: $0.id.rawValue,
                title: $0.primitive.bodyPartInjury.description,
                date: $0.startDateTime,
                description: $0.primitive.extraData.note
            )
        }
    }
    
    init(items: [ModuleResult<HemoJournalPrimitive>] = [], fileRepository: AnyFileRepository) {
        self.items = items
        self.fileRepository = fileRepository
    }
    
    func selectBleedItem(id: String) {
        if let selectedItem = items.first(where: { $0.id.rawValue == id }) {
            selectedBleedLog = selectedItem
            navigateToBleedLog = true
        }
    }
}
