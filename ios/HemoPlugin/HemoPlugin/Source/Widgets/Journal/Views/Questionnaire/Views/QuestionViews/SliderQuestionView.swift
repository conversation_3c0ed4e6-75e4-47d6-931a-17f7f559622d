//
// SliderQuestionView.swift
// HemoPlugin

// Created by <PERSON> on 01/05/2025.
// Copyright © 2025 Huma Therapeutics Ltd. All rights reserved.

import SwiftUI

struct SliderQuestionView: View {
    let question: Question
    @State var value: Double
    @ObservedObject var viewModel: QuestionnaireViewModel
    let isMandatory: Bool
    @State private var validationError: String?

    // Configuration for the slider
    let range: ClosedRange<Double>
    let startLabel: String
    let endLabel: String

    init(
        question: Question,
        viewModel: QuestionnaireViewModel,
        isMandatory: Bool = true,
        range: ClosedRange<Double>,
        startLabel: String = "Low",
        endLabel: String = "High"
    ) {
        self.question = question
        self.viewModel = viewModel
        self.isMandatory = isMandatory
        self.range = range
        self.startLabel = startLabel
        self.endLabel = endLabel
        // Initialize with the middle value or from stored answer if available
        _value = State(initialValue: (range.upperBound + range.lowerBound) / 2)
    }

    var body: some View {
        VStack(alignment: .leading, spacing: 8) {
            HumaSliderView(
                value: $value,
                range: range,
                startLabel: startLabel,
                endLabel: endLabel,
                onDragEnded: {
                    submitAnswer()
                }
            )
            .onChange(of: value) { _ in
                // Clear validation errors when user interacts
                validationError = nil
                // Answer will be submitted when user lifts thumb, not on every change
            }

            if let error = validationError {
                Text(error)
                    .font(.footnote)
                    .foregroundColor(.red)
                    .padding(.horizontal, 4)
            }
        }
    }

    private func validateInput() -> Bool {
        // Clear previous errors
        validationError = nil

        // For slider, we mainly check if the value is within the range
        // which should always be true due to the slider constraints
        if value < range.lowerBound || value > range.upperBound {
            validationError = "Value must be between \(Int(range.lowerBound)) and \(Int(range.upperBound))"
            return false
        }

        return true
    }

    private func submitAnswer() {
        if validateInput() {
            viewModel.submitAnswer(answer: .init(question: question, value: value))
        }
    }
}
