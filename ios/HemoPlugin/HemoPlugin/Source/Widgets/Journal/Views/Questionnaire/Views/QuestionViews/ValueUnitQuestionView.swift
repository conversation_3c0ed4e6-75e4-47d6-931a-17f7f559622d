//
// ValueUnitQuestionView.swift
// HemoPlugin

// Created by <PERSON> on 01/05/2025.
// Copyright © 2025 Huma Therapeutics Ltd. All rights reserved.
//

import SwiftUI

struct ValueUnitQuestionView: View {
    // MARK: - Properties
    let question: Question
    let options: [OptionItem]
    let isMandatory: Bool
    @ObservedObject var viewModel: QuestionnaireViewModel
    
    // User input state
    @State var value: String?
    @State var selection: OptionItem?
    
    // UI state
    @FocusState private var isTextFieldFocused: Bool
    @State private var showPicker: Bool? = nil
    @State private var selectedIndex = 0
    @State private var validationError: String?

    private var selectionText: String {
        selection?.title ?? "Unit"
    }

    // MARK: - Body
    var body: some View {
        VStack(alignment: .leading, spacing: 8) {
            valueTextField
            unitSelectionButton
            
            if let error = validationError {
                errorMessage(error)
            }
        }
        .onAppear {
            isTextFieldFocused = true
        }
        .halfSheet(showSheet: $showPicker, content: {
            unitPickerSheet
        }) {
            isTextFieldFocused = value == nil
        }
    }
    
    // MARK: - UI Components
    /// Text field for entering the dosage value
    private var valueTextField: some View {
        TextField("Dosage", text: $value.toUnwrapped(defaultValue: ""))
            .font(.default)
            .keyboardType(.numberPad)
            .focused($isTextFieldFocused)
            .padding()
            .overlay(
                RoundedRectangle(cornerRadius: Dimensions.cornerRadius)
                    .strokeBorder(validationError != nil ? Color.red : Color.veryLightGrey, 
                                  style: StrokeStyle(lineWidth: Dimensions.lineWidth))
            )
            .foregroundColor(.charcoalGrey)
            .toolbar {
                ToolbarItem(placement: .keyboard) {
                    if isTextFieldFocused {
                        keyboardToolbar
                    }
                }
            }
            .onChange(of: value) { _ in
                // Clear validation errors when user types
                validationError = nil
            }
    }
    
    /// Keyboard toolbar with Done button
    private var keyboardToolbar: some View {
        HStack {
            Spacer()
            Button("Done") {
                if validateInput() {
                    submitAnswer()
                    isTextFieldFocused = false
                }
            }
        }
    }
    
    /// Button to select the unit of measurement
    private var unitSelectionButton: some View {
        Button(action: {
            if let selection = self.selection,
               let index = options.firstIndex(where: { $0.value == selection.value }) {
                selectedIndex = index
            }
            showPicker = true
        }) {
            Text(selectionText)
                .frame(minWidth: 0, maxWidth: .infinity, alignment: .leading)
                .font(.default)
                .foregroundColor(selection == nil ? .lightGrey3 : Color.charcoalGrey)
                .padding()
                .overlay(
                    RoundedRectangle(cornerRadius: Dimensions.cornerRadius)
                        .strokeBorder(validationError != nil ? Color.red : Color.veryLightGrey, 
                                     style: StrokeStyle(lineWidth: Dimensions.lineWidth))
                )
        }
        .onChange(of: selection) { _ in
            // Clear validation errors when user selects a unit
            validationError = nil
        }
    }
    
    /// Error message view
    private func errorMessage(_ message: String) -> some View {
        Text(message)
            .font(.footnote)
            .foregroundColor(.red)
            .padding(.horizontal, 4)
    }
    
    /// Unit picker sheet content
    private var unitPickerSheet: some View {
        VStack(spacing: 0) {
            // Toolbar
            HStack {
                Button("Cancel") {
                    showPicker = false
                }
                .foregroundColor(.blue)

                Spacer()

                Text("Select Unit")
                    .font(.headline)

                Spacer()

                Button("Done") {
                    if !options.isEmpty {
                        selection = options[selectedIndex]
                        showPicker = false
                        isTextFieldFocused = false // Ensure keyboard stays dismissed
                        if validateInput() {
                            submitAnswer()
                        }
                    }
                }
                .foregroundColor(.blue)
            }
            .padding()
            .background(Color(.systemGroupedBackground))

            // Option picker
            Picker("", selection: $selectedIndex) {
                ForEach(0..<options.count, id: \.self) { index in
                    Text(options[index].title)
                        .tag(index)
                }
            }
            .pickerStyle(WheelPickerStyle())
            .frame(height: 200)
            .padding()
        }
        .background(.white)
    }

    // MARK: - Validation and Submission
    /// Validates the user input before submission
    private func validateInput() -> Bool {
        // Clear previous errors
        validationError = nil
        
        // Check if value is provided
        guard let inputValue = value, !inputValue.isEmpty else {
            if question.isMandatory {
                validationError = "Please enter a dosage value"
                return false
            }
            return true
        }
        
        // Check if unit is selected
        if selection == nil && question.isMandatory {
            validationError = "Please select a unit"
            return false
        }
        
        // Validate numeric value
        if Double(inputValue) == nil {
            validationError = "Please enter a valid number"
            return false
        }
        
        // Check other validation rules
        for rule in question.validationRules {
            if !rule.validate(inputValue) {
                validationError = rule.errorMessage()
                return false
            }
        }
        
        return true
    }

    /// Submits the answer to the view model
    private func submitAnswer() {
        if let value = self.value?.doubleValue, let selection = self.selection, let unit = MedicationDosageV2.Unit(
            rawValue: selection.value
        ) {
            viewModel
                .submitAnswer(
                    answer: .init(
                        question: question,
                        value: MedicationDosageV2(value: value, unit: unit)
                    )
                )
        }
    }
}

struct ValueUnitQuestionView_Preview: View {

    @State var value: String = ""
    @State var selection: OptionItem? = nil
    var options: [OptionItem] = [
        .init(value: "OPTION_1", title: "Option 1"),
        .init(value: "OPTION_2", title: "Option 2"),
        .init(value: "OPTION_3", title: "Option 3")
    ]

    var body: some View {
        ValueUnitQuestionView(
            question: BaseQuestion(
                id: HemoProfileQuestionnaireSource.QuestionID.dosage.rawValue,
                title: "Please enter your dosage",
                subtitle: nil,
                placeholder: nil,
                accessoryString: nil,
                type: .valueUnit,
                isMandatory: true,
                validationRules: [.required(), .numeric()],
                nextQuestionID: HemoProfileQuestionnaireSource.QuestionID.medicationFrequency.rawValue,
                branchingLogic: nil
            ),
            options: options,
            isMandatory: false,
            viewModel: .init(questionSource: PreviewModels.hemoProfileQuestionSource),
            value: value,
            selection: selection
        )
    }
}

#Preview {
    ValueUnitQuestionView_Preview()
        .padding()
}
