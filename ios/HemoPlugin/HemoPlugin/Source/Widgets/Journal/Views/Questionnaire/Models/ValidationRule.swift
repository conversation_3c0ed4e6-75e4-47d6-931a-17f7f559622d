//
// ValidationRule.swift
// HemoPlugin

// Created by <PERSON> on 01/05/2025.
// Copyright © 2025 Huma Therapeutics Ltd. All rights reserved.

import Foundation

enum ValidationRule {
    case required(message: String? = nil)
    case numeric(message: String? = nil)
    case email(message: String? = nil)
    case minLength(Int, message: String? = nil)
    case maxLength(Int, message: String? = nil)
    case regex(String, message: String? = nil)
    case dateRange(Date?, Date?, message: String? = nil) // Added date range validation
    case custom((Any?) -> Bool, message: String? = nil)
    
    func validate(_ value: Any?) -> Bool {
        switch self {
        case .required:
            if let str = value as? String {
                return !str.isEmpty
            }
            return value != nil
            
        case .numeric:
            guard let str = value as? String else { return false }
            return Double(str) != nil
            
        case .email:
            guard let email = value as? String else { return false }
            let emailRegex = "[A-Z0-9a-z._%+-]+@[A-Za-z0-9.-]+\\.[A-Za-z]{2,64}"
            return NSPredicate(format: "SELF MATCHES %@", emailRegex).evaluate(with: email)
            
        case .minLength(let length, _):
            guard let str = value as? String else { return false }
            return str.count >= length
            
        case .maxLength(let length, _):
            guard let str = value as? String else { return false }
            return str.count <= length
            
        case .regex(let pattern, _):
            guard let str = value as? String else { return false }
            return NSPredicate(format: "SELF MATCHES %@", pattern).evaluate(with: str)
            
        case .dateRange(let minDate, let maxDate, _):
            guard let date = value as? Date else { return false }
            
            if let minDate = minDate, date < minDate {
                return false
            }
            
            if let maxDate = maxDate, date > maxDate {
                return false
            }
            
            return true
            
        case .custom(let validator, _):
            return validator(value)
        }
    }
    
    func errorMessage() -> String {
        switch self {
        case .required(let message):
            return message ?? "This field is required"
            
        case .numeric(let message):
            return message ?? "Please enter a valid number"
            
        case .email(let message):
            return message ?? "Please enter a valid email address"
            
        case .minLength(let length, let message):
            return message ?? "Please enter at least \(length) characters"
            
        case .maxLength(let length, let message):
            return message ?? "Please enter no more than \(length) characters"
            
        case .regex(_, let message):
            return message ?? "Please enter a valid format"
            
        case .dateRange(let minDate, let maxDate, let message):
            if message != nil {
                return message!
            }
            
            if let minDate = minDate, let maxDate = maxDate {
                let formatter = DateFormatter()
                formatter.dateStyle = .medium
                return "Please select a date between \(formatter.string(from: minDate)) and \(formatter.string(from: maxDate))"
            } else if let minDate = minDate {
                let formatter = DateFormatter()
                formatter.dateStyle = .medium
                return "Please select a date after \(formatter.string(from: minDate))"
            } else if let maxDate = maxDate {
                let formatter = DateFormatter()
                formatter.dateStyle = .medium
                return "Please select a date before \(formatter.string(from: maxDate))"
            }
            
            return "Please select a valid date"
            
        case .custom(_, let message):
            return message ?? "Validation failed"
        }
    }
}