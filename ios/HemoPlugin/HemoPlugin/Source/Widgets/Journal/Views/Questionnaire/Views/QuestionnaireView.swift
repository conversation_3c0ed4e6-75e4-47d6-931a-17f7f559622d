//
//  QuestionnaireView.swift
//  HemoPlugin
//
//  Created by <PERSON> on 21/02/2025.
//  Copyright © 2025 Huma Therapeutics Ltd. All rights reserved.
//

import SwiftUI

struct QuestionnaireView<Source: AnyQuestionSource>: View {
    @Environment(\.presentationMode) var presentationMode
    @StateObject private var viewModel: QuestionnaireViewModel
    @State private var questionHeight: CGFloat = 0
    private let title: String

    // Initialize with a specific questionnaire source
    init(source: Source, title: String) {
        _viewModel = StateObject(wrappedValue: QuestionnaireViewModel(questionSource: source))
        self.title = title
    }

    var body: some View {
        VStack {
            HeaderView(title: title, style: .compact)

            ScrollViewReader { proxy in
                ScrollView(showsIndicators: false) {
                    VStack(alignment: .leading, spacing: 20) {
                        let orderedVisibleQuestions = viewModel.getOrderedVisibleQuestions()
                        
                        ForEach(orderedVisibleQuestions) { question in
                            QuestionView(
                                question: question,
                                viewModel: viewModel,
                                isLastVisible: question.id == orderedVisibleQuestions.last?.id
                            )
                            .id(question.id)
                            .padding(.bottom, 20)
                            .background(
                                GeometryReader { geometry in
                                    Color.clear
                                        .onAppear {
                                            questionHeight = geometry.size.height
                                        }
                                }
                            )
                        }

                        if viewModel.showSubmitButton {
                            VStack(alignment: .leading, spacing: Dimensions.spacingMedium) {
                                Text("Confirm the information you have entered is correct.")
                                    .font(.bold)
                                    .foregroundColor(Color.charcoalGrey)

                                Text("Scroll up to edit this information.")
                                    .font(.xSmall)
                                    .foregroundColor(Color.charcoalGrey)

                                Button("Submit"){
                                    viewModel.submitQuestionnaire()
                                }
                                .pillStyle(isLoading: $viewModel.isSubmitting, style: .largeFill, fullWidth: true)
                            }
                            .onAppear {
                                withAnimation {
                                    proxy.scrollTo("submitButton", anchor: .bottom)
                                }
                            }
                        }

                        Spacer(minLength: calculateSpacerHeight())

                    }
                    .id("submitButton") // Add an ID to the bottom of the ScrollView
                }
                .onChange(of: viewModel.visibleQuestionsCount) { newCount in
                    if newCount > 0 {
                        // Get the ordered visible questions and scroll to the last one
                        let orderedVisibleQuestions = viewModel.getOrderedVisibleQuestions()
                        if let lastQuestion = orderedVisibleQuestions.last {
                            withAnimation {
                                proxy.scrollTo(lastQuestion.id, anchor: .top)
                            }
                        }
                    }
                }
            }
        }
        .padding(.horizontal, Dimensions.horizontalPadding)
        .navigationBarBackButtonHidden(true)
        .navigationBarHidden(true)
        .onChange(of: viewModel.shouldDismiss) { shouldDismiss in
            if shouldDismiss {
                presentationMode.wrappedValue.dismiss()
            }
        }
    }
}

private extension QuestionnaireView {
    private func calculateSpacerHeight() -> CGFloat {
        let screenHeight = UIScreen.main.bounds.height
        let spacerHeight = (screenHeight - questionHeight) / 2
        return max(spacerHeight, 0)
    }
}


struct DefaultQuestionnaireView: View {
    var body: some View {
        QuestionnaireView(source: PreviewModels.hemoProfileQuestionSource, title: "Hemophilia profile")
    }
}


struct QuestionnaireView_Previews: PreviewProvider {
    static var previews: some View {
        Group {
            DefaultQuestionnaireView()
        }
    }
}
