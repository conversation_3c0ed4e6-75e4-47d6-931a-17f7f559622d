//
// MultipleChoiceQuestionView.swift
// HemoPlugin

// Created by <PERSON> on 01/05/2025.
// Copyright © 2025 Huma Therapeutics Ltd. All rights reserved.

import SwiftUI

struct MultipleChoiceQuestionView: View {
    let question: Question
    let options: [OptionItem]
    @State var selectedTagIDs: Set<UUID> = [] // Track selected tags by their IDs
    @ObservedObject var viewModel: QuestionnaireViewModel
    let isMandatory: Bool
    let choiceType: MultipleChoiceType

    @State private var tagItems: [TagViewItem] = [] // Convert options to TagViewItems

    var body: some View {
        VStack(alignment: .leading, spacing: 16) {
            // Use the provided TagView with tagType
            TagView(tags: $tagItems, style: choiceType.tagType)
                .onChange(of: tagItems) { newTags in
                    // Update selectedTagIDs when tags change
                    selectedTagIDs = Set(newTags.filter { $0.isSelected }.map { $0.id })
                }

            // Submit button
            Button("Next") {
                submitAnswer()
            }
            .pillStyle(.large, fullWidth: true)
            .disabled(isMandatory && selectedTagIDs.isEmpty)
            .opacity(isMandatory && selectedTagIDs.isEmpty ? 0.6 : 1.0)
            .padding(.top, 8)
        }
        .onAppear {
            initializeTagItems()
        }
        .onChange(of: selectedTagIDs) { newSelection in
            syncTagsWithSelection(newSelection)
        }
    }

    private func initializeTagItems() {
        // Initialize tag items with Binding for isSelected
        tagItems = options.map { option in
            TagViewItem(
                id: option.id,
                title: option.title,
                isSelected: selectedTagIDs.contains { id in id == UUID() } // Default to unselected
            )
        }
    }

    private func syncTagsWithSelection(_ selection: Set<UUID>) {
        // Update tagItems to reflect the current selectedTagIDs
        for index in tagItems.indices {
            tagItems[index].isSelected = selection.contains(tagItems[index].id)
        }
    }

    private func submitAnswer() {
        let selectedOptions = options
            .filter { selectedTagIDs.contains($0.id) }
        let answer = Answer(question: question, value: selectedOptions)
        viewModel.submitAnswer(answer: answer)
    }
}

private extension MultipleChoiceType {
    var tagType: TagStyle {
        switch self {
        case .circle:
            return .circular
        case .default:
            return .defaultStyle
        }
    }
}

private struct MultipleChoiceQuestionView_Preview: View {
    var body: some View {
        MultipleChoiceQuestionView(
            question: BaseQuestion(
                id: HemoProfileQuestionnaireSource.QuestionID.targetJointLocations.rawValue,
                title: "Which are your target joints?",
                subtitle: nil,
                placeholder: nil,
                accessoryString: nil,
                type: .multipleChoice(.default),
                isMandatory: true,
                validationRules: [.required()],
                nextQuestionID: HemoProfileQuestionnaireSource.QuestionID.onProphylacticTreatment.rawValue,
                branchingLogic: nil
            ),
            options: [
                .init(value: "OPTION_1", title: "Option 1"),
                .init(value: "OPTION_2", title: "Option 2"),
                .init(value: "OPTION_3", title: "Option 3")
            ],
            viewModel: .init(questionSource: PreviewModels.hemoProfileQuestionSource),
            isMandatory: true,
            choiceType: .default
        )
    }
}

#Preview {
    MultipleChoiceQuestionView_Preview()
}
