//
// TextQuestionView.swift
// HemoPlugin

// Created by <PERSON> on 01/05/2025.
// Copyright © 2025 Huma Therapeutics Ltd. All rights reserved.

import SwiftUI

struct TextQuestionView: View {
    let question: Question
    @State var value: String?
    @ObservedObject var viewModel: QuestionnaireViewModel
    let isMandatory: Bool
    @FocusState private var isTextFieldFocused: Bool
    @State private var validationError: String?
    
    var body: some View {
        VStack(alignment: .leading, spacing: 8) {
            TextField(question.placeholder ?? "", text: $value.toUnwrapped(defaultValue: ""))
                .font(.default)
                .keyboardType(.default)
                .focused($isTextFieldFocused)
                .padding()
                .overlay(
                    RoundedRectangle(cornerRadius: Dimensions.cornerRadius)
                        .strokeBorder(validationError != nil ? Color.red : Color.veryLightGrey, style: StrokeStyle(lineWidth: Dimensions.lineWidth))
                )
                .foregroundColor(Color.charcoalGrey)
                .toolbar {
                    ToolbarItem(placement: .keyboard) {
                        if isTextFieldFocused {
                            HStack {
                                Spacer()
                                But<PERSON>("Done") {
                                    if validateInput() {
                                        submitAnswer()
                                        isTextFieldFocused = false
                                    }
                                }
                            }
                        }
                    }
                }
                .onChange(of: value) { _ in
                    // Clear validation errors when user types
                    validationError = nil
                }
            
            if let error = validationError {
                Text(error)
                    .font(.footnote)
                    .foregroundColor(.red)
                    .padding(.horizontal, 4)
            }
        }
        .onAppear {
            isTextFieldFocused = true
        }
    }

    private func validateInput() -> Bool {
        // Clear previous errors
        validationError = nil
        
        guard let inputValue = value, !inputValue.isEmpty else {
            if question.isMandatory {
                validationError = "This field is required"
                return false
            }
            return true
        }
        
        // Check against all validation rules
        for rule in question.validationRules {
            if !rule.validate(inputValue) {
                validationError = rule.errorMessage()
                return false
            }
        }
        
        return true
    }
    
    private func submitAnswer() {
        if let value = self.value {
            viewModel.submitAnswer(answer: .init(question: question, value: value))
        }
    }
}
