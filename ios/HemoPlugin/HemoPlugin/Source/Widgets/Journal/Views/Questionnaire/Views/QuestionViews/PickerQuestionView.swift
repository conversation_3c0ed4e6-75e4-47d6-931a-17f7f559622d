//
// PickerQuestionView.swift
// HemoPlugin

// Created by <PERSON> on 01/05/2025.
// Copyright © 2025 Huma Therapeutics Ltd. All rights reserved.

import SwiftUI

struct PickerQuestionView: View {
    let question: Question
    @ObservedObject var viewModel: QuestionnaireViewModel
    let isMandatory: Bool
    let options: [OptionItem]
    @State var selection: OptionItem?

    @State private var showPicker: Bool? = nil
    @State private var selectedIndex = 0

    private var text: String {
        if let selection = self.selection {
            return selection.title
        } else {
            return question.placeholder ?? ""
        }
    }

    var body: some View {
        VStack(alignment: .leading, spacing: 8) {
            Button(
action: {
    if let selection = self.selection,
       let index = options.firstIndex(where: { $0.value == selection.value}) {
                    selectedIndex = index
                }
                showPicker = true
            }) {
                Text(text)
                    .frame(minWidth: 0, maxWidth: .infinity, alignment: .leading)
                    .font(.default)
                    .foregroundColor(selection == nil ? .lightGrey3 : Color.charcoalGrey)
                    .padding()
                    .overlay(
                        RoundedRectangle(cornerRadius: Dimensions.cornerRadius)
                            .strokeBorder(Color.veryLightGrey, style: StrokeStyle(lineWidth: Dimensions.lineWidth))
                    )
            }

            if !isMandatory {
                Button("Skip this question") {
                    viewModel.skipQuestion(for: question.id)
                }
                .padding(.top, 8)
                .foregroundColor(.blue)
            }
        }
        .halfSheet(showSheet: $showPicker) {
            VStack(spacing: 0) {
                // Toolbar
                HStack {
                    Button("Cancel") {
                        showPicker = false
                    }
                    .foregroundColor(.blue)

                    Spacer()

                    Text(self.question.placeholder ?? "")
                        .font(.headline)

                    Spacer()

                    Button("Done") {
                        if !options.isEmpty {
                            selection = options[selectedIndex]
                            showPicker = false
                            submitAnswers()
                        }
                    }
                    .foregroundColor(.blue)
                }
                .padding()
                .background(Color(.systemGroupedBackground))

                // Option picker
                Picker("", selection: $selectedIndex) {
                    ForEach(0..<options.count, id: \.self) { index in
                        Text(options[index].title)
                            .tag(index)
                    }
                }
                .pickerStyle(WheelPickerStyle())
                .frame(height: 200)
                .background(Color(.systemGroupedBackground))
                .padding()
            }
            .background(Color(.systemGroupedBackground))
        }
    }

    private func submitAnswers() {
        if let selection = self.selection {
            viewModel.submitAnswer(answer: .init(question: question, value: selection))
        }
    }
}
