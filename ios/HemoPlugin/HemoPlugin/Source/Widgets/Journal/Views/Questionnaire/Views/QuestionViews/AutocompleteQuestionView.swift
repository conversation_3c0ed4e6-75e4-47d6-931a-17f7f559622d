//
//  AutocompleteQuestionView.swift
//  HemoPlugin
//
//  Created by <PERSON> on 21/02/2025.
//  Copyright © 2025 Huma Therapeutics Ltd. All rights reserved.
//

import SwiftUI
import Combine

struct AutocompleteQuestionView: View {
    let question: Question
    @ObservedObject var viewModel: QuestionnaireViewModel
    let isMandatory: Bool

    @State private var selectedOption: CMSMedicationResponse.MedicationItem?
    @State private var isPresentingSearch = false
    @State private var hasSubmitted = false

    var body: some View {
        VStack(alignment: .leading, spacing: 8) {
            Button(action: {
                isPresentingSearch = true
            }) {
                Text("Select option")
                    .font(.default)
                    .foregroundColor(selectedOption == nil ? .gray : .charcoalGrey)
                    .aligned(to: .leading)
            }
            .frame(maxWidth: .infinity)
            .padding()
            .overlay(
                RoundedRectangle(cornerRadius: Dimensions.cornerRadius)
                    .strokeBorder(Color.veryLightGrey, style: StrokeStyle(lineWidth: Dimensions.lineWidth))
            )
            .foregroundColor(.charcoalGrey)

            if let selectedOption = self.selectedOption {
                MedicationRowView(option: selectedOption, isSelected: false, selectable: false)
            }

            if !isMandatory {
                Button("Skip this question") {
                    viewModel.skipQuestion(for: question.id)
                }
                .padding(.top, 8)
                .foregroundColor(.blue)
            }
        }
        .fullScreenCover(isPresented: $isPresentingSearch) {
            AutocompleteSearchView(
                questionID: question.id,
                viewModel: viewModel,
                selectedOption: $selectedOption,
                isPresentingSearch: $isPresentingSearch
            )
        }
        // Only trigger when the sheet is dismissed and we have a selection
        .onChange(of: isPresentingSearch) { isPresenting in
            if !isPresenting, let option = selectedOption, !hasSubmitted {
                hasSubmitted = true  // Set flag to prevent infinite loop
                viewModel.submitAnswer(answer: .init(question: question, value: option))
            }
        }
    }
}

struct AutocompleteSearchView: View {
    let questionID: String
    @ObservedObject var viewModel: QuestionnaireViewModel
    @Binding var selectedOption: CMSMedicationResponse.MedicationItem?
    @Binding var isPresentingSearch: Bool

    @State private var searchText: String = ""
    @State private var searchResults: [CMSMedicationResponse.MedicationItem] = []
    @State private var temporarySelection: CMSMedicationResponse.MedicationItem?

    @FocusState private var isTextFieldFocused: Bool

    var body: some View {
        NavigationView {
            VStack {
                HeaderView(title: "Search Medication", style: .stacked) {
                    isPresentingSearch = false
                }
                // Search field
                TextField("Start typing to search", text: $searchText)
                    .font(.default)
                    .keyboardType(.default)
                    .focused($isTextFieldFocused)
                    .padding()
                    .overlay(
                        RoundedRectangle(cornerRadius: Dimensions.cornerRadius)
                            .strokeBorder(Color.veryLightGrey, style: StrokeStyle(lineWidth: Dimensions.lineWidth))
                    )
                    .foregroundColor(.charcoalGrey)
                    .toolbar {
                        ToolbarItem(placement: .keyboard) {
                            if isTextFieldFocused {
                                HStack {
                                    Spacer()
                                    Button("Done") {
                                        isTextFieldFocused = false
                                    }
                                }
                            }
                        }
                    }
                    .onChange(of: searchText) { _ in
                        performSearch(query: searchText)
                    }

                ScrollView(showsIndicators: false) {
                    if searchResults.isEmpty {
                        VStack(spacing: 20) {
                            // Only show Add button if user has typed something
                            if !searchText.isEmpty {
                                HStack {
                                    Image(HumaAssets.plus.name, bundle: HumaFoundationBundle.bundle)

                                    Button("Add \"\(searchText)\"", action: {
                                        // Create a custom medication item with the search text
                                        let customOption = CMSMedicationResponse.MedicationItem(
                                            id: UUID().uuidString,
                                            title: searchText
                                        )
                                        temporarySelection = customOption
                                        selectedOption = customOption
                                        isPresentingSearch = false
                                    })
                                    .underlineStyle(style: .large)
                                }
                                .frame(maxWidth: .infinity, alignment: .leading)
                                .padding(.horizontal)
                            }
                        }
                    } else {
                        ForEach(searchResults, id: \.id) { option in
                            Button(action: {
                                temporarySelection = option
                                selectedOption = option
                                isPresentingSearch = false
                            }) {
                                MedicationRowView(
                                    option: option,
                                    isSelected: temporarySelection == option
                                )
                            }
                        }
                    }
                }
                .background(.clear)}
        }
        .padding(.horizontal, Dimensions.horizontalPadding)
        .padding(.vertical, Dimensions.verticalPadding)
        .navigationBarBackButtonHidden(true)
        .navigationBarHidden(true)
        .onAppear {
            executeOnMainThread(after: 0.2) {
                performSearch(query: "")
            }

        }
    }


    private func performSearch(query: String) {
        self.searchResults = viewModel.searchAutocompleteOptions(for: query, questionID: questionID)
    }
}

private struct MedicationRowView: View {
    var option: CMSMedicationResponse.MedicationItem
    var isSelected: Bool
    var selectable: Bool = true

    var body: some View {
        HStack {
            Image(isSelected ? "radio-selected" : "radio", bundle: HemoPluginBundle.bundle)
                .frame(width: 30, height: 30)

            VStack(alignment: .leading, spacing: Dimensions.spacing) {
                VStack(alignment: .leading) {
                    Text(option.title)
                        .font(.bold)
                        .foregroundColor(Color.charcoalGrey)
                        .multilineTextAlignment(.leading)
                        .lineLimit(nil) // Allow multiline
                        .fixedSize(horizontal: false, vertical: true) // Prevent truncation
                        .frame(maxWidth: .infinity, alignment: .leading)
                }
                if let dosage = option.dosage, let unit = option.unit, let frequency = option.frequency {
                    Text("Recommended regimen:")
                        .font(.default)
                        .foregroundColor(Color.charcoalGrey)
                        .multilineTextAlignment(.leading)
                        .lineLimit(nil) // Allow multiline
                        .fixedSize(horizontal: false, vertical: true) // Prevent truncation
                        .frame(maxWidth: .infinity, alignment: .leading)

                    HStack(alignment: .top) {
                        Text("Dose:")
                            .font(.default)
                            .foregroundColor(Color.charcoalGrey)
                            .multilineTextAlignment(.leading)
                        Text("\(dosage) \(unit)")
                            .font(.bold)
                            .foregroundColor(Color.charcoalGrey)
                            .multilineTextAlignment(.leading)
                            .lineLimit(nil)
                            .fixedSize(horizontal: false, vertical: true)
                    }

                    HStack(alignment: .top) {
                        Text("Frequency:")
                            .font(.default)
                            .foregroundColor(Color.charcoalGrey)
                            .multilineTextAlignment(.leading)
                        Text(frequency)
                            .font(.bold)
                            .foregroundColor(Color.charcoalGrey)
                            .multilineTextAlignment(.leading)
                            .lineLimit(nil)
                            .fixedSize(horizontal: false, vertical: true)
                    }
                }
            }
        }
        .frame(maxWidth: .infinity, alignment: .leading)
        .padding()
        .enableCardStyling()
        .padding(.horizontal, 2)
    }
}

private struct AutocompleteSearchView_Preview : View {

    @State var selectedOption: CMSMedicationResponse.MedicationItem? = nil
    @State var isPresentingSearch: Bool = false

    var body: some View {
        AutocompleteSearchView(
            questionID: HemoProfileQuestionnaireSource.QuestionID.prophylacticTreatment.rawValue,
            viewModel: .init(questionSource: PreviewModels.hemoProfileQuestionSource),
            selectedOption: $selectedOption,
            isPresentingSearch: $isPresentingSearch
        )
    }
}

#Preview {
    Group {
        AutocompleteQuestionView(
            question: BaseQuestion(
                id: HemoProfileQuestionnaireSource.QuestionID.prophylacticTreatment.rawValue,
                title: "Please indicate your current prophylactic treatment for hemophilia.",
                subtitle: nil,
                placeholder: nil,
                accessoryString: nil,
                type: .autocompleteSearch,
                isMandatory: true,
                validationRules: [.required()],
                nextQuestionID: HemoProfileQuestionnaireSource.QuestionID.dosage.rawValue,
                branchingLogic: nil
            ),
            viewModel: .init(questionSource: PreviewModels.hemoProfileQuestionSource),
            isMandatory: true
        )
        .padding()

        //        AutocompleteSearchView_Preview()
    }
}
