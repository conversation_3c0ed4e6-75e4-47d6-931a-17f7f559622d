//
// QuestionnaireViewModel.swift
// HemoPlugin

// Created by <PERSON> on 01/05/2025.
// Copyright © 2025 Huma Therapeutics Ltd. All rights reserved.
//

import Foundation
import Combine
import HumaFoundation

protocol AnyQuestionSource {
    func loadQuestions() -> [BaseQuestion]
    func getOptions(for questionID: String) -> [OptionItem]
    func searchAutocompleteOptions(for searchTerm: String, questionID: String) -> [CMSMedicationResponse.MedicationItem]
    func onSubmitQuestionnaire(answers: [String : Answer]) async throws -> CreateObjectResponse
    var didSubmitQuestionnaire: TriggeredEvent<Void> { get set }
}

class QuestionnaireViewModel: ObservableObject {
    @Published private(set) var questions: [BaseQuestion] = []
    @Published private(set) var visibleQuestionsCount: Int = 0
    @Published var answers: [String: Answer] = [:]
    @Published var showSubmitButton: Bool = false
    @Published var isSubmitting: Bool = false
    @Published var shouldDismiss: Bool = false

    // Track which questions should be visible (not just count)
    @Published private(set) var visibleQuestionIDs: Set<String> = []

    // Updated type to match new protocol name
    private let questionSource: AnyQuestionSource
    private var cancellables = Set<AnyCancellable>()

    // Keep track of question order for resetting purposes
    private var questionSequence: [String] = []

    var didSubmitQuestionnaire: TriggeredEvent<Void> = .init()

    // Updated parameter type to match new protocol name
    init(questionSource: AnyQuestionSource) {
        self.questionSource = questionSource
        loadQuestions()
    }

    private func loadQuestions() {
        self.questions = questionSource.loadQuestions()
        if !self.questions.isEmpty {
            if let firstQuestionID = self.questions.first?.id {
                self.questionSequence = [firstQuestionID]
                self.visibleQuestionIDs = [firstQuestionID]
                self.visibleQuestionsCount = 1
            }
        }
    }

    // New method to retrieve questions in display order
    func getOrderedVisibleQuestions() -> [BaseQuestion] {
        // Filter questionSequence by visible questions and map to actual question objects
        return questionSequence
            .filter { visibleQuestionIDs.contains($0) }
            .compactMap { questionID in
                questions.first { $0.id == questionID }
            }
    }

    func submitAnswer(answer: Answer) {
        // Get the questionID from the Answer object
        let questionID = answer.question.id

        // Check if this is an answer change for an existing question
        let isAnswerChange = answers[questionID] != nil

        // Save the answer directly
        answers[questionID] = answer

        // If changing an existing answer, reset subsequent questions
        if isAnswerChange {
            resetSubsequentQuestions(after: questionID)
        } else {
            // Add to question sequence if it's a new answer
            if !questionSequence.contains(questionID) {
                questionSequence.append(questionID)
            }
        }

        // Update visible questions
        moveToNextQuestion(after: questionID)
    }

    private func resetSubsequentQuestions(after questionID: String) {
        guard let index = questionSequence.firstIndex(of: questionID) else { return }

        // Get all subsequent question IDs
        let subsequentQuestionIDs = Array(questionSequence.suffix(from: index + 1))

        // Remove answers for these questions
        for id in subsequentQuestionIDs {
            answers.removeValue(forKey: id)
            visibleQuestionIDs.remove(id)
        }

        // Update question sequence to remove subsequent questions
        questionSequence = Array(questionSequence.prefix(through: index))

        // Reset submit button visibility
        showSubmitButton = false

        // Update visible questions count
        updateVisibleQuestionsCount()
    }

    func skipQuestion(for questionID: String) {
        if (!questionSequence.contains(questionID)) {
            questionSequence.append(questionID)
        }
        moveToNextQuestion(after: questionID)
    }

    private func moveToNextQuestion(after currentQuestionID: String) {
        guard let currentQuestion = questions.first(where: { $0.id == currentQuestionID }) else { return }

        var nextQuestionID: String?

        // Check if there's branching logic based on answer
        if let branchLogic = currentQuestion.branchingLogic,
           let answer = answers[currentQuestionID],
           let stringValue = answer.stringValue,
           let targetID = branchLogic[stringValue] {
            nextQuestionID = targetID
        } else {
            nextQuestionID = currentQuestion.nextQuestionID
        }

        // If we have a next question, make it visible
        if let nextID = nextQuestionID,
           let nextQuestion = questions.first(where: { $0.id == nextID }) {

            // Add to visible question IDs
            visibleQuestionIDs.insert(nextID)

            // Add next question to sequence if not already present
            if !questionSequence.contains(nextID) {
                // Insert at the position after the current question in the sequence
                if let currentIndex = questionSequence.firstIndex(of: currentQuestionID) {
                    questionSequence.insert(nextID, at: currentIndex + 1)
                } else {
                    questionSequence.append(nextID)
                }
            }

            // Update the visible questions count
            updateVisibleQuestionsCount()
        } else {
            // No more questions, show submit button
            showSubmitButton = true
        }
    }

    private func updateVisibleQuestionsCount() {
        visibleQuestionsCount = visibleQuestionIDs.count
    }

    func isQuestionVisible(_ questionID: String) -> Bool {
        return visibleQuestionIDs.contains(questionID)
    }

    func submitQuestionnaire() {
        isSubmitting = true

        Task { @MainActor in

            // Validate that all mandatory questions are answered
            let unansweredMandatoryQuestions = questions.filter { question in
                question.isMandatory &&
                visibleQuestionIDs.contains(question.id) &&
                !answers.keys.contains(question.id)
            }

            if !unansweredMandatoryQuestions.isEmpty {
                isSubmitting = false
                return
            }
            do {
                _ = try await questionSource.onSubmitQuestionnaire(answers: answers)
                isSubmitting = false
                shouldDismiss = true
            } catch {
                isSubmitting = false
            }
        }
    }

    func getOptions(for questionID: String) -> [OptionItem] {
        return questionSource.getOptions(for: questionID)
    }

    func searchAutocompleteOptions(for searchTerm: String, questionID: String) -> [CMSMedicationResponse.MedicationItem] {
        questionSource.searchAutocompleteOptions(for: searchTerm, questionID: questionID)
    }
}

// Helper extension for safe array access
extension Array {
    subscript(safe index: Index) -> Element? {
        return indices.contains(index) ? self[index] : nil
    }
}
