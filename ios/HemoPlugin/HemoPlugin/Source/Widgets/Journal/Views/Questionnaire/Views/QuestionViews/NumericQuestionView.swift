//
// NumericQuestionView.swift
// HemoPlugin

// Created by <PERSON> on 01/05/2025.
// Copyright © 2025 Huma Therapeutics Ltd. All rights reserved.
//

import SwiftUI

struct NumericQuestionView: View {
    let question: Question
    @State var value: String?
    @ObservedObject var viewModel: QuestionnaireViewModel
    let isMandatory: Bool
    @FocusState private var isTextFieldFocused: Bool
    @Binding var isFocused: Bool
    @State private var validationError: String?
    
    init(question: Question, value: String?, viewModel: QuestionnaireViewModel, isMandatory: Bool, isFocused: Binding<Bool> = .constant(true)) {
        self.question = question
        self._value = State(initialValue: value)
        self.viewModel = viewModel
        self.isMandatory = isMandatory
        self._isFocused = isFocused
    }

    var body: some View {
        VStack(alignment: .leading, spacing: 8) {
            HStack {
                TextField(question.placeholder ?? "", text: $value.toUnwrapped(defaultValue: ""))
                    .font(.default)
                    .keyboardType(.numberPad) // Use number pad for numeric input
                    .focused($isTextFieldFocused)
                    .padding()
                    .foregroundColor(Color.charcoalGrey)
                    .toolbar {
                        ToolbarItem(placement: .keyboard) {
                            if isTextFieldFocused {
                                HStack {
                                    Spacer()
                                    Button("Done") {
                                        if validateInput() {
                                            submitAnswer()
                                            isTextFieldFocused = false
                                        }
                                    }
                                }
                            }
                        }
                    }
                    .onChange(of: value) { _ in
                        // Clear validation errors when user types
                        validationError = nil
                    }

                if let accessoryString = question.accessoryString {
                    Text(accessoryString)
                        .font(.default)
                        .foregroundColor(.charcoalGrey)
                        .padding(.trailing, Dimensions.horizontalPadding)
                }
            }
            .overlay(
                RoundedRectangle(cornerRadius: Dimensions.cornerRadius)
                    .strokeBorder(validationError != nil ? Color.red : Color.veryLightGrey, style: StrokeStyle(lineWidth: Dimensions.lineWidth))
            )

            if let error = validationError {
                Text(error)
                    .font(.footnote)
                    .foregroundColor(.red)
                    .padding(.horizontal, 4)
            }
        }
        .onAppear {
            isTextFieldFocused = isFocused
        }
        .onChange(of: isFocused) { newValue in
            isTextFieldFocused = newValue
        }
        .onChange(of: isTextFieldFocused) { newValue in
            isFocused = newValue
        }
    }

    private func validateInput() -> Bool {
        // Clear previous errors
        validationError = nil
        
        guard let inputValue = value, !inputValue.isEmpty else {
            if question.isMandatory {
                validationError = "This field is required"
                return false
            }
            return true
        }
        
        // Check against all validation rules
        for rule in question.validationRules {
            if !rule.validate(inputValue) {
                validationError = rule.errorMessage()
                return false
            }
        }
        
        return true
    }

    private func submitAnswer() {
        // Convert to Double before submitting to ensure it's a valid number
        if let value = self.value {
            viewModel.submitAnswer(answer: .init(question: question, value: value))
        }
    }
}

private struct NumericQuestionView_Previews: View {

    @State var text = ""

    var body: some View {
        NumericQuestionView(
            question: BaseQuestion(
                id: HemoProfileQuestionnaireSource.QuestionID.weight.rawValue,
                title: "What is your weight?",
                subtitle: nil,
                placeholder: "Enter your weight",
                accessoryString: nil,
                type: .numeric,
                isMandatory: true,
                validationRules: [.required(), .numeric()],
                nextQuestionID: HemoProfileQuestionnaireSource.QuestionID.hemophiliaType.rawValue,
                branchingLogic: nil
            ),
            value: text,
            viewModel: .init(questionSource: PreviewModels.hemoProfileQuestionSource),
            isMandatory: true,
            isFocused: .constant(true)
        )
    }
}

#Preview {
    NumericQuestionView_Previews()
        .padding()
}
