//
// Question.swift
// HemoPlugin

// Created by <PERSON> on 01/05/2025.
// Copyright © 2025 Huma Therapeutics Ltd. All rights reserved.
//

import Foundation

protocol Question {
    var id: String { get }
    var title: String { get }
    var subtitle: String? { get }
    var placeholder: String? { get }
    var accessoryString: String? { get }
    var type: QuestionType { get }
    var isMandatory: Bool { get }
    var validationRules: [ValidationRule] { get }
    var nextQuestionID: String? { get }
    var branchingLogic: [String: String]? { get }
}

// Base implementation for questions
struct BaseQuestion: Question, Identifiable {
    let id: String
    let title: String
    let subtitle: String?
    let placeholder: String?
    let accessoryString: String?
    let type: QuestionType
    let isMandatory: Bool
    let validationRules: [ValidationRule]
    let nextQuestionID: String?
    let branchingLogic: [String: String]?
}
