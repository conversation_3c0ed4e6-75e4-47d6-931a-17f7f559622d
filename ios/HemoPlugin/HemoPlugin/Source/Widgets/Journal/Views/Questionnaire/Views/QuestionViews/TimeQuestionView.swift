//
//  TimeQuestionView.swift
//  HemoPlugin
//
//  Created by <PERSON> on 21/02/2025.
//  Copyright © 2025 Huma Therapeutics Ltd. All rights reserved.
//

import SwiftUI

struct TimeQuestionView: View {
    let question: Question
    @ObservedObject var viewModel: QuestionnaireViewModel
    let isMandatory: Bool

    @State var times: [Date?] = [nil]
    @State private var showTimePicker: Bool? = nil
    @State private var showTimePickerIndex: Int? = nil
    @State private var tempTime: Date = Date()
    @State private var validationError: String?

    var body: some View {
        VStack(alignment: .leading, spacing: 8) {
            ForEach(0..<times.count, id: \.self) { index in
                HStack(spacing: 12) {

                    Button(action: {
                        tempTime = times[index] ?? Date()
                        showTimePicker = true
                        showTimePickerIndex = index
                    }) {
                        Text(formattedTime(times[index]))
                            .frame(minWidth: 0, maxWidth: .infinity, alignment: .leading)
                            .font(.default)
                            .foregroundColor(times[index] == nil ? .lightGrey3 : Color.charcoalGrey)
                            .padding()


                        // Remove button (only show if there's more than one time)
                        if index >= 1 {
                            Button(action: {
                                times.remove(at: index)
                            }) {
                                Image(HumaAssets.icInvalid.name, bundle: HumaFoundationBundle.bundle)
                                    .renderingMode(.template)
                                    .resizable()  // Make sure to add this first
                                    .frame(square: 24)
                                    .foregroundColor(Color.charcoalGrey)
                                    .padding()
                            }
                        }
                    }
                    .overlay(
                        RoundedRectangle(cornerRadius: Dimensions.cornerRadius)
                            .strokeBorder(validationError != nil ? Color.red : Color.veryLightGrey, style: StrokeStyle(lineWidth: Dimensions.lineWidth))
                    )
                }
            }

            // Add new time button
            HStack {
                Image(HumaAssets.plus.name, bundle: HumaFoundationBundle.bundle)

                Button("Add more") {
                    times.append(Date())
                }
                .underlineStyle()
            }
            
            if let error = validationError {
                Text(error)
                    .font(.footnote)
                    .foregroundColor(.red)
                    .padding(.horizontal, 4)
            }

            if !isMandatory {
                Button("Skip this question") {
                    viewModel.skipQuestion(for: question.id)
                }
                .padding(.top, 8)
                .foregroundColor(.blue)
            }

            // Submit button
            Button("Next") {
                if validateInput() {
                    submitAnswer()
                }
            }
            .pillStyle(.large, fullWidth: true)
            .disabled(isMandatory && times.isEmpty)
            .opacity(isMandatory && times.isEmpty ? 0.6 : 1.0)
            .padding(.top, 8)
        }
        .halfSheet(showSheet: $showTimePicker) {
            VStack(spacing: 0) {
                // Toolbar
                HStack {
                    Button("Cancel") {
                        showTimePicker = false
                    }
                    .foregroundColor(.blue)

                    Spacer()

                    Text("Select Time")
                        .font(.headline)

                    Spacer()

                    Button("Done") {
                        if let index = showTimePickerIndex {
                            times[index] = tempTime
                        }
                        showTimePickerIndex = nil
                        showTimePicker = false
                        // Clear validation error when time is set
                        validationError = nil
                    }
                    .foregroundColor(.blue)
                }
                .padding()
                .background(Color(.systemGroupedBackground))

                // Time picker
                DatePicker("", selection: $tempTime, displayedComponents: [.hourAndMinute])
                    .datePickerStyle(WheelDatePickerStyle())
                    .labelsHidden()
                    .padding()
            }
            .background(Color(.systemGroupedBackground))
        }
    }
    
    private func validateInput() -> Bool {
        // Clear previous errors
        validationError = nil
        
        let validTimes = times.compactMap { $0 }
        
        if validTimes.isEmpty && question.isMandatory {
            validationError = "Please select at least one time"
            return false
        }
        
        // For time entries, we typically validate each time
        for time in validTimes {
            for rule in question.validationRules {
                if !rule.validate(time) {
                    validationError = rule.errorMessage()
                    return false
                }
            }
        }
        
        return true
    }

    private func submitAnswer() {
        let validTimes = times.compactMap({ $0 })
        viewModel.submitAnswer(answer: .init(question: question, value: validTimes))
    }

    private func formattedTime(_ time: Date?) -> String {
        if let time = time {
            let formatter = DateFormatter()
            formatter.timeStyle = .short
            return formatter.string(from: time)
        } else {
            return "Select time"
        }
    }
}

private struct TimeQuestionView_Previews: View {

    @State var time: Date? = Date()

    var body: some View {
        TimeQuestionView(
            question: BaseQuestion(
                id: HemoProfileQuestionnaireSource.QuestionID.timeOfDay.rawValue,
                title: "Time of day",
                subtitle: nil,
                placeholder: nil,
                accessoryString: nil,
                type: .time,
                isMandatory: true,
                validationRules: [.required()],
                nextQuestionID: HemoProfileQuestionnaireSource.QuestionID.setReminder.rawValue,
                branchingLogic: nil
            ),
            viewModel: .init(questionSource: PreviewModels.hemoProfileQuestionSource),
            isMandatory: true,
            times: [Date()]
        )
    }
}

#Preview {
    TimeQuestionView_Previews()
        .padding()
}
