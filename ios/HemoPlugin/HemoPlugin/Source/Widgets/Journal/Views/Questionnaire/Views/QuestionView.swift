//
// QuestionView.swift
// HemoPlugin

// Created by <PERSON> on 01/05/2025.
// Copyright © 2025 Huma Therapeutics Ltd. All rights reserved.

import SwiftUI

struct QuestionView: View {
    let question: BaseQuestion
    @ObservedObject var viewModel: QuestionnaireViewModel
    let isLastVisible: Bool
    @State var focus: Bool = true

    var body: some View {
        VStack(alignment: .leading, spacing: 12) {
            Text(question.title)
                .font(.large)
                .foregroundColor(Color.charcoalGrey)

            if let subtitle = question.subtitle, !subtitle.isEmpty {
                HTMLText(text: subtitle)
                    .font(.default)
                    .padding(.bottom, 4)
            }
            
            switch question.type {
            case .text:
                TextQuestionView(
                    question: question,
                    value: nil,
                    viewModel: viewModel,
                    isMandatory: question.isMandatory
                )
            case .numeric:
                NumericQuestionView(
                    question: question,
                    value: nil,
                    viewModel: viewModel,
                    isMandatory: question.isMandatory,
                    isFocused: $focus
                )

            case .booleanChoice:
                BooleanChoiceQuestionView(
                    question: question,
                    viewModel: viewModel,
                    isMandatory: question.isMandatory,
                    selectedOption: nil
                )

            case .singleChoice:
                SingleChoiceQuestionView(
                    question: question,
                    options: viewModel.getOptions(for: question.id),
                    viewModel: viewModel,
                    isMandatory: question.isMandatory,
                    selectedOption: nil
                )
                
            case .multipleChoice(let choiceType):
                MultipleChoiceQuestionView(
                    question: question,
                    options: viewModel.getOptions(for: question.id),
                    viewModel: viewModel,
                    isMandatory: question.isMandatory,
                    choiceType: choiceType
                )
                
            case .date(let range):
                DateQuestionView(
                    question: question,
                    viewModel: viewModel,
                    isMandatory: question.isMandatory,
                    range: range,
                    date: nil
                )
                
            case .time:
                TimeQuestionView(
                    question: question,
                    viewModel: viewModel,
                    isMandatory: question.isMandatory,
                    times: [Date()]
                )
                
            case .autocompleteSearch:
                AutocompleteQuestionView(
                    question: question,
                    viewModel: viewModel,
                    isMandatory: question.isMandatory
                )
                
            case .picker:
                PickerQuestionView(
                    question: question,
                    viewModel: viewModel,
                    isMandatory: question.isMandatory,
                    options: viewModel.getOptions(for: question.id),
                    selection: nil
                )
            case .valueUnit:
                ValueUnitQuestionView(
                    question: question,
                    options: viewModel.getOptions(for: question.id),                    
                    isMandatory: question.isMandatory,
                    viewModel: viewModel,
                    value: nil,
                    selection: nil
                )
            case .multilineText:
                MultilineTextQuestionView(
                    question: question,
                    value: nil,
                    viewModel: viewModel,
                    isMandatory: question.isMandatory
                )
            case .slider:
                SliderQuestionView(
                    question: question,
                    viewModel: viewModel,
                    isMandatory: question.isMandatory,
                    range: 1...10,
                    startLabel: "No pain",
                    endLabel: "Severe"
                )

            case .photo:
                PhotoPickerQuestionView(
                    question: question,
                    viewModel: viewModel,
                    isMandatory: question.isMandatory
                )
            }
            
            if !question.isMandatory && isLastVisible {
                Button("Skip this question") {
                    focus = false
                    viewModel.skipQuestion(for: question.id)
                }
                .underlineStyle()
                .padding(.top, 8)
            }
        }
        .padding()
    }
}
