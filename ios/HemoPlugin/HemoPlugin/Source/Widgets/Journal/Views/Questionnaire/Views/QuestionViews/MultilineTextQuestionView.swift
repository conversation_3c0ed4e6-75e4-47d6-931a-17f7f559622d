//
// MultilineTextQuestionView.swift
// HemoPlugin

// Created by <PERSON> on 01/05/2025.
// Copyright © 2025 Huma Therapeutics Ltd. All rights reserved.
//

import SwiftUI

struct MultilineTextQuestionView: View {
    let question: Question
    @State var value: String?
    @ObservedObject var viewModel: QuestionnaireViewModel
    let isMandatory: Bool
    @FocusState private var isTextEditorFocused: Bool
    @State private var validationError: String?
    
    var body: some View {
        VStack(alignment: .leading, spacing: 8) {
            ZStack(alignment: .topLeading) {
                // Placeholder text that shows when TextEditor is empty
                if let value = value, value.isEmpty {
                    Text(question.placeholder ?? "")
                        .foregroundColor(Color.gray.opacity(0.8))
                        .font(.default)
                        .padding(.horizontal, 5)
                        .padding(.top, 8)
                }
                
                TextEditor(text: $value.toUnwrapped(defaultValue: ""))
                    .font(.default)
                    .focused($isTextEditorFocused)
                    .frame(minHeight: 100)
                    .padding(5)
                    .background(Color.white)
                    .foregroundColor(Color.charcoalGrey)
                    .onChange(of: value) { _ in
                        // Clear validation errors when user types
                        validationError = nil
                    }
            }
            .overlay(
                RoundedRectangle(cornerRadius: Dimensions.cornerRadius)
                    .strokeBorder(validationError != nil ? Color.red : Color.veryLightGrey, style: StrokeStyle(lineWidth: Dimensions.lineWidth))
            )
            .toolbar {
                ToolbarItem(placement: .keyboard) {
                    if isTextEditorFocused {
                        HStack {
                            Spacer()
                            Button("Done") {
                                if validateInput() {
                                    submitAnswer()
                                    isTextEditorFocused = false
                                }
                            }
                        }
                    }
                }
            }
            
            if let error = validationError {
                Text(error)
                    .font(.footnote)
                    .foregroundColor(.red)
                    .padding(.horizontal, 4)
            }
        }
        .onAppear {
            isTextEditorFocused = true
        }
    }

    private func validateInput() -> Bool {
        // Clear previous errors
        validationError = nil
        
        guard let value = value, !value.isEmpty else {
            if question.isMandatory {
                validationError = "This field is required"
                return false
            }
            return true
        }
        
        // Check against all validation rules
        for rule in question.validationRules {
            if !rule.validate(value) {
                validationError = rule.errorMessage()
                return false
            }
        }
        
        return true
    }
    
    private func submitAnswer() {
        viewModel.submitAnswer(answer: .init(question: question, value: value))
    }
}
