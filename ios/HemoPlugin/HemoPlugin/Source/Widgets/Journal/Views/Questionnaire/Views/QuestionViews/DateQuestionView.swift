//
//  DateQuestionView.swift
//  HemoPlugin
//
//  Created by <PERSON> on 01/05/2025.
//  Copyright © 2025 Huma Therapeutics Ltd. All rights reserved.
//

import SwiftUI

struct DateQuestionView: View {
    let question: Question
    @ObservedObject var viewModel: QuestionnaireViewModel
    let isMandatory: Bool
    let range: ClosedRange<Date>?
    @State var date: Date?
    @State private var showDatePicker: Bool? = nil
    @State private var tempDate: Date = Date()
    @State private var validationError: String?

    var formattedDate: String {
        if let date = date {
            let formatter = DateFormatter()
            formatter.dateStyle = .medium
            return formatter.string(from: date)
        } else {
            return "Select date"
        }
    }

    var body: some View {
        VStack(alignment: .leading, spacing: 8) {
            Button(action: {
                tempDate = date ?? Date()
                
                // Ensure initial date is within range constraints if they exist
                if let dateRange = range {
                    if tempDate < dateRange.lowerBound {
                        tempDate = dateRange.lowerBound
                    } else if tempDate > dateRange.upperBound {
                        tempDate = dateRange.upperBound
                    }
                }
                
                showDatePicker = true
            }) {
                HStack {
                    Text(formattedDate)
                        .frame(minWidth: 0, maxWidth: .infinity, alignment: .leading)
                        .font(.default)
                        .foregroundColor(date == nil ? .lightGrey3 : Color.charcoalGrey)
                        .padding()

                    Image(HumaAssets.icCalendar.name, bundle: HumaFoundationBundle.bundle)
                        .resizable()
                        .frame(square: 16)
                        .padding(.trailing, Dimensions.horizontalPadding)
                }
                .overlay(
                    RoundedRectangle(cornerRadius: Dimensions.cornerRadius)
                        .strokeBorder(validationError != nil ? Color.red : Color.veryLightGrey, style: StrokeStyle(lineWidth: Dimensions.lineWidth))
                )
            }
            
            if let error = validationError {
                Text(error)
                    .font(.footnote)
                    .foregroundColor(.red)
                    .padding(.horizontal, 4)
            }

            if !isMandatory {
                Button("Skip this question") {
                    viewModel.skipQuestion(for: question.id)
                }
                .padding(.top, 8)
                .foregroundColor(.blue)
            }
        }
        .halfSheet(showSheet: $showDatePicker) {
            VStack(spacing: 0) {
                // Toolbar
                HStack {
                    Button("Cancel") {
                        showDatePicker = false
                    }
                    .foregroundColor(.blue)

                    Spacer()

                    Text("Select Date")
                        .font(.headline)

                    Spacer()

                    Button("Done") {
                        date = tempDate
                        showDatePicker = false
                        if validateInput() {
                            viewModel.submitAnswer(answer: .init(question: question, value: date))
                        }
                    }
                    .foregroundColor(.blue)
                }
                .padding()
                .background(Color(.systemGroupedBackground))

                
                // Date picker with correct optional range handling
                if let dateRange = range {
                    DatePicker("", selection: $tempDate, in: dateRange, displayedComponents: [.date])
                        .datePickerStyle(WheelDatePickerStyle())
                        .labelsHidden()
                        .padding()
                } else {
                    DatePicker("", selection: $tempDate, displayedComponents: [.date])
                        .datePickerStyle(WheelDatePickerStyle())
                        .labelsHidden()
                        .padding()
                }
            }
            .background(Color(.systemGroupedBackground))
        }
    }
    
    private func validateInput() -> Bool {
        // Clear previous errors
        validationError = nil
        
        guard let selectedDate = date else {
            if question.isMandatory {
                validationError = "Please select a date"
                return false
            }
            return true
        }
        
        // Check if date is within the specified range
        if let dateRange = range {
            if selectedDate < dateRange.lowerBound {
                let formatter = DateFormatter()
                formatter.dateStyle = .medium
                validationError = "Date must be on or after \(formatter.string(from: dateRange.lowerBound))"
                return false
            }
            
            if selectedDate > dateRange.upperBound {
                let formatter = DateFormatter()
                formatter.dateStyle = .medium
                validationError = "Date must be on or before \(formatter.string(from: dateRange.upperBound))"
                return false
            }
        }
        
        // Check against all validation rules
        for rule in question.validationRules {
            if !rule.validate(selectedDate) {
                validationError = rule.errorMessage()
                return false
            }
        }
        
        return true
    }
}
