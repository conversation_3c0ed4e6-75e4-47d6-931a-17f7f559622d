//
//  BooleanChoiceQuestionView.swift
//  HemoPlugin
//
//  Created by <PERSON> on 01/05/2025.
//  Copyright © 2025 Huma Therapeutics Ltd. All rights reserved.
//

import SwiftUI

struct BooleanChoiceQuestionView: View {
    let question: Question
    private let options: [OptionItem] = [
        .init(value: "YES", title: HumaStrings.commonActionYes),
        .init(value: "NO", title: HumaStrings.commonActionNo)
    ]
    @ObservedObject var viewModel: QuestionnaireViewModel
    let isMandatory: Bool
    @State var selectedOption: OptionItem?

    var body: some View {
        VStack(alignment: .leading, spacing: 10) {
            ForEach(options, id: \.value) { option in
                Button(option.title) {
                    selectedOption = option
                    submitAnswers()
                }
                .pillStyle(selectedOption?.value == option.value ? .largeFill : .large, fullWidth: true)
            }
        }
    }

    private func submitAnswers() {
        if let selectedOption = selectedOption {
            viewModel.submitAnswer(answer: .init(question: question, value: selectedOption))
        }

    }
}
