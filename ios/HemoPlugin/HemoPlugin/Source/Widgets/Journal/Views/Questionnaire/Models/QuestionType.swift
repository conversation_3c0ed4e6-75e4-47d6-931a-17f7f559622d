//
// QuestionType.swift
// HemoPlugin

// Created by <PERSON> on 01/05/2025.
// Copyright © 2025 Huma Therapeutics Ltd. All rights reserved.
//

import Foundation

enum MultipleChoiceType {
    case `default`
    case circle
}

enum QuestionType {
    case text
    case booleanChoice
    case singleChoice
    case multipleChoice(MultipleChoiceType)
    case date(range: ClosedRange<Date>?)
    case time
    case autocompleteSearch
    case picker
    case numeric
    case valueUnit
    case multilineText
    case slider
    case photo
}
