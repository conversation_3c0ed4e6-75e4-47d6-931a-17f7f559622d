//
// PhotoPickerQuestionView.swift
// HemoPlugin

// Created by <PERSON> on 01/05/2025.
// Copyright © 2025 Huma Therapeutics Ltd. All rights reserved.

import SwiftUI
import PhotosUI

struct PhotoPickerQuestionView: View {
    let question: Question
    @ObservedObject var viewModel: QuestionnaireViewModel
    let isMandatory: Bool
    let maxPhotos: Int

    @State private var selectedPhotos: [UIImage] = []
    @State private var isShowingImagePicker = false
    @State private var isShowingCamera = false
    @State private var showSourceSheet: Bool? = nil
    @State private var validationError: String?

    init(question: Question, viewModel: QuestionnaireViewModel, isMandatory: Bool = true, maxPhotos: Int = 5) {
        self.question = question
        self.viewModel = viewModel
        self.isMandatory = isMandatory
        self.maxPhotos = maxPhotos
    }

    var body: some View {
        VStack(alignment: .leading, spacing: 12) {
            // Button to trigger photo selection
            But<PERSON>("Choose or take photo")
            {
                if selectedPhotos.count < maxPhotos {
                    showSourceSheet = true
                } else {
                    validationError = "Maximum \(maxPhotos) photos allowed"
                }
            }
            .pillStyle(.large, fullWidth: true)

            // Display selected photos
            if !selectedPhotos.isEmpty {
                ScrollView(.horizontal, showsIndicators: false) {
                    HStack(spacing: 12) {
                        ForEach(0..<selectedPhotos.count, id: \.self) { index in
                            photoThumbnail(image: selectedPhotos[index], index: index)
                        }
                    }
                    .padding(.vertical, 8)
                }
            }

            // Error message display
            if let error = validationError {
                Text(error)
                    .font(.footnote)
                    .foregroundColor(.red)
                    .padding(.horizontal, 4)
            }
        }
        .halfSheet(
            showSheet: $showSourceSheet,
            content: {
                VStack(spacing: Dimensions.spacing) {
                    Button("Take Photo")
                    {
                        isShowingCamera = true
                        showSourceSheet = false
                    }
                    .pillStyle(.large, fullWidth: true)

                    Button("Choose from Library")
                    {
                        isShowingImagePicker = true
                        showSourceSheet = false
                    }
                    .pillStyle(.large, fullWidth: true)

                    Button("Cancel") {
                        showSourceSheet = false
                    }
                    .pillStyle(.largeFill, fullWidth: true)
                    .padding(.top, Dimensions.verticalPadding)
                }
                .padding()
                .frame(maxWidth: .infinity, maxHeight: .infinity)
                .background(Color.white)
            },
            onDismiss: {
                // Handle dismissal if needed
            }
        )
        .sheet(isPresented: $isShowingImagePicker) {
            PHPickerView(selectedPhotos: $selectedPhotos, maxSelectionCount: maxPhotos - selectedPhotos.count) {
                validationError = nil
                submitAnswer()
            }
        }
        .fullScreenCover(isPresented: $isShowingCamera) {
            CameraView(selectedImage: { image in
                if let image = image {
                    selectedPhotos.append(image)
                    validationError = nil
                    submitAnswer()
                }
                isShowingCamera = false
            })
        }
//        .onAppear {
//            // Load stored images if available
//            if let storedAnswer = viewModel.getStoredAnswer(for: question.id),
//               let photoData = storedAnswer.photoData {
//                loadStoredPhotos(from: photoData)
//            }
//        }
    }

    private func photoThumbnail(image: UIImage, index: Int) -> some View {
        ZStack(alignment: .topTrailing) {
            Image(uiImage: image)
                .resizable()
                .scaledToFill()
                .frame(width: 100, height: 100)
                .clipShape(RoundedRectangle(cornerRadius: 8))
                .shadow(radius: 2)

            // Delete button
            Button(action: {
                selectedPhotos.remove(at: index)
                submitAnswer()
            }) {
                Image(HumaAssets.icTrashBin.name, bundle: HumaFoundationBundle.bundle)
                    .resizable()
                    .padding(4)
            }
            .padding(1)
            .frame(square: 24)
            .background(Circle().fill(Color.white))
        }
    }

    private func validateInput() -> Bool {
        validationError = nil

        if selectedPhotos.isEmpty && isMandatory {
            validationError = "At least one photo is required"
            return false
        }

        if selectedPhotos.count > maxPhotos {
            validationError = "Maximum \(maxPhotos) photos allowed"
            return false
        }

        return true
    }

    private func submitAnswer() {
        if validateInput() {
            // Convert images to Data for storage
            let photoDataArray = selectedPhotos.compactMap { $0.jpegData(compressionQuality: 0.7) }
            let answer = Answer(question: question, value: photoDataArray)
            viewModel.submitAnswer(answer: answer)
        }
    }

    private func loadStoredPhotos(from photoDataArray: [Data]) {
        selectedPhotos = photoDataArray.compactMap { UIImage(data: $0) }
    }
}

// MARK: - PHPicker View
struct PHPickerView: UIViewControllerRepresentable {
    @Binding var selectedPhotos: [UIImage]
    var maxSelectionCount: Int
    var onCompletion: () -> Void

    func makeUIViewController(context: Context) -> PHPickerViewController {
        var configuration = PHPickerConfiguration()
        configuration.selectionLimit = maxSelectionCount
        configuration.filter = .images

        let picker = PHPickerViewController(configuration: configuration)
        picker.delegate = context.coordinator
        return picker
    }

    func updateUIViewController(_ uiViewController: PHPickerViewController, context: Context) {}

    func makeCoordinator() -> Coordinator {
        Coordinator(self)
    }

    class Coordinator: NSObject, PHPickerViewControllerDelegate {
        let parent: PHPickerView

        init(_ parent: PHPickerView) {
            self.parent = parent
        }

        func picker(_ picker: PHPickerViewController, didFinishPicking results: [PHPickerResult]) {
            picker.dismiss(animated: true)
            
            // If no photos were selected, call completion and return
            if results.isEmpty {
                DispatchQueue.main.async {
                    self.parent.onCompletion()
                }
                return
            }
            
            // Create a dispatch group to track when all images are loaded
            let dispatchGroup = DispatchGroup()
            var loadedImages: [UIImage] = []
            
            // Process each selected photo
            for result in results {
                dispatchGroup.enter()
                result.itemProvider.loadObject(ofClass: UIImage.self) { reading, error in
                    defer { dispatchGroup.leave() }
                    guard let image = reading as? UIImage, error == nil else { return }
                    
                    DispatchQueue.main.async {
                        loadedImages.append(image)
                    }
                }
            }
            
            // When all images are loaded, update the selectedPhotos array at once
            dispatchGroup.notify(queue: .main) { [weak self] in
                self?.parent.selectedPhotos.append(contentsOf: loadedImages)
                self?.parent.onCompletion()
            }
        }
    }
}

// MARK: - Camera View
struct CameraView: UIViewControllerRepresentable {
    var selectedImage: (UIImage?) -> Void

    func makeUIViewController(context: Context) -> UIImagePickerController {
        let picker = UIImagePickerController()
        picker.sourceType = .camera
        picker.delegate = context.coordinator
        return picker
    }

    func updateUIViewController(_ uiViewController: UIImagePickerController, context: Context) {}

    func makeCoordinator() -> Coordinator {
        Coordinator(self)
    }

    class Coordinator: NSObject, UINavigationControllerDelegate, UIImagePickerControllerDelegate {
        let parent: CameraView

        init(_ parent: CameraView) {
            self.parent = parent
        }

        func imagePickerController(_ picker: UIImagePickerController, didFinishPickingMediaWithInfo info: [UIImagePickerController.InfoKey: Any]) {
            let image = info[.originalImage] as? UIImage
            parent.selectedImage(image)
        }

        func imagePickerControllerDidCancel(_ picker: UIImagePickerController) {
            parent.selectedImage(nil)
        }
    }
}

// MARK: - Preview Provider
struct PhotoPickerQuestionView_Previews: View {
    let sampleQuestion = BaseQuestion(
        id: AddBleedQuestionnaireSource.QuestionID.bleepPhotos.rawValue,
        title: "Would you like to add the photo?",
        subtitle: "A photo of the bleed can help your care team better understand what’s happening and guide treatment decisions.",
        placeholder: "Choose or take photo",
        accessoryString: nil,
        type: .photo,
        isMandatory: true,
        validationRules: [],
        nextQuestionID: nil,
        branchingLogic: nil
    )

    var body: some View {
        PhotoPickerQuestionView(
            question: sampleQuestion,
            viewModel: .init(questionSource: PreviewModels.hemoProfileQuestionSource)
        )
        .padding()
    }
}

#Preview {
    PhotoPickerQuestionView_Previews()
}
