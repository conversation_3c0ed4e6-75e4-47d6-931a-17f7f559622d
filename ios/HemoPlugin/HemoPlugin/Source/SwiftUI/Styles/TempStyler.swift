//
//  TempStyler.swift
//  HemoPlugin
//
//  Created by <PERSON> on 21/02/2025.
//  Copyright © 2025 Huma Therapeutics Ltd. All rights reserved.
//

import SwiftUI

// FIXME: - Move to HumaFoundation in next PRs

enum HumaColors: String, CaseIterable {
    case tealGreen = "#297A7A"
    case offWhiteBackground = "#F5F5F5"
    case veryLightGrey = "#EBEBEB"
    case lightGrey1 = "#6A6D72"
    case lightGrey2 = "#909499"
    case lightGrey3 = "#B7B8B9"
    case grey = "#2F3033"
    case charcoalGrey = "#424347"

    var color: Color {
        Color(hex: self.rawValue)
    }
}

extension Color {
    static let tealGreen = HumaColors.tealGreen.color
    static let offWhiteBackground = HumaColors.offWhiteBackground.color
    static let veryLightGrey = HumaColors.veryLightGrey.color
    static let lightGrey1 = HumaColors.lightGrey1.color
    static let lightGrey2 = HumaColors.light<PERSON>2.color
    static let lightGrey3 = HumaColors.lightGrey3.color
    static let grey = HumaColors.grey.color
    static let charcoalGrey = HumaColors.charcoalGrey.color
}

extension UIColor {
    static let charcoalGrey = UIColor(Color.charcoalGrey)
}

struct TempStyler {
    var color: TempColorStyle
    var appearance: TempAppearanceStyle

    static var `default`: Self {
        TempStyler(
            color: TempColorStyle(generalCard: commonColorStyle),
            appearance: TempAppearanceStyle(generalCard: commonAppearanceStyle)
        )
    }
}

struct TempColorStyle {
    var generalCard: TempColorProperties
}

struct TempAppearanceStyle {
    var generalCard: TempAppearanceProperties
}

struct TempColorProperties {
    var background: Color
    var shadowColor: Color
    var borderColor: Color
}

struct TempAppearanceProperties {
    var cornerRadius: CGFloat
    var shadowOffset: CGSize
    var shadowOpacity: Float
    var borderWidth: CGFloat
}

let commonAppearanceStyle = TempAppearanceProperties(
    cornerRadius: 18,
    shadowOffset: CGSize(width: 3, height: 3),
    shadowOpacity: 1,
    borderWidth: 2
)

let commonColorStyle = TempColorProperties(
    background: Color(hex: "#ffffff"),
    shadowColor: Color(hex: "#eeeef0"),
    borderColor: Color(hex: "#fbfbfb")
)

extension Color {
    init(hex: String) {
        // Remove any non-hex characters
        let hex = hex.trimmingCharacters(in: CharacterSet.alphanumerics.inverted)
        var int: UInt64 = 0

        // Scan the hex value
        Scanner(string: hex).scanHexInt64(&int)

        let r, g, b, a: UInt64
        switch hex.count {
        case 6: // RGB (24-bit)
            (r, g, b, a) = (int >> 16 & 0xFF, int >> 8 & 0xFF, int & 0xFF, 255)
        case 8: // ARGB (32-bit)
            (a, r, g, b) = (int >> 24 & 0xFF, int >> 16 & 0xFF, int >> 8 & 0xFF, int & 0xFF)
        default:
            // Default to black color for invalid hex strings
            (r, g, b, a) = (0, 0, 0, 255)
        }

        // Initialize the Color
        self.init(
            .sRGB,
            red: Double(r) / 255,
            green: Double(g) / 255,
            blue: Double(b) / 255,
            opacity: Double(a) / 255
        )
    }

    var cgColor: CGColor {
        UIColor(self).cgColor
    }
}

func executeOnMainThread(closure: @escaping () -> Void) {
    Thread.isMainThread ? closure() : DispatchQueue.main.async(execute: closure)
}

func executeOnMainThread(after delay: TimeInterval, closure: @escaping () -> Void) {
    if delay > .zero {
        DispatchQueue.main.asyncAfter(deadline: .now() + delay, execute: closure)
    } else {
        executeOnMainThread(closure: closure)
    }
}

extension CGSize {
    func scaled(_ value: CGFloat) -> CGSize {
        .init(width: width * value, height: height * value)
    }

    static func square(_ value: CGFloat) -> CGSize {
        .init(width: value, height: value)
    }
}

extension UIApplication {
    var currentWindow: UIWindow? {
        UIApplication.shared.connectedScenes
            .first(where: { $0.activationState == .foregroundActive })
            .orDefault(UIApplication.shared.connectedScenes.first)
            .flatMap { $0 as? UIWindowScene }
            .flatMap { $0?.windows.first }
    }
}

extension Optional {
    func orDefault(_ defaultValue: Wrapped) -> Wrapped {
        self ?? defaultValue
    }

    func orDefault(_ defaultValue: Self) -> Self {
        self ?? defaultValue
    }
}

extension EdgeInsets {
    static func oneValue(_ value: CGFloat) -> EdgeInsets  {
        .init(
            top: value,
            leading: value,
            bottom: value,
            trailing: value
        )
    }

    static func symmetric(vertical: CGFloat, horizontal: CGFloat) -> EdgeInsets {
        .init(
            top: vertical,
            leading: horizontal,
            bottom: vertical,
            trailing: horizontal
        )
    }
}

// `ContentState` is an enumeration that represents different states of content in a UI.
/// It is generic over `T`, which represents the type of content (e.g., data models) that is being loaded or displayed.
/// This enum conforms to `Equatable` to allow comparison between instances, especially useful in UI updates.
enum ContentState<T> {

    /// Represents a state where content is currently being loaded.
    case loading

    /// Represents a state where there is no content available.
    case empty

    /// Represents a state with content, encapsulating an array of items of type `T`.
    case results(T)
}

extension ContentState {
    /// A computed property that indicates whether the content state is empty.
    var isEmpty: Bool {
        switch self {
        case .empty:
            return true
        case .loading, .results:
            return false
        }
    }

    /// A computed property that indicates whether the content is currently being loaded.
    var isLoading: Bool {
        switch self {
        case .empty, .results:
            return false
        case .loading: /// It should be `true` for `results` to avoid showing empty state while dismissing.
            return true
        }
    }

    var result: T? {
        switch self {
        case let .results(result):
            return result
        case .loading, .empty:
            return nil
        }
    }

    /// A computed property indicating whether a placeholder view should be shown.
    /// This is typically true when the content is either empty or still loading.
    var shouldShowPlaceholder: Bool {
        switch self {
        case .empty, .loading:
            return true
        case .results:
            return false
        }
    }

    /// A computed property indicating whether the results view should be shown.
    /// This is true when the content state is not in a placeholder state.
    var shouldShowResults: Bool {
        !shouldShowPlaceholder
    }
}

// MARK: - Extensions

extension ContentState: Equatable where T: Equatable { }

extension ContentState where T: Sequence {
    var results: [T.Element] {
        switch self {
        case let .results(results):
            return Array(results)
        case .loading, .empty:
            return []
        }
    }

    mutating func append(contentsOf array: [T.Element]) {
        let appendedResult = results + array
        (appendedResult as? T).map { self = .results($0) }
    }

    mutating func update(value: T.Element, at index: Int) {
        var newResults = results
        newResults[index] = value
        setResult(newResults)
    }

    mutating func setResult(_ results: [T.Element]) {
        if !results.isEmpty, let result = results as? T {
            self = .results(result)
        } else {
            self = .empty
        }
    }

    mutating func remove(at index: Int) {
        var newResults = results
        newResults.remove(at: index)
        setResult(newResults)
    }
}

extension ContentState {
    mutating func update(_ update: (inout T) -> Void) {
        guard var result else { return }
        update(&result)
        self = .results(result)
    }
}
