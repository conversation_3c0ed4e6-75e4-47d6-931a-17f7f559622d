//
//  Configuration.swift
//  HumaFoundationMocks
//
//  Created by <PERSON><PERSON><PERSON> on 27.11.2020.
//  Copyright © 2020 Huma Therapeutics Ltd. All rights reserved.
//

import Foundation
import HumaFoundation

/// Implementation of `AnyConfiguration` that can be used by test targets.

public struct MockConfiguration: AnyConfiguration {
    public var status: ConfigurationStatus?
    public var isHumaWatchEnabled: Bool = false
    public var isHealthAppsEnabled: Bool
    public var mfaRequired: Bool = true
    public var deploymentID: String = ""
    public var consent: ConsentForm?
    public var eConsent: EConsentForm?
    public var learn: AnyLearnContent? { learnContent }
    public var icon: FileObject?
    public var eulaURL: URL?
    public var privacyPolicyURL: URL?
    public var groupedModuleConfigs: [GroupedModuleConfig] = []
    public var nextOnboardingTaskID: String?
    public var onboardingConfigs: [OnboardingTaskConfig]
    public var contactUsURL: URL?
    public var isOffBoarded: Bool
    public var learnContent: LearnContent?
    private let moduleConfigs: [ModuleConfig]
    public var defaultUnits: [String: String]?
    public let features: Features
    public var staticEventConfig: StaticEventConfig?
    public let productLabelURL: URL?
    public let isClassBCompliant: Bool
    public let isSummaryReportEnabled: Bool
    public var isMealBasedBloodGlucoseEnabled: Bool = false

    public init(
        deploymentID: String = "",
        consent: ConsentForm? = nil,
        eConsent: EConsentForm? = nil,
        learn: LearnContent? = nil,
        icon: FileObject? = nil,
        eulaURL: URL? = nil,
        privacyPolicyURL: URL? = nil,
        moduleConfigs: [ModuleConfig] = [.init(id: "", moduleID: "HemophiliaJournal")],
        mfaRequired: Bool = true,
        onboardingConfigs: [OnboardingTaskConfig] = [],
        nextOnboardingTaskID: String? = nil,
        isOffBoarded: Bool = false,
        isHealthAppsEnabled: Bool = true,
        staticEventConfig: StaticEventConfig? = nil,
        productLabelURL: URL? = nil,
        isClassBCompliant: Bool = false,
        isSummaryReportEnabled: Bool = false
    ) {
        self.deploymentID = deploymentID
        self.consent = consent
        self.eConsent = eConsent
        self.learnContent = learn
        self.icon = icon
        self.eulaURL = eulaURL
        self.privacyPolicyURL = privacyPolicyURL
        self.moduleConfigs = moduleConfigs
        self.groupedModuleConfigs = moduleConfigs.map { GroupedModuleConfig(configs: [$0]) }
        self.mfaRequired = mfaRequired
        self.nextOnboardingTaskID = nextOnboardingTaskID
        self.onboardingConfigs = onboardingConfigs
        self.isOffBoarded = isOffBoarded
        self.defaultUnits = [:]
        self.isHealthAppsEnabled = isHealthAppsEnabled
        self.features = .init()
        self.staticEventConfig = staticEventConfig
        self.productLabelURL = productLabelURL
        self.isClassBCompliant = isClassBCompliant
        self.isSummaryReportEnabled = isSummaryReportEnabled
    }
}

// MARK: - MedopadIdentifiable

extension MockConfiguration: HumaIdentifiable {
    public var id: Identifier<Self> { .init(rawValue: deploymentID) }

    public var isGoalFeatureEnabled: Bool { features.patientPersonalization.goal }

    public struct Features: Codable {
        struct Personalization: Codable, SafelyDecodable {
            @SafelyDecoded
            private(set) var goal: Bool = false

            static var decodingFallback: Self { .init(goal: false) }
        }

        @SafelyDecoded var patientPersonalization: Personalization = .decodingFallback
    }
}

// MARK: - Codable

extension MockConfiguration: Codable {
    private enum CodingKeys: String, CodingKey {
        case id
        case deploymentID = "deploymentId"
        case icon
        case consent
        case eConsent = "econsent"
        case learn
        case eulaURL = "eulaUrl"
        case privacyPolicyURL = "privacyPolicyUrl"
        case moduleConfigs
        case onboardingConfigs
        case nextOnboardingTaskID = "nextOnboardingTaskId"
        case contactUsURL
        case isOffBoarded
        case isHealthAppsEnabled
        case features
    }

    public init(from decoder: Decoder) throws {
        let container = try decoder.container(keyedBy: CodingKeys.self)
        deploymentID = try container.decode(forKey: .deploymentID)
        consent = try container.decodeIfPresent(forKey: .consent)
        eConsent = try container.decodeIfPresent(forKey: .eConsent)
        learnContent = try container.decodeIfPresent(forKey: .learn)
        icon = try container.decodeIfPresent(forKey: .icon)
        eulaURL = try container.decodeIfPresent(forKey: .eulaURL)
        privacyPolicyURL = try container.decodeIfPresent(forKey: .privacyPolicyURL)
        contactUsURL = try container.decodeIfPresent(forKey: .contactUsURL)
        moduleConfigs = container.decodeSafely([ModuleConfig].self, forKey: .moduleConfigs)
        groupedModuleConfigs = moduleConfigs.map { GroupedModuleConfig(configs: [$0]) }
        onboardingConfigs = container.decodeSafely([OnboardingTaskConfig].self, forKey: .onboardingConfigs)
        nextOnboardingTaskID = try container.decodeIfPresent(forKey: .nextOnboardingTaskID)
        isOffBoarded = try container.decodeIfPresent(forKey: .isOffBoarded) ?? false
        isHealthAppsEnabled = try container.decodeIfPresent(forKey: .isHealthAppsEnabled) ?? false
        features = try container.decodeIfPresent(forKey: .features) ?? Features()
        productLabelURL = nil
        isClassBCompliant = false
        isSummaryReportEnabled = false
    }

    public func encode(to encoder: Encoder) throws {
        var container = encoder.container(keyedBy: CodingKeys.self)
        try container.encode(deploymentID, forKey: .deploymentID)
        try container.encode(moduleConfigs, forKey: .moduleConfigs)
        try container.encode(onboardingConfigs, forKey: .onboardingConfigs)
        try container.encodeIfPresent(consent, forKey: .consent)
        try container.encodeIfPresent(eConsent, forKey: .eConsent)
        try container.encodeIfPresent(learnContent, forKey: .learn)
        try container.encodeIfPresent(icon, forKey: .icon)
        try container.encodeIfPresent(eulaURL, forKey: .eulaURL)
        try container.encodeIfPresent(privacyPolicyURL, forKey: .privacyPolicyURL)
        try container.encodeIfPresent(contactUsURL, forKey: .contactUsURL)
        try container.encodeIfPresent(nextOnboardingTaskID, forKey: .nextOnboardingTaskID)
        try container.encodeIfPresent(isOffBoarded, forKey: .isOffBoarded)
        try container.encodeIfPresent(features, forKey: .features)
    }
}
