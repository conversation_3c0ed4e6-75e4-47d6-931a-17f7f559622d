//
//  MockModuleResultRepository.swift
//  Pods
//
//  Created by <PERSON> on 13/05/2025.
//  Copyright © 2020 Huma Therapeutics Ltd. All rights reserved.
//

import HumaFoundation

final class MockModuleResultRepository: AnyModuleResultObserveRepository {
    func getModuleResults<Primitive: AnyCodablePrimitive>(
        moduleID: String,
        moduleConfigID: String?,
        limit: Int,
        excludedFields: [String]?,
        completion: @escaping RepositoryCompletion<[ModuleResult<Primitive>]>
    ) {
        
    }

    func getLatestModuleResult<Primitive: AnyCodablePrimitive>(
        moduleID: String,
        moduleConfigID: String?,
        excludedFields: [String]?,
        completion: @escaping RepositoryCompletion<ModuleResult<Primitive>>
    ) {
        
    }

    func observeModuleResultCount<P: AnyCodablePrimitive>(
        primitiveType: P.Type,
        moduleID: String,
        moduleConfigID: String?,
        listener: @escaping (Int) -> Void
    ) -> Disposable {
        return AnyDisposable()
    }
}
