//
//  PreviewModels.swift
//  HemoPlugin
//
//  Created by <PERSON> on 10/04/2025.
//  Copyright © 2025 Huma Therapeutics Ltd. All rights reserved.
//

import Foundation

enum PreviewModels {
    static let bodyMapVM = BodyMapViewModel(
        bodyMapColor: widgetDataResponseActive.bodyMapColor ?? [],
        legend: widgetDataResponseActive.legend,
        jointData: journalWidgetConfig.jointData,
        allowInteraction: true,
        legendText: "Data shown from the past 6 months",
        selectedBodyPoint: nil
    )

    static let bodyMapVMSetupRequired = BodyMapViewModel(
        bodyMapColor: widgetDataResponseActive.bodyMapColor ?? [],
        legend: widgetDataResponseActive.legend,
        jointData: journalWidgetConfig.jointData,
        allowInteraction: true,
        legendText: "Data shown from the past 6 months",
        selectedBodyPoint: nil
    )

    static let setupProfileCardVM = SetupProfileCardViewModel(
        headline: widgetDataResponseSetupRequire.title,
        bodyText: widgetDataResponseSetupRequire.description,
        primaryButtonTitle: widgetDataResponseSetupRequire.primaryCTAtext
    )

    static let activeProfileCardVM = ActiveProfileCardViewModel(
        primaryButtonTitle: widgetDataResponseActive.primaryCTAtext,
        secondaryButtonTitle: widgetDataResponseActive.secondaryCTAtext
    )

    static let journalHeaderVM = PreviewHeaderViewModel(
        title: journalWidgetConfig.header.title,
        logoImageName: journalWidgetConfig.header.icon
    )

    static let addBleedVM: AddBleedViewModel = .init(
        bodyMapViewModel: PreviewModels.bodyMapVM,
        medicationRepository: MockMedicationRepository(),
        moduleResultSubmitRepository: MockModuleResultSubmitRepository(),
        deploymentConfig: MockConfiguration(),
        userRepository: MockUserObserveRepository(),
        fileRepository: MockFileRepository()
    )

    static let hemoProfileQuestionSource = HemoProfileQuestionnaireSource(repository: MockMedicationRepository())

    static var journalWidgetConfig: HemoJournalWidgetConfig {
        let jsonData = """
                    {
                                  "moduleId": "HemophiliaJournal",
                                  "moduleConfigId": "67ea95e17c1c1684c8bbf079",
                                  "header": {
                                    "title": "Hi Jay",
                                    "icon": "670526cd767ecb0cd2fa4251"
                                  },
                                  "bodyMap": [
                                    {
                                      "bleedType": "JOINTS",
                                      "title": "Hemophilia.JOINTS.title",
                                      "description": "Hemophilia.JOINTS.description",
                                      "locations": [
                                        {
                                          "location": "RIGHT_WRIST",
                                          "points": [
                                            {
                                              "name": "Wrist (right)",
                                              "value": "JOINT_RIGHT_WRIST"
                                            },
                                            {
                                              "name": "Muscular bleed of arm (right)",
                                              "value": "MUSCLE_RIGHT_ARM"
                                            },
                                            {
                                              "name": "Surface bleed of arm (right)",
                                              "value": "SURFACE_RIGHT_ARM"
                                            }
                                          ]
                                        },
                                        {
                                          "location": "LEFT_WRIST",
                                          "points": [
                                            {
                                              "name": "Wrist (left)",
                                              "value": "JOINT_LEFT_WRIST"
                                            },
                                            {
                                              "name": "Muscular bleed of arm (left)",
                                              "value": "MUSCLE_LEFT_ARM"
                                            },
                                            {
                                              "name": "Surface bleed of arm (left)",
                                              "value": "SURFACE_LEFT_ARM"
                                            }
                                          ]
                                        },
                                        {
                                          "location": "RIGHT_ELBOW",
                                          "points": [
                                            {
                                              "name": "Elbow (right)",
                                              "value": "JOINT_RIGHT_ELBOW"
                                            },
                                            {
                                              "name": "Muscular bleed of arm (right)",
                                              "value": "MUSCLE_RIGHT_ARM"
                                            },
                                            {
                                              "name": "Surface bleed of arm (right)",
                                              "value": "SURFACE_RIGHT_ARM"
                                            }
                                          ]
                                        },
                                        {
                                          "location": "LEFT_ELBOW",
                                          "points": [
                                            {
                                              "name": "Elbow (left)",
                                              "value": "JOINT_LEFT_ELBOW"
                                            },
                                            {
                                              "name": "Muscular bleed of arm (left)",
                                              "value": "MUSCLE_LEFT_ARM"
                                            },
                                            {
                                              "name": "Surface bleed of arm (left)",
                                              "value": "SURFACE_LEFT_ARM"
                                            }
                                          ]
                                        },
                                        {
                                          "location": "RIGHT_SHOULDER",
                                          "points": [
                                            {
                                              "name": "Shoulder (right)",
                                              "value": "JOINT_RIGHT_SHOULDER"
                                            },
                                            {
                                              "name": "Muscular bleed of arm (right)",
                                              "value": "MUSCLE_RIGHT_ARM"
                                            },
                                            {
                                              "name": "Surface bleed of arm (right)",
                                              "value": "SURFACE_RIGHT_ARM"
                                            }
                                          ]
                                        },
                                        {
                                          "location": "LEFT_SHOULDER",
                                          "points": [
                                            {
                                              "name": "Shoulder (left)",
                                              "value": "JOINT_LEFT_SHOULDER"
                                            },
                                            {
                                              "name": "Muscular bleed of arm (left)",
                                              "value": "MUSCLE_LEFT_ARM"
                                            },
                                            {
                                              "name": "Surface bleed of arm (left)",
                                              "value": "SURFACE_LEFT_ARM"
                                            }
                                          ]
                                        },
                                        {
                                          "location": "RIGHT_HIP",
                                          "points": [
                                            {
                                              "name": "Hip (right)",
                                              "value": "JOINT_RIGHT_HIP"
                                            },
                                            {
                                              "name": "Muscular bleed of leg (right)",
                                              "value": "MUSCLE_RIGHT_LEG"
                                            },
                                            {
                                              "name": "Surface bleed of leg (right)",
                                              "value": "SURFACE_RIGHT_LEG"
                                            }
                                          ]
                                        },
                                        {
                                          "location": "LEFT_HIP",
                                          "points": [
                                            {
                                              "name": "Hip (left)",
                                              "value": "JOINT_LEFT_HIP"
                                            },
                                            {
                                              "name": "Muscular bleed of left (left)",
                                              "value": "MUSCLE_LEFT_LEG"
                                            },
                                            {
                                              "name": "Surface bleed of lef (left)",
                                              "value": "SURFACE_LEFT_LEG"
                                            }
                                          ]
                                        },
                                        {
                                          "location": "RIGHT_KNEE",
                                          "points": [
                                            {
                                              "name": "Knee (right)",
                                              "value": "JOINT_RIGHT_KNEE"
                                            },
                                            {
                                              "name": "Muscular bleed of leg (right)",
                                              "value": "MUSCLE_RIGHT_LEG"
                                            },
                                            {
                                              "name": "Surface bleed of leg (right)",
                                              "value": "SURFACE_RIGHT_LEG"
                                            }
                                          ]
                                        },
                                        {
                                          "location": "LEFT_KNEE",
                                          "points": [
                                            {
                                              "name": "Knee (left)",
                                              "value": "JOINT_LEFT_KNEE"
                                            },
                                            {
                                              "name": "Muscular bleed of left (left)",
                                              "value": "MUSCLE_LEFT_LEG"
                                            },
                                            {
                                              "name": "Surface bleed of lef (left)",
                                              "value": "SURFACE_LEFT_LEG"
                                            }
                                          ]
                                        },
                                        {
                                          "location": "RIGHT_ANKLE",
                                          "points": [
                                            {
                                              "name": "Ankle (right)",
                                              "value": "JOINT_RIGHT_ANKLE"
                                            },
                                            {
                                              "name": "Muscular bleed of leg (right)",
                                              "value": "MUSCLE_RIGHT_LEG"
                                            },
                                            {
                                              "name": "Surface bleed of leg (right)",
                                              "value": "SURFACE_RIGHT_LEG"
                                            }
                                          ]
                                        },
                                        {
                                          "location": "LEFT_ANKLE",
                                          "points": [
                                            {
                                              "name": "Ankle (left)",
                                              "value": "JOINT_LEFT_ANKLE"
                                            },
                                            {
                                              "name": "Muscular bleed of left (left)",
                                              "value": "MUSCLE_LEFT_LEG"
                                            },
                                            {
                                              "name": "Surface bleed of lef (left)",
                                              "value": "SURFACE_LEFT_LEG"
                                            }
                                          ]
                                        }
                                      ]
                                    },
                                    {
                                      "bleedType": "NON_JOINTS",
                                      "title": "Hemophilia.NON_JOINTS.title",
                                      "description": "Hemophilia.NON_JOINTS.description",
                                      "locations": [
                                        {
                                          "location": "OTHER",
                                          "points": [
                                            {
                                              "name": "Gum bleed",
                                              "value": "GUMS_BLEED"
                                            },
                                            {
                                              "name": "Nose bleed",
                                              "value": "NOSE_BLEED"
                                            },
                                            {
                                              "name": "Menstrual",
                                              "value": "MENSTRUAL"
                                            },
                                            {
                                              "name": "Blood in stool",
                                              "value": "BLOOD_IN_STOOL"
                                            },
                                            {
                                              "name": "Blood in urine",
                                              "value": "BLOOD_IN_URINE"
                                            },
                                            {
                                              "name": "Hemophilia.body.CUSTOM",
                                              "value": "CUSTOM"
                                            }
                                          ]
                                        }
                                      ]
                                    }
                                  ]
                                }
    """
            .data(using: .utf8)!
        return (try? JSONDecoder().decode(from: jsonData, HemoJournalWidgetConfig.self))!
    }

    static var widgetDataResponseActive: WidgetDataResponse {
        let jsonData = """
                    {
                      "title": "No bleeds yet",
                      "description": "Track bleeds to better understand your condition. Add your first now.",
                      "primaryCTAtext": "Add new bleed",
                      "secondaryCTAtext": "View history",
                      "state": "ACTIVE",
                      "legend": [
                        {
                          "color": "#909499",
                          "label": "No bleeds"
                        },
                        {
                          "color": "#FA8333",
                          "label": "1-9 bleeds"
                        },
                        {
                          "color": "#EB0037",
                          "label": "10+ bleeds"
                        }
                      ],
                      "bodyMapColor": [
                        {
                          "location": "RIGHT_WRIST",
                          "color": "#909499"
                        },
                        {
                          "location": "RIGHT_WRIST",
                          "color": "#909499"
                        },
                        {
                          "location": "RIGHT_WRIST",
                          "color": "#909499"
                        },
                        {
                          "location": "RIGHT_WRIST",
                          "color": "#909499"
                        },
                        {
                          "location": "RIGHT_WRIST",
                          "color": "#909499"
                        },
                        {
                          "location": "RIGHT_WRIST",
                          "color": "#909499"
                        }
                      ]
                    }
    """
            .data(using: .utf8)!

        return (try? JSONDecoder().decode(from: jsonData, WidgetDataResponse.self))!
    }

    static var widgetDataResponseSetupRequire: WidgetDataResponse {
        let jsonData = """
                    {
                      "title": "Set up your hemophilia details",
                      "description": "You haven’t added your hemophilia details yet. Set them up to track bleeds and pain.",
                      "primaryCTAtext": "Set up",
                      "state": "SETUP_REQUIRED",
                      "bodyMapColor": [
                        {
                          "location": "LEFT_WRIST",
                          "color": "#EBEBEB"
                        },
                        {
                          "location": "RIGHT_WRIST",
                          "color": "#EBEBEB"
                        },
                        {
                          "location": "LEFT_ELBOW",
                          "color": "#EBEBEB"
                        },
                        {
                          "location": "RIGHT_ELBOW",
                          "color": "#EBEBEB"
                        },
                        {
                          "location": "LEFT_SHOULDER",
                          "color": "#EBEBEB"
                        },
                        {
                          "location": "RIGHT_SHOULDER",
                          "color": "#EBEBEB"
                        },
                        {
                          "location": "LEFT_HIP",
                          "color": "#EBEBEB"
                        },
                        {
                          "location": "RIGHT_HIP",
                          "color": "#EBEBEB"
                        },
                        {
                          "location": "LEFT_KNEE",
                          "color": "#EBEBEB"
                        },
                        {
                          "location": "RIGHT_KNEE",
                          "color": "#EBEBEB"
                        },
                        {
                          "location": "LEFT_ANKLE",
                          "color": "#EBEBEB"
                        },
                        {
                          "location": "RIGHT_ANKLE",
                          "color": "#EBEBEB"
                        },
                        {
                          "location": "OTHER",
                          "color": "#EBEBEB"
                        }
                      ]
                    }
    """
            .data(using: .utf8)!

        return (try? JSONDecoder().decode(from: jsonData, WidgetDataResponse.self))!
    }

    static var createObjectResponse: CreateObjectResponse {
        let jsonData = """
                    {
                      "id": "243213432"
                    }
    """
            .data(using: .utf8)!

        return (try? JSONDecoder().decode(from: jsonData, CreateObjectResponse.self))!
    }
}
