//
//  MockMedicationRepository.swift
//  HemoPlugin
//
//  Created by <PERSON> on 01/05/2025.
//  Copyright © 2025 Huma Therapeutics Ltd. All rights reserved.
//

import HumaFoundation

final class MockMedicationRepository: AnyMedicaitionRepository {

    func getCMSMedication(
        completion: @escaping RepositoryCompletion<CMSMedicationResponse>
    ) {
        completion(.success(CMSMedicationResponse(items: medicationItems)))
    }

    func submitHemoProfileQuestionnaire(
        requestBody: HemoProfileQuestionnaireRequest,
        completion: @escaping RepositoryCompletion<CreateObjectResponse>
    ) {
        completion(.success(createObjectResponse))
    }
}

private extension MockMedicationRepository {
    var medicationItems: [CMSMedicationResponse.MedicationItem] {
        let jsonData = """
[
    {
      "id": "6810d09221bc04c2a7ea1f51",
      "status": "PUBLISHED",
      "data": {
        "unit": "IU / kg",
        "title": "Alprolix",
        "dosage": 100,
        "frequency": "once in two weeks"
      },
      "schema": "medications",
      "tags": [
        "Prophylactic"
      ],
      "resources": [
        "deployment/66ed50384fd3ec6700cae46f",
        "deployment/66f174ad8d06fd56dac30321",
        "deployment/66f174ad8d06fd56dac30321",
        "deployment/66f174ad8d06fd56dac30321",
        "deployment/66f174ad8d06fd56dac30321",
        "deployment/66f174ad8d06fd56dac30321",
        "deployment/66f174ad8d06fd56dac30321",
        "deployment/66f174ad8d06fd56dac30321",
        "deployment/66f174ad8d06fd56dac30321",
        "deployment/66f174ad8d06fd56dac30321"
      ],
      "version": 2,
      "updateDateTime": "2025-04-29T14:44:26.380189Z",
      "publishDateTime": "2025-04-29T14:44:50.321192Z",
      "createDateTime": "2025-04-29T13:13:54.003757Z"
    },
    {
      "id": "6810d20ad3606c8af1e99843",
      "status": "PUBLISHED",
      "data": {
        "unit": "None",
        "title": "Esperoct",
        "dosage": 0
      },
      "schema": "medications",
      "tags": [
        "OnDemand"
      ],
      "resources": [
        "deployment/66ed50384fd3ec6700cae46f",
        "deployment/66f174ad8d06fd56dac30321",
        "deployment/66f174ad8d06fd56dac30321",
        "deployment/66f174ad8d06fd56dac30321",
        "deployment/66f174ad8d06fd56dac30321",
        "deployment/66f174ad8d06fd56dac30321",
        "deployment/66f174ad8d06fd56dac30321",
        "deployment/66f174ad8d06fd56dac30321",
        "deployment/66f174ad8d06fd56dac30321",
        "deployment/66f174ad8d06fd56dac30321"
      ],
      "version": 1,
      "updateDateTime": "2025-04-29T13:20:10.128723Z",
      "publishDateTime": "2025-04-29T13:21:33.964860Z",
      "createDateTime": "2025-04-29T13:20:10.128723Z"
    },
    {
      "id": "6810d0b8d3606c8af1e993a7",
      "status": "PUBLISHED",
      "data": {
        "unit": "IU / kg",
        "title": "BeneFIX",
        "dosage": 120,
        "frequency": "once a week"
      },
      "schema": "medications",
      "tags": [
        "Prophylactic"
      ],
      "resources": [
        "deployment/66ed50384fd3ec6700cae46f",
        "deployment/66f174ad8d06fd56dac30321",
        "deployment/66f174ad8d06fd56dac30321",
        "deployment/66f174ad8d06fd56dac30321",
        "deployment/66f174ad8d06fd56dac30321",
        "deployment/66f174ad8d06fd56dac30321",
        "deployment/66f174ad8d06fd56dac30321",
        "deployment/66f174ad8d06fd56dac30321",
        "deployment/66f174ad8d06fd56dac30321",
        "deployment/66f174ad8d06fd56dac30321"
      ],
      "version": 2,
      "updateDateTime": "2025-04-29T14:44:16.020446Z",
      "publishDateTime": "2025-04-29T14:44:50.346767Z",
      "createDateTime": "2025-04-29T13:14:32.140278Z"
    },
    {
      "id": "6810d06e17ce1509eae98910",
      "status": "PUBLISHED",
      "data": {
        "unit": "IU / kg",
        "title": "AlphaNine SD Coagulation Factor IX,",
        "dosage": 150,
        "frequency": "once a week"
      },
      "schema": "medications",
      "tags": [
        "Prophylactic"
      ],
      "resources": [
        "deployment/66ed50384fd3ec6700cae46f",
        "deployment/66f174ad8d06fd56dac30321",
        "deployment/66f174ad8d06fd56dac30321",
        "deployment/66f174ad8d06fd56dac30321",
        "deployment/66f174ad8d06fd56dac30321",
        "deployment/66f174ad8d06fd56dac30321",
        "deployment/66f174ad8d06fd56dac30321",
        "deployment/66f174ad8d06fd56dac30321",
        "deployment/66f174ad8d06fd56dac30321",
        "deployment/66f174ad8d06fd56dac30321"
      ],
      "version": 2,
      "updateDateTime": "2025-04-29T14:44:35.325188Z",
      "publishDateTime": "2025-04-29T14:44:50.242302Z",
      "createDateTime": "2025-04-29T13:13:18.237817Z"
    },
    {
      "id": "6810d23d3c87788e95367e32",
      "status": "PUBLISHED",
      "data": {
        "unit": "None",
        "title": "Idelvion",
        "dosage": 0
      },
      "schema": "medications",
      "tags": [
        "OnDemand"
      ],
      "resources": [
        "deployment/66ed50384fd3ec6700cae46f",
        "deployment/66f174ad8d06fd56dac30321",
        "deployment/66f174ad8d06fd56dac30321",
        "deployment/66f174ad8d06fd56dac30321",
        "deployment/66f174ad8d06fd56dac30321",
        "deployment/66f174ad8d06fd56dac30321",
        "deployment/66f174ad8d06fd56dac30321",
        "deployment/66f174ad8d06fd56dac30321",
        "deployment/66f174ad8d06fd56dac30321",
        "deployment/66f174ad8d06fd56dac30321"
      ],
      "version": 1,
      "updateDateTime": "2025-04-29T13:21:01.179791Z",
      "publishDateTime": "2025-04-29T13:21:34.015251Z",
      "createDateTime": "2025-04-29T13:21:01.179791Z"
    },
    {
      "id": "6810d25121bc04c2a7ea20ed",
      "status": "PUBLISHED",
      "data": {
        "unit": "None",
        "title": "Jivi",
        "dosage": 0
      },
      "schema": "medications",
      "tags": [
        "OnDemand"
      ],
      "resources": [
        "deployment/66ed50384fd3ec6700cae46f",
        "deployment/66f174ad8d06fd56dac30321",
        "deployment/66f174ad8d06fd56dac30321",
        "deployment/66f174ad8d06fd56dac30321",
        "deployment/66f174ad8d06fd56dac30321",
        "deployment/66f174ad8d06fd56dac30321",
        "deployment/66f174ad8d06fd56dac30321",
        "deployment/66f174ad8d06fd56dac30321",
        "deployment/66f174ad8d06fd56dac30321",
        "deployment/66f174ad8d06fd56dac30321"
      ],
      "version": 1,
      "updateDateTime": "2025-04-29T13:21:21.196623Z",
      "publishDateTime": "2025-04-29T13:21:34.029868Z",
      "createDateTime": "2025-04-29T13:21:21.196623Z"
    },
    {
      "id": "6810d0d817ce1509eae98911",
      "status": "PUBLISHED",
      "data": {
        "unit": "IU / kg",
        "title": "Beqvez (fidanacogene elaparvovec),",
        "dosage": 140,
        "frequency": "Once in two weeks"
      },
      "schema": "medications",
      "tags": [
        "Prophylactic"
      ],
      "resources": [
        "deployment/66ed50384fd3ec6700cae46f",
        "deployment/66f174ad8d06fd56dac30321",
        "deployment/66f174ad8d06fd56dac30321",
        "deployment/66f174ad8d06fd56dac30321",
        "deployment/66f174ad8d06fd56dac30321",
        "deployment/66f174ad8d06fd56dac30321",
        "deployment/66f174ad8d06fd56dac30321",
        "deployment/66f174ad8d06fd56dac30321",
        "deployment/66f174ad8d06fd56dac30321",
        "deployment/66f174ad8d06fd56dac30321"
      ],
      "version": 2,
      "updateDateTime": "2025-04-29T14:44:01.009204Z",
      "publishDateTime": "2025-04-29T14:44:50.363467Z",
      "createDateTime": "2025-04-29T13:15:04.116023Z"
    },
    {
      "id": "6810d223f8ee4f042f3679a0",
      "status": "PUBLISHED",
      "data": {
        "unit": "None",
        "title": "Hemgenix",
        "dosage": 0
      },
      "schema": "medications",
      "tags": [
        "OnDemand"
      ],
      "resources": [
        "deployment/66ed50384fd3ec6700cae46f",
        "deployment/66f174ad8d06fd56dac30321",
        "deployment/66f174ad8d06fd56dac30321",
        "deployment/66f174ad8d06fd56dac30321",
        "deployment/66f174ad8d06fd56dac30321",
        "deployment/66f174ad8d06fd56dac30321",
        "deployment/66f174ad8d06fd56dac30321",
        "deployment/66f174ad8d06fd56dac30321",
        "deployment/66f174ad8d06fd56dac30321",
        "deployment/66f174ad8d06fd56dac30321"
      ],
      "version": 1,
      "updateDateTime": "2025-04-29T13:20:35.754790Z",
      "publishDateTime": "2025-04-29T13:21:33.998353Z",
      "createDateTime": "2025-04-29T13:20:35.754790Z"
    },
    {
      "id": "680d2156ec4b848c99162a45",
      "status": "PUBLISHED",
      "data": {
        "unit": "IU / kg",
        "title": "Afstyla",
        "dosage": 100,
        "frequency": "once a week",
        "maxDosage": 16
      },
      "schema": "medications",
      "tags": [
        "Prophylactic"
      ],
      "resources": [
        "deployment/66ed50384fd3ec6700cae46f",
        "deployment/66f174ad8d06fd56dac30321",
        "deployment/66f174ad8d06fd56dac30321",
        "deployment/66f174ad8d06fd56dac30321",
        "deployment/66f174ad8d06fd56dac30321",
        "deployment/66f174ad8d06fd56dac30321",
        "deployment/66f174ad8d06fd56dac30321",
        "deployment/66f174ad8d06fd56dac30321",
        "deployment/66f174ad8d06fd56dac30321",
        "deployment/66f174ad8d06fd56dac30321",
        "deployment/66f174ad8d06fd56dac30321",
        "deployment/66f174ad8d06fd56dac30321",
        "deployment/66f174ad8d06fd56dac30321"
      ],
      "version": 2,
      "updateDateTime": "2025-04-29T13:12:48.889897Z",
      "publishDateTime": "2025-04-29T13:21:34.048577Z",
      "createDateTime": "2025-04-26T18:09:26.380446Z"
    },
    {
      "id": "680d218d4cae9b1013b36e7f",
      "status": "PUBLISHED",
      "data": {
        "unit": "Test 2",
        "title": "Eloctate (Antihemophilic Factor Recombinant),",
        "dosage": 2,
        "maxDosage": 10
      },
      "schema": "medications",
      "tags": [
        "OnDemand"
      ],
      "resources": [
        "deployment/66ed50384fd3ec6700cae46f",
        "deployment/66f174ad8d06fd56dac30321",
        "deployment/66f174ad8d06fd56dac30321",
        "deployment/66f174ad8d06fd56dac30321",
        "deployment/66f174ad8d06fd56dac30321",
        "deployment/66f174ad8d06fd56dac30321",
        "deployment/66f174ad8d06fd56dac30321",
        "deployment/66f174ad8d06fd56dac30321",
        "deployment/66f174ad8d06fd56dac30321",
        "deployment/66f174ad8d06fd56dac30321",
        "deployment/66f174ad8d06fd56dac30321",
        "deployment/66f174ad8d06fd56dac30321",
        "deployment/66f174ad8d06fd56dac30321"
      ],
      "version": 2,
      "updateDateTime": "2025-04-29T13:16:25.990138Z",
      "publishDateTime": "2025-04-29T13:21:34.065111Z",
      "createDateTime": "2025-04-26T18:10:21.902088Z"
    }
  ]
""".data(using: .utf8)!
        return (try? JSONDecoder().decode(from: jsonData, [CMSMedicationResponse.MedicationItem].self))!

    }

    var createObjectResponse: CreateObjectResponse {
        let jsonData =  """
                            {
                              "id": "6810d09221bc04c2a7ea1f51"
                            }
                        """
            .data(using: .utf8)!
        return (try? JSONDecoder().decode(from: jsonData, CreateObjectResponse.self))!

    }
}
