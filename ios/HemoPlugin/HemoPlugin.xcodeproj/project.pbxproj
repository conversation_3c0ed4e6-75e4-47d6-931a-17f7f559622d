// !$*UTF8*$!
{
	archiveVersion = 1;
	classes = {
	};
	objectVersion = 56;
	objects = {

/* Begin PBXBuildFile section */
		00A90FA442F996C7551C02EC /* Pods_HemoPluginSampleApp.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 6A3ABD4E3558E8F18B75EECA /* Pods_HemoPluginSampleApp.framework */; };
		2B1A31C82DA80E57008BA2AE /* Dimensions.swift in Sources */ = {isa = PBXBuildFile; fileRef = 2B1A31C02DA80E57008BA2AE /* Dimensions.swift */; };
		2B1A31C92DA80E57008BA2AE /* DatePickerView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 2B1A31B42DA80E57008BA2AE /* DatePickerView.swift */; };
		2B1A31CA2DA80E57008BA2AE /* HumaSUIImageView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 2B1A31B72DA80E57008BA2AE /* HumaSUIImageView.swift */; };
		2B1A31CB2DA80E57008BA2AE /* SwiftUI+Fonts.swift in Sources */ = {isa = PBXBuildFile; fileRef = 2B1A31C32DA80E57008BA2AE /* SwiftUI+Fonts.swift */; };
		2B1A31CC2DA80E57008BA2AE /* HumaSUITextField.swift in Sources */ = {isa = PBXBuildFile; fileRef = 2B1A31B82DA80E57008BA2AE /* HumaSUITextField.swift */; };
		2B1A31CD2DA80E57008BA2AE /* TempStyler.swift in Sources */ = {isa = PBXBuildFile; fileRef = 2B1A31C42DA80E57008BA2AE /* TempStyler.swift */; };
		2B1A31CE2DA80E57008BA2AE /* CircleLoadingView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 2B1A31B22DA80E57008BA2AE /* CircleLoadingView.swift */; };
		2B1A31CF2DA80E57008BA2AE /* HeaderView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 2B1A31B62DA80E57008BA2AE /* HeaderView.swift */; };
		2B1A31D02DA80E57008BA2AE /* PillButtonStyle.swift in Sources */ = {isa = PBXBuildFile; fileRef = 2B1A31C12DA80E57008BA2AE /* PillButtonStyle.swift */; };
		2B1A31D12DA80E57008BA2AE /* HalfSheetHelper.swift in Sources */ = {isa = PBXBuildFile; fileRef = 2B1A31B52DA80E57008BA2AE /* HalfSheetHelper.swift */; };
		2B1A31D32DA80E57008BA2AE /* CardStyleModifier.swift in Sources */ = {isa = PBXBuildFile; fileRef = 2B1A31BF2DA80E57008BA2AE /* CardStyleModifier.swift */; };
		2B1A31D62DA80E57008BA2AE /* ShimmerModifier.swift in Sources */ = {isa = PBXBuildFile; fileRef = 2B1A31C22DA80E57008BA2AE /* ShimmerModifier.swift */; };
		2B1A31D72DA80E57008BA2AE /* UnderlineButtonStyle.swift in Sources */ = {isa = PBXBuildFile; fileRef = 2B1A31C52DA80E57008BA2AE /* UnderlineButtonStyle.swift */; };
		2B1A31D82DA80E57008BA2AE /* View+Extensions.swift in Sources */ = {isa = PBXBuildFile; fileRef = 2B1A31BB2DA80E57008BA2AE /* View+Extensions.swift */; };
		2B1A31D92DA80E57008BA2AE /* Dimensions.swift in Sources */ = {isa = PBXBuildFile; fileRef = 2B1A31C02DA80E57008BA2AE /* Dimensions.swift */; };
		2B1A31DA2DA80E57008BA2AE /* DatePickerView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 2B1A31B42DA80E57008BA2AE /* DatePickerView.swift */; };
		2B1A31DB2DA80E57008BA2AE /* HumaSUIImageView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 2B1A31B72DA80E57008BA2AE /* HumaSUIImageView.swift */; };
		2B1A31DC2DA80E57008BA2AE /* SwiftUI+Fonts.swift in Sources */ = {isa = PBXBuildFile; fileRef = 2B1A31C32DA80E57008BA2AE /* SwiftUI+Fonts.swift */; };
		2B1A31DD2DA80E57008BA2AE /* HumaSUITextField.swift in Sources */ = {isa = PBXBuildFile; fileRef = 2B1A31B82DA80E57008BA2AE /* HumaSUITextField.swift */; };
		2B1A31DE2DA80E57008BA2AE /* TempStyler.swift in Sources */ = {isa = PBXBuildFile; fileRef = 2B1A31C42DA80E57008BA2AE /* TempStyler.swift */; };
		2B1A31DF2DA80E57008BA2AE /* CircleLoadingView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 2B1A31B22DA80E57008BA2AE /* CircleLoadingView.swift */; };
		2B1A31E02DA80E57008BA2AE /* HeaderView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 2B1A31B62DA80E57008BA2AE /* HeaderView.swift */; };
		2B1A31E12DA80E57008BA2AE /* PillButtonStyle.swift in Sources */ = {isa = PBXBuildFile; fileRef = 2B1A31C12DA80E57008BA2AE /* PillButtonStyle.swift */; };
		2B1A31E22DA80E57008BA2AE /* HalfSheetHelper.swift in Sources */ = {isa = PBXBuildFile; fileRef = 2B1A31B52DA80E57008BA2AE /* HalfSheetHelper.swift */; };
		2B1A31E42DA80E57008BA2AE /* CardStyleModifier.swift in Sources */ = {isa = PBXBuildFile; fileRef = 2B1A31BF2DA80E57008BA2AE /* CardStyleModifier.swift */; };
		2B1A31E72DA80E57008BA2AE /* ShimmerModifier.swift in Sources */ = {isa = PBXBuildFile; fileRef = 2B1A31C22DA80E57008BA2AE /* ShimmerModifier.swift */; };
		2B1A31E82DA80E57008BA2AE /* UnderlineButtonStyle.swift in Sources */ = {isa = PBXBuildFile; fileRef = 2B1A31C52DA80E57008BA2AE /* UnderlineButtonStyle.swift */; };
		2B1A31E92DA80E57008BA2AE /* View+Extensions.swift in Sources */ = {isa = PBXBuildFile; fileRef = 2B1A31BB2DA80E57008BA2AE /* View+Extensions.swift */; };
		2B1A31EA2DA80E57008BA2AE /* Dimensions.swift in Sources */ = {isa = PBXBuildFile; fileRef = 2B1A31C02DA80E57008BA2AE /* Dimensions.swift */; };
		2B1A31EB2DA80E57008BA2AE /* DatePickerView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 2B1A31B42DA80E57008BA2AE /* DatePickerView.swift */; };
		2B1A31EC2DA80E57008BA2AE /* HumaSUIImageView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 2B1A31B72DA80E57008BA2AE /* HumaSUIImageView.swift */; };
		2B1A31ED2DA80E57008BA2AE /* SwiftUI+Fonts.swift in Sources */ = {isa = PBXBuildFile; fileRef = 2B1A31C32DA80E57008BA2AE /* SwiftUI+Fonts.swift */; };
		2B1A31EE2DA80E57008BA2AE /* HumaSUITextField.swift in Sources */ = {isa = PBXBuildFile; fileRef = 2B1A31B82DA80E57008BA2AE /* HumaSUITextField.swift */; };
		2B1A31EF2DA80E57008BA2AE /* TempStyler.swift in Sources */ = {isa = PBXBuildFile; fileRef = 2B1A31C42DA80E57008BA2AE /* TempStyler.swift */; };
		2B1A31F02DA80E57008BA2AE /* CircleLoadingView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 2B1A31B22DA80E57008BA2AE /* CircleLoadingView.swift */; };
		2B1A31F12DA80E57008BA2AE /* HeaderView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 2B1A31B62DA80E57008BA2AE /* HeaderView.swift */; };
		2B1A31F22DA80E57008BA2AE /* PillButtonStyle.swift in Sources */ = {isa = PBXBuildFile; fileRef = 2B1A31C12DA80E57008BA2AE /* PillButtonStyle.swift */; };
		2B1A31F32DA80E57008BA2AE /* HalfSheetHelper.swift in Sources */ = {isa = PBXBuildFile; fileRef = 2B1A31B52DA80E57008BA2AE /* HalfSheetHelper.swift */; };
		2B1A31F52DA80E57008BA2AE /* CardStyleModifier.swift in Sources */ = {isa = PBXBuildFile; fileRef = 2B1A31BF2DA80E57008BA2AE /* CardStyleModifier.swift */; };
		2B1A31F82DA80E57008BA2AE /* ShimmerModifier.swift in Sources */ = {isa = PBXBuildFile; fileRef = 2B1A31C22DA80E57008BA2AE /* ShimmerModifier.swift */; };
		2B1A31F92DA80E57008BA2AE /* UnderlineButtonStyle.swift in Sources */ = {isa = PBXBuildFile; fileRef = 2B1A31C52DA80E57008BA2AE /* UnderlineButtonStyle.swift */; };
		2B1A31FA2DA80E57008BA2AE /* View+Extensions.swift in Sources */ = {isa = PBXBuildFile; fileRef = 2B1A31BB2DA80E57008BA2AE /* View+Extensions.swift */; };
		2B1A31FD2DA81113008BA2AE /* PreviewModels.swift in Sources */ = {isa = PBXBuildFile; fileRef = 2B1A31FB2DA81113008BA2AE /* PreviewModels.swift */; };
		2B1A31FE2DA81113008BA2AE /* PreviewModels.swift in Sources */ = {isa = PBXBuildFile; fileRef = 2B1A31FB2DA81113008BA2AE /* PreviewModels.swift */; };
		2B1A31FF2DA81113008BA2AE /* PreviewModels.swift in Sources */ = {isa = PBXBuildFile; fileRef = 2B1A31FB2DA81113008BA2AE /* PreviewModels.swift */; };
		2B1A320A2DA8111D008BA2AE /* BodyMapView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 2B1A32022DA8111D008BA2AE /* BodyMapView.swift */; };
		2B1A320B2DA8111D008BA2AE /* BodyMapViewModel.swift in Sources */ = {isa = PBXBuildFile; fileRef = 2B1A32002DA8111D008BA2AE /* BodyMapViewModel.swift */; };
		2B1A320C2DA8111D008BA2AE /* CircularInitialView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 2B1A32032DA8111D008BA2AE /* CircularInitialView.swift */; };
		2B1A320D2DA8111D008BA2AE /* BodyMapView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 2B1A32022DA8111D008BA2AE /* BodyMapView.swift */; };
		2B1A320E2DA8111D008BA2AE /* BodyMapViewModel.swift in Sources */ = {isa = PBXBuildFile; fileRef = 2B1A32002DA8111D008BA2AE /* BodyMapViewModel.swift */; };
		2B1A320F2DA8111D008BA2AE /* CircularInitialView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 2B1A32032DA8111D008BA2AE /* CircularInitialView.swift */; };
		2B1A32102DA8111D008BA2AE /* BodyMapView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 2B1A32022DA8111D008BA2AE /* BodyMapView.swift */; };
		2B1A32112DA8111D008BA2AE /* BodyMapViewModel.swift in Sources */ = {isa = PBXBuildFile; fileRef = 2B1A32002DA8111D008BA2AE /* BodyMapViewModel.swift */; };
		2B1A32122DA8111D008BA2AE /* CircularInitialView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 2B1A32032DA8111D008BA2AE /* CircularInitialView.swift */; };
		2B1A32192DA8117D008BA2AE /* HemoJournalWidget.swift in Sources */ = {isa = PBXBuildFile; fileRef = 2B1A32162DA8117D008BA2AE /* HemoJournalWidget.swift */; };
		2B1A321A2DA8117D008BA2AE /* AnyHumaNetworking+Route.swift in Sources */ = {isa = PBXBuildFile; fileRef = 2B1A32132DA8117D008BA2AE /* AnyHumaNetworking+Route.swift */; };
		2B1A321B2DA8117D008BA2AE /* HemoJournalWidgetConfig.swift in Sources */ = {isa = PBXBuildFile; fileRef = 2B1A32172DA8117D008BA2AE /* HemoJournalWidgetConfig.swift */; };
		2B1A321C2DA8117D008BA2AE /* HemoJournalRepository.swift in Sources */ = {isa = PBXBuildFile; fileRef = 2B1A32142DA8117D008BA2AE /* HemoJournalRepository.swift */; };
		2B1A321D2DA8117D008BA2AE /* HemoJournalWidgetCoordinator.swift in Sources */ = {isa = PBXBuildFile; fileRef = 2B1A32182DA8117D008BA2AE /* HemoJournalWidgetCoordinator.swift */; };
		2B1A321E2DA8117D008BA2AE /* HemoJournalWidget.swift in Sources */ = {isa = PBXBuildFile; fileRef = 2B1A32162DA8117D008BA2AE /* HemoJournalWidget.swift */; };
		2B1A321F2DA8117D008BA2AE /* AnyHumaNetworking+Route.swift in Sources */ = {isa = PBXBuildFile; fileRef = 2B1A32132DA8117D008BA2AE /* AnyHumaNetworking+Route.swift */; };
		2B1A32202DA8117D008BA2AE /* HemoJournalWidgetConfig.swift in Sources */ = {isa = PBXBuildFile; fileRef = 2B1A32172DA8117D008BA2AE /* HemoJournalWidgetConfig.swift */; };
		2B1A32212DA8117D008BA2AE /* HemoJournalRepository.swift in Sources */ = {isa = PBXBuildFile; fileRef = 2B1A32142DA8117D008BA2AE /* HemoJournalRepository.swift */; };
		2B1A32222DA8117D008BA2AE /* HemoJournalWidgetCoordinator.swift in Sources */ = {isa = PBXBuildFile; fileRef = 2B1A32182DA8117D008BA2AE /* HemoJournalWidgetCoordinator.swift */; };
		2B1A32232DA8117D008BA2AE /* HemoJournalWidget.swift in Sources */ = {isa = PBXBuildFile; fileRef = 2B1A32162DA8117D008BA2AE /* HemoJournalWidget.swift */; };
		2B1A32242DA8117D008BA2AE /* AnyHumaNetworking+Route.swift in Sources */ = {isa = PBXBuildFile; fileRef = 2B1A32132DA8117D008BA2AE /* AnyHumaNetworking+Route.swift */; };
		2B1A32252DA8117D008BA2AE /* HemoJournalWidgetConfig.swift in Sources */ = {isa = PBXBuildFile; fileRef = 2B1A32172DA8117D008BA2AE /* HemoJournalWidgetConfig.swift */; };
		2B1A32262DA8117D008BA2AE /* HemoJournalRepository.swift in Sources */ = {isa = PBXBuildFile; fileRef = 2B1A32142DA8117D008BA2AE /* HemoJournalRepository.swift */; };
		2B1A32272DA8117D008BA2AE /* HemoJournalWidgetCoordinator.swift in Sources */ = {isa = PBXBuildFile; fileRef = 2B1A32182DA8117D008BA2AE /* HemoJournalWidgetCoordinator.swift */; };
		2B1A322A2DA811BB008BA2AE /* WidgetDataResponse.swift in Sources */ = {isa = PBXBuildFile; fileRef = 2B1A32282DA811BB008BA2AE /* WidgetDataResponse.swift */; };
		2B1A322B2DA811BB008BA2AE /* WidgetDataResponse.swift in Sources */ = {isa = PBXBuildFile; fileRef = 2B1A32282DA811BB008BA2AE /* WidgetDataResponse.swift */; };
		2B1A322C2DA811BB008BA2AE /* WidgetDataResponse.swift in Sources */ = {isa = PBXBuildFile; fileRef = 2B1A32282DA811BB008BA2AE /* WidgetDataResponse.swift */; };
		2B1A32452DA8123A008BA2AE /* AnyHemoJournalCardViewModel.swift in Sources */ = {isa = PBXBuildFile; fileRef = 2B1A323F2DA8123A008BA2AE /* AnyHemoJournalCardViewModel.swift */; };
		2B1A32472DA8123A008BA2AE /* HemoJournalCardViewModel.swift in Sources */ = {isa = PBXBuildFile; fileRef = 2B1A32402DA8123A008BA2AE /* HemoJournalCardViewModel.swift */; };
		2B1A32482DA8123A008BA2AE /* HemoJournalCardView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 2B1A32432DA8123A008BA2AE /* HemoJournalCardView.swift */; };
		2B1A32492DA8123A008BA2AE /* AnyHemoJournalCardViewModel.swift in Sources */ = {isa = PBXBuildFile; fileRef = 2B1A323F2DA8123A008BA2AE /* AnyHemoJournalCardViewModel.swift */; };
		2B1A324B2DA8123A008BA2AE /* HemoJournalCardViewModel.swift in Sources */ = {isa = PBXBuildFile; fileRef = 2B1A32402DA8123A008BA2AE /* HemoJournalCardViewModel.swift */; };
		2B1A324C2DA8123A008BA2AE /* HemoJournalCardView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 2B1A32432DA8123A008BA2AE /* HemoJournalCardView.swift */; };
		2B1A324D2DA8123A008BA2AE /* AnyHemoJournalCardViewModel.swift in Sources */ = {isa = PBXBuildFile; fileRef = 2B1A323F2DA8123A008BA2AE /* AnyHemoJournalCardViewModel.swift */; };
		2B1A324F2DA8123A008BA2AE /* HemoJournalCardViewModel.swift in Sources */ = {isa = PBXBuildFile; fileRef = 2B1A32402DA8123A008BA2AE /* HemoJournalCardViewModel.swift */; };
		2B1A32502DA8123A008BA2AE /* HemoJournalCardView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 2B1A32432DA8123A008BA2AE /* HemoJournalCardView.swift */; };
		2B1A325B2DA81289008BA2AE /* HeaderViewModel.swift in Sources */ = {isa = PBXBuildFile; fileRef = 2B1A32562DA81289008BA2AE /* HeaderViewModel.swift */; };
		2B1A325D2DA81289008BA2AE /* AnyHeaderViewModel.swift in Sources */ = {isa = PBXBuildFile; fileRef = 2B1A32552DA81289008BA2AE /* AnyHeaderViewModel.swift */; };
		2B1A325E2DA81289008BA2AE /* JournalHeaderView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 2B1A32592DA81289008BA2AE /* JournalHeaderView.swift */; };
		2B1A325F2DA81289008BA2AE /* PreviewHeaderViewModel.swift in Sources */ = {isa = PBXBuildFile; fileRef = 2B1A32572DA81289008BA2AE /* PreviewHeaderViewModel.swift */; };
		2B1A32612DA81289008BA2AE /* HeaderViewModel.swift in Sources */ = {isa = PBXBuildFile; fileRef = 2B1A32562DA81289008BA2AE /* HeaderViewModel.swift */; };
		2B1A32632DA81289008BA2AE /* AnyHeaderViewModel.swift in Sources */ = {isa = PBXBuildFile; fileRef = 2B1A32552DA81289008BA2AE /* AnyHeaderViewModel.swift */; };
		2B1A32642DA81289008BA2AE /* JournalHeaderView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 2B1A32592DA81289008BA2AE /* JournalHeaderView.swift */; };
		2B1A32652DA81289008BA2AE /* PreviewHeaderViewModel.swift in Sources */ = {isa = PBXBuildFile; fileRef = 2B1A32572DA81289008BA2AE /* PreviewHeaderViewModel.swift */; };
		2B1A32672DA81289008BA2AE /* HeaderViewModel.swift in Sources */ = {isa = PBXBuildFile; fileRef = 2B1A32562DA81289008BA2AE /* HeaderViewModel.swift */; };
		2B1A32692DA81289008BA2AE /* AnyHeaderViewModel.swift in Sources */ = {isa = PBXBuildFile; fileRef = 2B1A32552DA81289008BA2AE /* AnyHeaderViewModel.swift */; };
		2B1A326A2DA81289008BA2AE /* JournalHeaderView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 2B1A32592DA81289008BA2AE /* JournalHeaderView.swift */; };
		2B1A326B2DA81289008BA2AE /* PreviewHeaderViewModel.swift in Sources */ = {isa = PBXBuildFile; fileRef = 2B1A32572DA81289008BA2AE /* PreviewHeaderViewModel.swift */; };
		2B1A326E2DA813BD008BA2AE /* LoadingView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 2B1A326D2DA813BD008BA2AE /* LoadingView.swift */; };
		2B1A326F2DA813BD008BA2AE /* LoadingView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 2B1A326D2DA813BD008BA2AE /* LoadingView.swift */; };
		2B1A32702DA813BD008BA2AE /* LoadingView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 2B1A326D2DA813BD008BA2AE /* LoadingView.swift */; };
		2B1A32792DA813C7008BA2AE /* AddBleedView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 2B1A32732DA813C7008BA2AE /* AddBleedView.swift */; };
		2B1A327A2DA813C7008BA2AE /* CustomBodyPartView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 2B1A32762DA813C7008BA2AE /* CustomBodyPartView.swift */; };
		2B1A327B2DA813C7008BA2AE /* AddBleedViewModel.swift in Sources */ = {isa = PBXBuildFile; fileRef = 2B1A32712DA813C7008BA2AE /* AddBleedViewModel.swift */; };
		2B1A327D2DA813C7008BA2AE /* AddBleedView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 2B1A32732DA813C7008BA2AE /* AddBleedView.swift */; };
		2B1A327E2DA813C7008BA2AE /* CustomBodyPartView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 2B1A32762DA813C7008BA2AE /* CustomBodyPartView.swift */; };
		2B1A327F2DA813C7008BA2AE /* AddBleedViewModel.swift in Sources */ = {isa = PBXBuildFile; fileRef = 2B1A32712DA813C7008BA2AE /* AddBleedViewModel.swift */; };
		2B1A32812DA813C7008BA2AE /* AddBleedView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 2B1A32732DA813C7008BA2AE /* AddBleedView.swift */; };
		2B1A32822DA813C7008BA2AE /* CustomBodyPartView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 2B1A32762DA813C7008BA2AE /* CustomBodyPartView.swift */; };
		2B1A32832DA813C7008BA2AE /* AddBleedViewModel.swift in Sources */ = {isa = PBXBuildFile; fileRef = 2B1A32712DA813C7008BA2AE /* AddBleedViewModel.swift */; };
		2B1A32852DA81596008BA2AE /* Assets.xcassets in Resources */ = {isa = PBXBuildFile; fileRef = 2B1A32842DA81596008BA2AE /* Assets.xcassets */; };
		2B1A32862DA81596008BA2AE /* Assets.xcassets in Resources */ = {isa = PBXBuildFile; fileRef = 2B1A32842DA81596008BA2AE /* Assets.xcassets */; };
		2B1A32872DA81596008BA2AE /* Assets.xcassets in Resources */ = {isa = PBXBuildFile; fileRef = 2B1A32842DA81596008BA2AE /* Assets.xcassets */; };
		2B2FECEC2DC123AA00A699CA /* ReusableListView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 2B2FECE62DC123AA00A699CA /* ReusableListView.swift */; };
		2B2FECED2DC123AA00A699CA /* SetupProfileCardView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 2B2FECEA2DC123AA00A699CA /* SetupProfileCardView.swift */; };
		2B2FECEE2DC123AA00A699CA /* SetupProfileCardViewModel.swift in Sources */ = {isa = PBXBuildFile; fileRef = 2B2FECE82DC123AA00A699CA /* SetupProfileCardViewModel.swift */; };
		2B2FECEF2DC123AA00A699CA /* ActiveProfileCardViewModel.swift in Sources */ = {isa = PBXBuildFile; fileRef = 2B2FECDE2DC123AA00A699CA /* ActiveProfileCardViewModel.swift */; };
		2B2FECF02DC123AA00A699CA /* ActiveProfileCardView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 2B2FECE02DC123AA00A699CA /* ActiveProfileCardView.swift */; };
		2B2FECF12DC123AA00A699CA /* ReusableListViewModel.swift in Sources */ = {isa = PBXBuildFile; fileRef = 2B2FECE42DC123AA00A699CA /* ReusableListViewModel.swift */; };
		2B2FECF22DC123AA00A699CA /* ReusableListView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 2B2FECE62DC123AA00A699CA /* ReusableListView.swift */; };
		2B2FECF32DC123AA00A699CA /* SetupProfileCardView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 2B2FECEA2DC123AA00A699CA /* SetupProfileCardView.swift */; };
		2B2FECF42DC123AA00A699CA /* SetupProfileCardViewModel.swift in Sources */ = {isa = PBXBuildFile; fileRef = 2B2FECE82DC123AA00A699CA /* SetupProfileCardViewModel.swift */; };
		2B2FECF52DC123AA00A699CA /* ActiveProfileCardViewModel.swift in Sources */ = {isa = PBXBuildFile; fileRef = 2B2FECDE2DC123AA00A699CA /* ActiveProfileCardViewModel.swift */; };
		2B2FECF62DC123AA00A699CA /* ActiveProfileCardView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 2B2FECE02DC123AA00A699CA /* ActiveProfileCardView.swift */; };
		2B2FECF72DC123AA00A699CA /* ReusableListViewModel.swift in Sources */ = {isa = PBXBuildFile; fileRef = 2B2FECE42DC123AA00A699CA /* ReusableListViewModel.swift */; };
		2B2FECF82DC123AA00A699CA /* ReusableListView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 2B2FECE62DC123AA00A699CA /* ReusableListView.swift */; };
		2B2FECF92DC123AA00A699CA /* SetupProfileCardView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 2B2FECEA2DC123AA00A699CA /* SetupProfileCardView.swift */; };
		2B2FECFA2DC123AA00A699CA /* SetupProfileCardViewModel.swift in Sources */ = {isa = PBXBuildFile; fileRef = 2B2FECE82DC123AA00A699CA /* SetupProfileCardViewModel.swift */; };
		2B2FECFB2DC123AA00A699CA /* ActiveProfileCardViewModel.swift in Sources */ = {isa = PBXBuildFile; fileRef = 2B2FECDE2DC123AA00A699CA /* ActiveProfileCardViewModel.swift */; };
		2B2FECFC2DC123AA00A699CA /* ActiveProfileCardView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 2B2FECE02DC123AA00A699CA /* ActiveProfileCardView.swift */; };
		2B2FECFD2DC123AA00A699CA /* ReusableListViewModel.swift in Sources */ = {isa = PBXBuildFile; fileRef = 2B2FECE42DC123AA00A699CA /* ReusableListViewModel.swift */; };
		2B2FECFF2DC1240200A699CA /* HemoJournalCardViewModel+Preview.swift in Sources */ = {isa = PBXBuildFile; fileRef = 2B2FECFE2DC1240200A699CA /* HemoJournalCardViewModel+Preview.swift */; };
		2B2FED002DC1240200A699CA /* HemoJournalCardViewModel+Preview.swift in Sources */ = {isa = PBXBuildFile; fileRef = 2B2FECFE2DC1240200A699CA /* HemoJournalCardViewModel+Preview.swift */; };
		2B2FED012DC1240200A699CA /* HemoJournalCardViewModel+Preview.swift in Sources */ = {isa = PBXBuildFile; fileRef = 2B2FECFE2DC1240200A699CA /* HemoJournalCardViewModel+Preview.swift */; };
		2B2FED1E2DC127AC00A699CA /* HemoProfileQuestionnaireSource.swift in Sources */ = {isa = PBXBuildFile; fileRef = 2B2FED092DC127AC00A699CA /* HemoProfileQuestionnaireSource.swift */; };
		2B2FED1F2DC127AC00A699CA /* TimeQuestionView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 2B2FED172DC127AC00A699CA /* TimeQuestionView.swift */; };
		2B2FED202DC127AC00A699CA /* NumericQuestionView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 2B2FED132DC127AC00A699CA /* NumericQuestionView.swift */; };
		2B2FED212DC127AC00A699CA /* QuestionnaireViewModel.swift in Sources */ = {isa = PBXBuildFile; fileRef = 2B2FED0B2DC127AC00A699CA /* QuestionnaireViewModel.swift */; };
		2B2FED222DC127AC00A699CA /* QuestionType.swift in Sources */ = {isa = PBXBuildFile; fileRef = 2B2FED052DC127AC00A699CA /* QuestionType.swift */; };
		2B2FED232DC127AC00A699CA /* QuestionnaireView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 2B2FED1A2DC127AC00A699CA /* QuestionnaireView.swift */; };
		2B2FED242DC127AC00A699CA /* TextQuestionView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 2B2FED162DC127AC00A699CA /* TextQuestionView.swift */; };
		2B2FED262DC127AC00A699CA /* SingleChoiceQuestionView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 2B2FED152DC127AC00A699CA /* SingleChoiceQuestionView.swift */; };
		2B2FED272DC127AC00A699CA /* AutocompleteQuestionView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 2B2FED102DC127AC00A699CA /* AutocompleteQuestionView.swift */; };
		2B2FED282DC127AC00A699CA /* Question.swift in Sources */ = {isa = PBXBuildFile; fileRef = 2B2FED042DC127AC00A699CA /* Question.swift */; };
		2B2FED292DC127AC00A699CA /* QuestionView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 2B2FED1B2DC127AC00A699CA /* QuestionView.swift */; };
		2B2FED2A2DC127AC00A699CA /* ValidationRule.swift in Sources */ = {isa = PBXBuildFile; fileRef = 2B2FED072DC127AC00A699CA /* ValidationRule.swift */; };
		2B2FED2B2DC127AC00A699CA /* TagView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 2B2FED0E2DC127AC00A699CA /* TagView.swift */; };
		2B2FED2C2DC127AC00A699CA /* PickerQuestionView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 2B2FED142DC127AC00A699CA /* PickerQuestionView.swift */; };
		2B2FED2D2DC127AC00A699CA /* Answer.swift in Sources */ = {isa = PBXBuildFile; fileRef = 2B2FED032DC127AC00A699CA /* Answer.swift */; };
		2B2FED2E2DC127AC00A699CA /* ValueUnitQuestionView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 2B2FED182DC127AC00A699CA /* ValueUnitQuestionView.swift */; };
		2B2FED2F2DC127AC00A699CA /* DateQuestionView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 2B2FED112DC127AC00A699CA /* DateQuestionView.swift */; };
		2B2FED302DC127AC00A699CA /* MultipleChoiceQuestionView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 2B2FED122DC127AC00A699CA /* MultipleChoiceQuestionView.swift */; };
		2B2FED322DC127AC00A699CA /* HemoProfileQuestionnaireSource.swift in Sources */ = {isa = PBXBuildFile; fileRef = 2B2FED092DC127AC00A699CA /* HemoProfileQuestionnaireSource.swift */; };
		2B2FED332DC127AC00A699CA /* TimeQuestionView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 2B2FED172DC127AC00A699CA /* TimeQuestionView.swift */; };
		2B2FED342DC127AC00A699CA /* NumericQuestionView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 2B2FED132DC127AC00A699CA /* NumericQuestionView.swift */; };
		2B2FED352DC127AC00A699CA /* QuestionnaireViewModel.swift in Sources */ = {isa = PBXBuildFile; fileRef = 2B2FED0B2DC127AC00A699CA /* QuestionnaireViewModel.swift */; };
		2B2FED362DC127AC00A699CA /* QuestionType.swift in Sources */ = {isa = PBXBuildFile; fileRef = 2B2FED052DC127AC00A699CA /* QuestionType.swift */; };
		2B2FED372DC127AC00A699CA /* QuestionnaireView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 2B2FED1A2DC127AC00A699CA /* QuestionnaireView.swift */; };
		2B2FED382DC127AC00A699CA /* TextQuestionView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 2B2FED162DC127AC00A699CA /* TextQuestionView.swift */; };
		2B2FED3A2DC127AC00A699CA /* SingleChoiceQuestionView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 2B2FED152DC127AC00A699CA /* SingleChoiceQuestionView.swift */; };
		2B2FED3B2DC127AC00A699CA /* AutocompleteQuestionView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 2B2FED102DC127AC00A699CA /* AutocompleteQuestionView.swift */; };
		2B2FED3C2DC127AC00A699CA /* Question.swift in Sources */ = {isa = PBXBuildFile; fileRef = 2B2FED042DC127AC00A699CA /* Question.swift */; };
		2B2FED3D2DC127AC00A699CA /* QuestionView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 2B2FED1B2DC127AC00A699CA /* QuestionView.swift */; };
		2B2FED3E2DC127AC00A699CA /* ValidationRule.swift in Sources */ = {isa = PBXBuildFile; fileRef = 2B2FED072DC127AC00A699CA /* ValidationRule.swift */; };
		2B2FED3F2DC127AC00A699CA /* TagView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 2B2FED0E2DC127AC00A699CA /* TagView.swift */; };
		2B2FED402DC127AC00A699CA /* PickerQuestionView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 2B2FED142DC127AC00A699CA /* PickerQuestionView.swift */; };
		2B2FED412DC127AC00A699CA /* Answer.swift in Sources */ = {isa = PBXBuildFile; fileRef = 2B2FED032DC127AC00A699CA /* Answer.swift */; };
		2B2FED422DC127AC00A699CA /* ValueUnitQuestionView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 2B2FED182DC127AC00A699CA /* ValueUnitQuestionView.swift */; };
		2B2FED432DC127AC00A699CA /* DateQuestionView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 2B2FED112DC127AC00A699CA /* DateQuestionView.swift */; };
		2B2FED442DC127AC00A699CA /* MultipleChoiceQuestionView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 2B2FED122DC127AC00A699CA /* MultipleChoiceQuestionView.swift */; };
		2B2FED462DC127AC00A699CA /* HemoProfileQuestionnaireSource.swift in Sources */ = {isa = PBXBuildFile; fileRef = 2B2FED092DC127AC00A699CA /* HemoProfileQuestionnaireSource.swift */; };
		2B2FED472DC127AC00A699CA /* TimeQuestionView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 2B2FED172DC127AC00A699CA /* TimeQuestionView.swift */; };
		2B2FED482DC127AC00A699CA /* NumericQuestionView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 2B2FED132DC127AC00A699CA /* NumericQuestionView.swift */; };
		2B2FED492DC127AC00A699CA /* QuestionnaireViewModel.swift in Sources */ = {isa = PBXBuildFile; fileRef = 2B2FED0B2DC127AC00A699CA /* QuestionnaireViewModel.swift */; };
		2B2FED4A2DC127AC00A699CA /* QuestionType.swift in Sources */ = {isa = PBXBuildFile; fileRef = 2B2FED052DC127AC00A699CA /* QuestionType.swift */; };
		2B2FED4B2DC127AC00A699CA /* QuestionnaireView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 2B2FED1A2DC127AC00A699CA /* QuestionnaireView.swift */; };
		2B2FED4C2DC127AC00A699CA /* TextQuestionView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 2B2FED162DC127AC00A699CA /* TextQuestionView.swift */; };
		2B2FED4E2DC127AC00A699CA /* SingleChoiceQuestionView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 2B2FED152DC127AC00A699CA /* SingleChoiceQuestionView.swift */; };
		2B2FED4F2DC127AC00A699CA /* AutocompleteQuestionView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 2B2FED102DC127AC00A699CA /* AutocompleteQuestionView.swift */; };
		2B2FED502DC127AC00A699CA /* Question.swift in Sources */ = {isa = PBXBuildFile; fileRef = 2B2FED042DC127AC00A699CA /* Question.swift */; };
		2B2FED512DC127AC00A699CA /* QuestionView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 2B2FED1B2DC127AC00A699CA /* QuestionView.swift */; };
		2B2FED522DC127AC00A699CA /* ValidationRule.swift in Sources */ = {isa = PBXBuildFile; fileRef = 2B2FED072DC127AC00A699CA /* ValidationRule.swift */; };
		2B2FED532DC127AC00A699CA /* TagView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 2B2FED0E2DC127AC00A699CA /* TagView.swift */; };
		2B2FED542DC127AC00A699CA /* PickerQuestionView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 2B2FED142DC127AC00A699CA /* PickerQuestionView.swift */; };
		2B2FED552DC127AC00A699CA /* Answer.swift in Sources */ = {isa = PBXBuildFile; fileRef = 2B2FED032DC127AC00A699CA /* Answer.swift */; };
		2B2FED562DC127AC00A699CA /* ValueUnitQuestionView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 2B2FED182DC127AC00A699CA /* ValueUnitQuestionView.swift */; };
		2B2FED572DC127AC00A699CA /* DateQuestionView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 2B2FED112DC127AC00A699CA /* DateQuestionView.swift */; };
		2B2FED582DC127AC00A699CA /* MultipleChoiceQuestionView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 2B2FED122DC127AC00A699CA /* MultipleChoiceQuestionView.swift */; };
		2B332CC52DC371010056C4CD /* MockMedicationRepository.swift in Sources */ = {isa = PBXBuildFile; fileRef = 2B332CC42DC370F30056C4CD /* MockMedicationRepository.swift */; };
		2B332CC62DC371010056C4CD /* MockMedicationRepository.swift in Sources */ = {isa = PBXBuildFile; fileRef = 2B332CC42DC370F30056C4CD /* MockMedicationRepository.swift */; };
		2B332CC72DC371010056C4CD /* MockMedicationRepository.swift in Sources */ = {isa = PBXBuildFile; fileRef = 2B332CC42DC370F30056C4CD /* MockMedicationRepository.swift */; };
		2B332CC92DC37F560056C4CD /* HTMLText.swift in Sources */ = {isa = PBXBuildFile; fileRef = 2B332CC82DC37F560056C4CD /* HTMLText.swift */; };
		2B332CCA2DC37F560056C4CD /* HTMLText.swift in Sources */ = {isa = PBXBuildFile; fileRef = 2B332CC82DC37F560056C4CD /* HTMLText.swift */; };
		2B332CCB2DC37F560056C4CD /* HTMLText.swift in Sources */ = {isa = PBXBuildFile; fileRef = 2B332CC82DC37F560056C4CD /* HTMLText.swift */; };
		2B46C4712DD4D3BF006D0619 /* BleedDetailsView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 2B46C46D2DD4D3BF006D0619 /* BleedDetailsView.swift */; };
		2B46C4722DD4D3BF006D0619 /* BodyMapLogView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 2B46C46F2DD4D3BF006D0619 /* BodyMapLogView.swift */; };
		2B46C4732DD4D3BF006D0619 /* BleedDetailsView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 2B46C46D2DD4D3BF006D0619 /* BleedDetailsView.swift */; };
		2B46C4742DD4D3BF006D0619 /* BodyMapLogView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 2B46C46F2DD4D3BF006D0619 /* BodyMapLogView.swift */; };
		2B46C4752DD4D3BF006D0619 /* BleedDetailsView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 2B46C46D2DD4D3BF006D0619 /* BleedDetailsView.swift */; };
		2B46C4762DD4D3BF006D0619 /* BodyMapLogView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 2B46C46F2DD4D3BF006D0619 /* BodyMapLogView.swift */; };
		2B46C4782DD4D3F4006D0619 /* MockModuleResultRepository.swift in Sources */ = {isa = PBXBuildFile; fileRef = 2B46C4772DD4D3F4006D0619 /* MockModuleResultRepository.swift */; };
		2B46C4792DD4D3F4006D0619 /* MockModuleResultRepository.swift in Sources */ = {isa = PBXBuildFile; fileRef = 2B46C4772DD4D3F4006D0619 /* MockModuleResultRepository.swift */; };
		2B46C47A2DD4D3F4006D0619 /* MockModuleResultRepository.swift in Sources */ = {isa = PBXBuildFile; fileRef = 2B46C4772DD4D3F4006D0619 /* MockModuleResultRepository.swift */; };
		2B46C47C2DD4D631006D0619 /* MockFileRepository.swift in Sources */ = {isa = PBXBuildFile; fileRef = 2B46C47B2DD4D631006D0619 /* MockFileRepository.swift */; };
		2B46C47D2DD4D631006D0619 /* MockFileRepository.swift in Sources */ = {isa = PBXBuildFile; fileRef = 2B46C47B2DD4D631006D0619 /* MockFileRepository.swift */; };
		2B46C47E2DD4D631006D0619 /* MockFileRepository.swift in Sources */ = {isa = PBXBuildFile; fileRef = 2B46C47B2DD4D631006D0619 /* MockFileRepository.swift */; };
		2B507FC82D7899C5009A07FE /* HemoPlugin.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 2BD3D6942D5A1D9D0094DD4C /* HemoPlugin.framework */; };
		2B507FC92D7899C5009A07FE /* HemoPlugin.framework in Embed Frameworks */ = {isa = PBXBuildFile; fileRef = 2BD3D6942D5A1D9D0094DD4C /* HemoPlugin.framework */; settings = {ATTRIBUTES = (CodeSignOnCopy, RemoveHeadersOnCopy, ); }; };
		2B507FD32D7899E7009A07FE /* HumaFoundation.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 2B507FCD2D7899E7009A07FE /* HumaFoundation.framework */; };
		2B507FD52D7899E7009A07FE /* HumaLearnKit.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 2B507FCE2D7899E7009A07FE /* HumaLearnKit.framework */; };
		2B507FD72D7899E7009A07FE /* HumaModuleKit.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 2B507FCF2D7899E7009A07FE /* HumaModuleKit.framework */; };
		2B507FD92D7899E7009A07FE /* HumaModules.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 2B507FD02D7899E7009A07FE /* HumaModules.framework */; };
		2B507FDB2D7899E7009A07FE /* HumaQuestionnaire.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 2B507FD12D7899E7009A07FE /* HumaQuestionnaire.framework */; };
		2B507FDD2D7899E7009A07FE /* HumaWidgetKit.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 2B507FD22D7899E7009A07FE /* HumaWidgetKit.framework */; };
		2B75478C2DC36AAB00D10E65 /* MedicationRepository.swift in Sources */ = {isa = PBXBuildFile; fileRef = 2B75478B2DC36AAB00D10E65 /* MedicationRepository.swift */; };
		2B75478D2DC36AAB00D10E65 /* MedicationRepository.swift in Sources */ = {isa = PBXBuildFile; fileRef = 2B75478B2DC36AAB00D10E65 /* MedicationRepository.swift */; };
		2B75478E2DC36AAB00D10E65 /* MedicationRepository.swift in Sources */ = {isa = PBXBuildFile; fileRef = 2B75478B2DC36AAB00D10E65 /* MedicationRepository.swift */; };
		2B7547902DC36B0B00D10E65 /* CMSMedicationResponse.swift in Sources */ = {isa = PBXBuildFile; fileRef = 2B75478F2DC36AFA00D10E65 /* CMSMedicationResponse.swift */; };
		2B7547912DC36B0B00D10E65 /* CMSMedicationResponse.swift in Sources */ = {isa = PBXBuildFile; fileRef = 2B75478F2DC36AFA00D10E65 /* CMSMedicationResponse.swift */; };
		2B7547922DC36B0B00D10E65 /* CMSMedicationResponse.swift in Sources */ = {isa = PBXBuildFile; fileRef = 2B75478F2DC36AFA00D10E65 /* CMSMedicationResponse.swift */; };
		2B7547952DC36B4300D10E65 /* CMSMedicationRequest.swift in Sources */ = {isa = PBXBuildFile; fileRef = 2B7547942DC36B3B00D10E65 /* CMSMedicationRequest.swift */; };
		2B7547962DC36B4300D10E65 /* CMSMedicationRequest.swift in Sources */ = {isa = PBXBuildFile; fileRef = 2B7547942DC36B3B00D10E65 /* CMSMedicationRequest.swift */; };
		2B7547972DC36B4300D10E65 /* CMSMedicationRequest.swift in Sources */ = {isa = PBXBuildFile; fileRef = 2B7547942DC36B3B00D10E65 /* CMSMedicationRequest.swift */; };
		2B7EBB042D786EC5005747CD /* ContentView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 2B7EBB012D786EC5005747CD /* ContentView.swift */; };
		2B7EBB052D786EC5005747CD /* HemoPluginSampleAppApp.swift in Sources */ = {isa = PBXBuildFile; fileRef = 2B7EBB022D786EC5005747CD /* HemoPluginSampleAppApp.swift */; };
		2B7EBB062D786EC5005747CD /* Preview Assets.xcassets in Resources */ = {isa = PBXBuildFile; fileRef = 2B7EBAFE2D786EC5005747CD /* Preview Assets.xcassets */; };
		2B7EBB072D786EC5005747CD /* Assets.xcassets in Resources */ = {isa = PBXBuildFile; fileRef = 2B7EBB002D786EC5005747CD /* Assets.xcassets */; };
		2B84899A2DD5F35300CA53F5 /* BleedDetailsViewModel.swift in Sources */ = {isa = PBXBuildFile; fileRef = 2B8489982DD5F35300CA53F5 /* BleedDetailsViewModel.swift */; };
		2B84899B2DD5F35300CA53F5 /* BleedDetailsViewModel.swift in Sources */ = {isa = PBXBuildFile; fileRef = 2B8489982DD5F35300CA53F5 /* BleedDetailsViewModel.swift */; };
		2B84899C2DD5F35300CA53F5 /* BleedDetailsViewModel.swift in Sources */ = {isa = PBXBuildFile; fileRef = 2B8489982DD5F35300CA53F5 /* BleedDetailsViewModel.swift */; };
		2B84899F2DD5F36800CA53F5 /* BleedHistoryViewModel.swift in Sources */ = {isa = PBXBuildFile; fileRef = 2B84899D2DD5F36800CA53F5 /* BleedHistoryViewModel.swift */; };
		2B8489A02DD5F36800CA53F5 /* BleedHistoryViewModel.swift in Sources */ = {isa = PBXBuildFile; fileRef = 2B84899D2DD5F36800CA53F5 /* BleedHistoryViewModel.swift */; };
		2B8489A12DD5F36800CA53F5 /* BleedHistoryViewModel.swift in Sources */ = {isa = PBXBuildFile; fileRef = 2B84899D2DD5F36800CA53F5 /* BleedHistoryViewModel.swift */; };
		2B8489A62DD5F37300CA53F5 /* PhotoLogView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 2B8489A42DD5F37300CA53F5 /* PhotoLogView.swift */; };
		2B8489A72DD5F37300CA53F5 /* PhotoLogViewModel.swift in Sources */ = {isa = PBXBuildFile; fileRef = 2B8489A22DD5F37300CA53F5 /* PhotoLogViewModel.swift */; };
		2B8489A82DD5F37300CA53F5 /* PhotoLogView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 2B8489A42DD5F37300CA53F5 /* PhotoLogView.swift */; };
		2B8489A92DD5F37300CA53F5 /* PhotoLogViewModel.swift in Sources */ = {isa = PBXBuildFile; fileRef = 2B8489A22DD5F37300CA53F5 /* PhotoLogViewModel.swift */; };
		2B8489AA2DD5F37300CA53F5 /* PhotoLogView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 2B8489A42DD5F37300CA53F5 /* PhotoLogView.swift */; };
		2B8489AB2DD5F37300CA53F5 /* PhotoLogViewModel.swift in Sources */ = {isa = PBXBuildFile; fileRef = 2B8489A22DD5F37300CA53F5 /* PhotoLogViewModel.swift */; };
		2BB39F472DD23477003762D7 /* DetailsViewModel.swift in Sources */ = {isa = PBXBuildFile; fileRef = 2BB39F432DD23477003762D7 /* DetailsViewModel.swift */; };
		2BB39F482DD23477003762D7 /* DetailsView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 2BB39F452DD23477003762D7 /* DetailsView.swift */; };
		2BB39F492DD23477003762D7 /* DetailsViewModel.swift in Sources */ = {isa = PBXBuildFile; fileRef = 2BB39F432DD23477003762D7 /* DetailsViewModel.swift */; };
		2BB39F4A2DD23477003762D7 /* DetailsView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 2BB39F452DD23477003762D7 /* DetailsView.swift */; };
		2BB39F4B2DD23477003762D7 /* DetailsViewModel.swift in Sources */ = {isa = PBXBuildFile; fileRef = 2BB39F432DD23477003762D7 /* DetailsViewModel.swift */; };
		2BB39F4C2DD23477003762D7 /* DetailsView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 2BB39F452DD23477003762D7 /* DetailsView.swift */; };
		2BB39F4F2DD24131003762D7 /* BleedHistoryListView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 2BB39F4E2DD24129003762D7 /* BleedHistoryListView.swift */; };
		2BB39F502DD24131003762D7 /* BleedHistoryListView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 2BB39F4E2DD24129003762D7 /* BleedHistoryListView.swift */; };
		2BB39F512DD24131003762D7 /* BleedHistoryListView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 2BB39F4E2DD24129003762D7 /* BleedHistoryListView.swift */; };
		2BC15BCB2DC526E800F07B80 /* OptionItem.swift in Sources */ = {isa = PBXBuildFile; fileRef = 2BC15BCA2DC526E800F07B80 /* OptionItem.swift */; };
		2BC15BCC2DC526E800F07B80 /* OptionItem.swift in Sources */ = {isa = PBXBuildFile; fileRef = 2BC15BCA2DC526E800F07B80 /* OptionItem.swift */; };
		2BC15BCD2DC526E800F07B80 /* OptionItem.swift in Sources */ = {isa = PBXBuildFile; fileRef = 2BC15BCA2DC526E800F07B80 /* OptionItem.swift */; };
		2BC7EC832DC26A22007752AB /* Binding+Extensions.swift in Sources */ = {isa = PBXBuildFile; fileRef = 2BC7EC822DC26A19007752AB /* Binding+Extensions.swift */; };
		2BC7EC842DC26A22007752AB /* Binding+Extensions.swift in Sources */ = {isa = PBXBuildFile; fileRef = 2BC7EC822DC26A19007752AB /* Binding+Extensions.swift */; };
		2BC7EC852DC26A22007752AB /* Binding+Extensions.swift in Sources */ = {isa = PBXBuildFile; fileRef = 2BC7EC822DC26A19007752AB /* Binding+Extensions.swift */; };
		2BCC77C92D94127A0076F4BD /* TypeAliases.swift in Sources */ = {isa = PBXBuildFile; fileRef = 2BE15B602D88412300E1618D /* TypeAliases.swift */; };
		2BD3D69D2D5A1D9D0094DD4C /* HemoPlugin.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 2BD3D6942D5A1D9D0094DD4C /* HemoPlugin.framework */; };
		2BD3D6B32D5A1DD50094DD4C /* HemoPlugin.h in Headers */ = {isa = PBXBuildFile; fileRef = 2BD3D6B12D5A1DD50094DD4C /* HemoPlugin.h */; settings = {ATTRIBUTES = (Public, ); }; };
		2BD3D6B62D5A1DD90094DD4C /* HemoPluginTests.swift in Sources */ = {isa = PBXBuildFile; fileRef = 2BD3D6B42D5A1DD90094DD4C /* HemoPluginTests.swift */; };
		2BD564902DC4E2310098CA65 /* HemoProfileQuestionnaireRequest.swift in Sources */ = {isa = PBXBuildFile; fileRef = 2BD5648F2DC4E21F0098CA65 /* HemoProfileQuestionnaireRequest.swift */; };
		2BD564912DC4E2310098CA65 /* HemoProfileQuestionnaireRequest.swift in Sources */ = {isa = PBXBuildFile; fileRef = 2BD5648F2DC4E21F0098CA65 /* HemoProfileQuestionnaireRequest.swift */; };
		2BD564922DC4E2310098CA65 /* HemoProfileQuestionnaireRequest.swift in Sources */ = {isa = PBXBuildFile; fileRef = 2BD5648F2DC4E21F0098CA65 /* HemoProfileQuestionnaireRequest.swift */; };
		2BD564D92DC4F8460098CA65 /* BooleanChoiceQuestionView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 2BD564D82DC4F8460098CA65 /* BooleanChoiceQuestionView.swift */; };
		2BD564DA2DC4F8460098CA65 /* BooleanChoiceQuestionView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 2BD564D82DC4F8460098CA65 /* BooleanChoiceQuestionView.swift */; };
		2BD564DB2DC4F8460098CA65 /* BooleanChoiceQuestionView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 2BD564D82DC4F8460098CA65 /* BooleanChoiceQuestionView.swift */; };
		2BE15B612D88412300E1618D /* TypeAliases.swift in Sources */ = {isa = PBXBuildFile; fileRef = 2BE15B602D88412300E1618D /* TypeAliases.swift */; };
		2BF084762DCB9597003A785A /* AddBleedQuestionnaireSource.swift in Sources */ = {isa = PBXBuildFile; fileRef = 2BF084752DCB9596003A785A /* AddBleedQuestionnaireSource.swift */; };
		2BF084772DCB9597003A785A /* AddBleedQuestionnaireSource.swift in Sources */ = {isa = PBXBuildFile; fileRef = 2BF084752DCB9596003A785A /* AddBleedQuestionnaireSource.swift */; };
		2BF084782DCB9597003A785A /* AddBleedQuestionnaireSource.swift in Sources */ = {isa = PBXBuildFile; fileRef = 2BF084752DCB9596003A785A /* AddBleedQuestionnaireSource.swift */; };
		2BF0847A2DCB9FE4003A785A /* MultilineTextQuestionView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 2BF084792DCB9FE4003A785A /* MultilineTextQuestionView.swift */; };
		2BF0847B2DCB9FE4003A785A /* MultilineTextQuestionView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 2BF084792DCB9FE4003A785A /* MultilineTextQuestionView.swift */; };
		2BF0847C2DCB9FE4003A785A /* MultilineTextQuestionView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 2BF084792DCB9FE4003A785A /* MultilineTextQuestionView.swift */; };
		2BF0847E2DCBA119003A785A /* HumaSliderView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 2BF0847D2DCBA119003A785A /* HumaSliderView.swift */; };
		2BF0847F2DCBA119003A785A /* HumaSliderView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 2BF0847D2DCBA119003A785A /* HumaSliderView.swift */; };
		2BF084802DCBA119003A785A /* HumaSliderView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 2BF0847D2DCBA119003A785A /* HumaSliderView.swift */; };
		2BF084822DCBEC19003A785A /* SliderQuestionView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 2BF084812DCBEC19003A785A /* SliderQuestionView.swift */; };
		2BF084832DCBEC19003A785A /* SliderQuestionView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 2BF084812DCBEC19003A785A /* SliderQuestionView.swift */; };
		2BF084842DCBEC19003A785A /* SliderQuestionView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 2BF084812DCBEC19003A785A /* SliderQuestionView.swift */; };
		2BF084862DCBEF0A003A785A /* PhotoPickerQuestionView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 2BF084852DCBEF0A003A785A /* PhotoPickerQuestionView.swift */; };
		2BF084872DCBEF0A003A785A /* PhotoPickerQuestionView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 2BF084852DCBEF0A003A785A /* PhotoPickerQuestionView.swift */; };
		2BF084882DCBEF0A003A785A /* PhotoPickerQuestionView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 2BF084852DCBEF0A003A785A /* PhotoPickerQuestionView.swift */; };
		2BFBCE4F2DCDFFAB00AECDC6 /* MockUserObserveRepository.swift in Sources */ = {isa = PBXBuildFile; fileRef = 2BFBCE4E2DCDFFAB00AECDC6 /* MockUserObserveRepository.swift */; };
		2BFBCE502DCDFFAB00AECDC6 /* MockUserObserveRepository.swift in Sources */ = {isa = PBXBuildFile; fileRef = 2BFBCE4E2DCDFFAB00AECDC6 /* MockUserObserveRepository.swift */; };
		2BFBCE512DCDFFAB00AECDC6 /* MockUserObserveRepository.swift in Sources */ = {isa = PBXBuildFile; fileRef = 2BFBCE4E2DCDFFAB00AECDC6 /* MockUserObserveRepository.swift */; };
		2BFD316A2DCCDACE008E4367 /* MockModuleResultSubmitRepository.swift in Sources */ = {isa = PBXBuildFile; fileRef = 2BFD31692DCCDACE008E4367 /* MockModuleResultSubmitRepository.swift */; };
		2BFD316B2DCCDACE008E4367 /* MockModuleResultSubmitRepository.swift in Sources */ = {isa = PBXBuildFile; fileRef = 2BFD31692DCCDACE008E4367 /* MockModuleResultSubmitRepository.swift */; };
		2BFD316C2DCCDACE008E4367 /* MockModuleResultSubmitRepository.swift in Sources */ = {isa = PBXBuildFile; fileRef = 2BFD31692DCCDACE008E4367 /* MockModuleResultSubmitRepository.swift */; };
		2BFD316E2DCCDB48008E4367 /* MockConfiguration.swift in Sources */ = {isa = PBXBuildFile; fileRef = 2BFD316D2DCCDB47008E4367 /* MockConfiguration.swift */; };
		2BFD316F2DCCDB48008E4367 /* MockConfiguration.swift in Sources */ = {isa = PBXBuildFile; fileRef = 2BFD316D2DCCDB47008E4367 /* MockConfiguration.swift */; };
		2BFD31702DCCDB48008E4367 /* MockConfiguration.swift in Sources */ = {isa = PBXBuildFile; fileRef = 2BFD316D2DCCDB47008E4367 /* MockConfiguration.swift */; };
		7C2D70EACE19F6194EC82264 /* Pods_HemoPlugin.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 51C261135E79ED0D9D8D2928 /* Pods_HemoPlugin.framework */; };
		CE55B301C74579260FDE3D4E /* Pods_HemoPluginTests.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 5C703870FCC13A774B77776B /* Pods_HemoPluginTests.framework */; };
/* End PBXBuildFile section */

/* Begin PBXContainerItemProxy section */
		2B507FCA2D7899C5009A07FE /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = 2BD3D68B2D5A1D9D0094DD4C /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = 2BD3D6932D5A1D9D0094DD4C;
			remoteInfo = HemoPlugin;
		};
		2BD3D69E2D5A1D9D0094DD4C /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = 2BD3D68B2D5A1D9D0094DD4C /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = 2BD3D6932D5A1D9D0094DD4C;
			remoteInfo = HemoPlugin;
		};
/* End PBXContainerItemProxy section */

/* Begin PBXCopyFilesBuildPhase section */
		2B507FCC2D7899C5009A07FE /* Embed Frameworks */ = {
			isa = PBXCopyFilesBuildPhase;
			buildActionMask = 2147483647;
			dstPath = "";
			dstSubfolderSpec = 10;
			files = (
				2B507FC92D7899C5009A07FE /* HemoPlugin.framework in Embed Frameworks */,
			);
			name = "Embed Frameworks";
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXCopyFilesBuildPhase section */

/* Begin PBXFileReference section */
		2B1A31B22DA80E57008BA2AE /* CircleLoadingView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = CircleLoadingView.swift; sourceTree = "<group>"; };
		2B1A31B42DA80E57008BA2AE /* DatePickerView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = DatePickerView.swift; sourceTree = "<group>"; };
		2B1A31B52DA80E57008BA2AE /* HalfSheetHelper.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = HalfSheetHelper.swift; sourceTree = "<group>"; };
		2B1A31B62DA80E57008BA2AE /* HeaderView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = HeaderView.swift; sourceTree = "<group>"; };
		2B1A31B72DA80E57008BA2AE /* HumaSUIImageView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = HumaSUIImageView.swift; sourceTree = "<group>"; };
		2B1A31B82DA80E57008BA2AE /* HumaSUITextField.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = HumaSUITextField.swift; sourceTree = "<group>"; };
		2B1A31BB2DA80E57008BA2AE /* View+Extensions.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = "View+Extensions.swift"; sourceTree = "<group>"; };
		2B1A31BF2DA80E57008BA2AE /* CardStyleModifier.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = CardStyleModifier.swift; sourceTree = "<group>"; };
		2B1A31C02DA80E57008BA2AE /* Dimensions.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = Dimensions.swift; sourceTree = "<group>"; };
		2B1A31C12DA80E57008BA2AE /* PillButtonStyle.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = PillButtonStyle.swift; sourceTree = "<group>"; };
		2B1A31C22DA80E57008BA2AE /* ShimmerModifier.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = ShimmerModifier.swift; sourceTree = "<group>"; };
		2B1A31C32DA80E57008BA2AE /* SwiftUI+Fonts.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = "SwiftUI+Fonts.swift"; sourceTree = "<group>"; };
		2B1A31C42DA80E57008BA2AE /* TempStyler.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = TempStyler.swift; sourceTree = "<group>"; };
		2B1A31C52DA80E57008BA2AE /* UnderlineButtonStyle.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = UnderlineButtonStyle.swift; sourceTree = "<group>"; };
		2B1A31FB2DA81113008BA2AE /* PreviewModels.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = PreviewModels.swift; sourceTree = "<group>"; };
		2B1A32002DA8111D008BA2AE /* BodyMapViewModel.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = BodyMapViewModel.swift; sourceTree = "<group>"; };
		2B1A32022DA8111D008BA2AE /* BodyMapView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = BodyMapView.swift; sourceTree = "<group>"; };
		2B1A32032DA8111D008BA2AE /* CircularInitialView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = CircularInitialView.swift; sourceTree = "<group>"; };
		2B1A32132DA8117D008BA2AE /* AnyHumaNetworking+Route.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = "AnyHumaNetworking+Route.swift"; sourceTree = "<group>"; };
		2B1A32142DA8117D008BA2AE /* HemoJournalRepository.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = HemoJournalRepository.swift; sourceTree = "<group>"; };
		2B1A32162DA8117D008BA2AE /* HemoJournalWidget.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = HemoJournalWidget.swift; sourceTree = "<group>"; };
		2B1A32172DA8117D008BA2AE /* HemoJournalWidgetConfig.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = HemoJournalWidgetConfig.swift; sourceTree = "<group>"; };
		2B1A32182DA8117D008BA2AE /* HemoJournalWidgetCoordinator.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = HemoJournalWidgetCoordinator.swift; sourceTree = "<group>"; };
		2B1A32282DA811BB008BA2AE /* WidgetDataResponse.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = WidgetDataResponse.swift; sourceTree = "<group>"; };
		2B1A323F2DA8123A008BA2AE /* AnyHemoJournalCardViewModel.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = AnyHemoJournalCardViewModel.swift; sourceTree = "<group>"; };
		2B1A32402DA8123A008BA2AE /* HemoJournalCardViewModel.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = HemoJournalCardViewModel.swift; sourceTree = "<group>"; };
		2B1A32432DA8123A008BA2AE /* HemoJournalCardView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = HemoJournalCardView.swift; sourceTree = "<group>"; };
		2B1A32552DA81289008BA2AE /* AnyHeaderViewModel.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = AnyHeaderViewModel.swift; sourceTree = "<group>"; };
		2B1A32562DA81289008BA2AE /* HeaderViewModel.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = HeaderViewModel.swift; sourceTree = "<group>"; };
		2B1A32572DA81289008BA2AE /* PreviewHeaderViewModel.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = PreviewHeaderViewModel.swift; sourceTree = "<group>"; };
		2B1A32592DA81289008BA2AE /* JournalHeaderView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = JournalHeaderView.swift; sourceTree = "<group>"; };
		2B1A326D2DA813BD008BA2AE /* LoadingView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = LoadingView.swift; sourceTree = "<group>"; };
		2B1A32712DA813C7008BA2AE /* AddBleedViewModel.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = AddBleedViewModel.swift; sourceTree = "<group>"; };
		2B1A32732DA813C7008BA2AE /* AddBleedView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = AddBleedView.swift; sourceTree = "<group>"; };
		2B1A32762DA813C7008BA2AE /* CustomBodyPartView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = CustomBodyPartView.swift; sourceTree = "<group>"; };
		2B1A32842DA81596008BA2AE /* Assets.xcassets */ = {isa = PBXFileReference; lastKnownFileType = folder.assetcatalog; path = Assets.xcassets; sourceTree = "<group>"; };
		2B2FECDE2DC123AA00A699CA /* ActiveProfileCardViewModel.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = ActiveProfileCardViewModel.swift; sourceTree = "<group>"; };
		2B2FECE02DC123AA00A699CA /* ActiveProfileCardView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = ActiveProfileCardView.swift; sourceTree = "<group>"; };
		2B2FECE42DC123AA00A699CA /* ReusableListViewModel.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = ReusableListViewModel.swift; sourceTree = "<group>"; };
		2B2FECE62DC123AA00A699CA /* ReusableListView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = ReusableListView.swift; sourceTree = "<group>"; };
		2B2FECE82DC123AA00A699CA /* SetupProfileCardViewModel.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = SetupProfileCardViewModel.swift; sourceTree = "<group>"; };
		2B2FECEA2DC123AA00A699CA /* SetupProfileCardView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = SetupProfileCardView.swift; sourceTree = "<group>"; };
		2B2FECFE2DC1240200A699CA /* HemoJournalCardViewModel+Preview.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = "HemoJournalCardViewModel+Preview.swift"; sourceTree = "<group>"; };
		2B2FED032DC127AC00A699CA /* Answer.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = Answer.swift; sourceTree = "<group>"; };
		2B2FED042DC127AC00A699CA /* Question.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = Question.swift; sourceTree = "<group>"; };
		2B2FED052DC127AC00A699CA /* QuestionType.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = QuestionType.swift; sourceTree = "<group>"; };
		2B2FED072DC127AC00A699CA /* ValidationRule.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = ValidationRule.swift; sourceTree = "<group>"; };
		2B2FED092DC127AC00A699CA /* HemoProfileQuestionnaireSource.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = HemoProfileQuestionnaireSource.swift; sourceTree = "<group>"; };
		2B2FED0B2DC127AC00A699CA /* QuestionnaireViewModel.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = QuestionnaireViewModel.swift; sourceTree = "<group>"; };
		2B2FED0E2DC127AC00A699CA /* TagView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = TagView.swift; sourceTree = "<group>"; };
		2B2FED102DC127AC00A699CA /* AutocompleteQuestionView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = AutocompleteQuestionView.swift; sourceTree = "<group>"; };
		2B2FED112DC127AC00A699CA /* DateQuestionView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = DateQuestionView.swift; sourceTree = "<group>"; };
		2B2FED122DC127AC00A699CA /* MultipleChoiceQuestionView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = MultipleChoiceQuestionView.swift; sourceTree = "<group>"; };
		2B2FED132DC127AC00A699CA /* NumericQuestionView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = NumericQuestionView.swift; sourceTree = "<group>"; };
		2B2FED142DC127AC00A699CA /* PickerQuestionView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = PickerQuestionView.swift; sourceTree = "<group>"; };
		2B2FED152DC127AC00A699CA /* SingleChoiceQuestionView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = SingleChoiceQuestionView.swift; sourceTree = "<group>"; };
		2B2FED162DC127AC00A699CA /* TextQuestionView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = TextQuestionView.swift; sourceTree = "<group>"; };
		2B2FED172DC127AC00A699CA /* TimeQuestionView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = TimeQuestionView.swift; sourceTree = "<group>"; };
		2B2FED182DC127AC00A699CA /* ValueUnitQuestionView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = ValueUnitQuestionView.swift; sourceTree = "<group>"; };
		2B2FED1A2DC127AC00A699CA /* QuestionnaireView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = QuestionnaireView.swift; sourceTree = "<group>"; };
		2B2FED1B2DC127AC00A699CA /* QuestionView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = QuestionView.swift; sourceTree = "<group>"; };
		2B332CC42DC370F30056C4CD /* MockMedicationRepository.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = MockMedicationRepository.swift; sourceTree = "<group>"; };
		2B332CC82DC37F560056C4CD /* HTMLText.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = HTMLText.swift; sourceTree = "<group>"; };
		2B46C46D2DD4D3BF006D0619 /* BleedDetailsView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = BleedDetailsView.swift; sourceTree = "<group>"; };
		2B46C46F2DD4D3BF006D0619 /* BodyMapLogView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = BodyMapLogView.swift; sourceTree = "<group>"; };
		2B46C4772DD4D3F4006D0619 /* MockModuleResultRepository.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = MockModuleResultRepository.swift; sourceTree = "<group>"; };
		2B46C47B2DD4D631006D0619 /* MockFileRepository.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = MockFileRepository.swift; sourceTree = "<group>"; };
		2B507FCD2D7899E7009A07FE /* HumaFoundation.framework */ = {isa = PBXFileReference; explicitFileType = wrapper.framework; path = HumaFoundation.framework; sourceTree = BUILT_PRODUCTS_DIR; };
		2B507FCE2D7899E7009A07FE /* HumaLearnKit.framework */ = {isa = PBXFileReference; explicitFileType = wrapper.framework; path = HumaLearnKit.framework; sourceTree = BUILT_PRODUCTS_DIR; };
		2B507FCF2D7899E7009A07FE /* HumaModuleKit.framework */ = {isa = PBXFileReference; explicitFileType = wrapper.framework; path = HumaModuleKit.framework; sourceTree = BUILT_PRODUCTS_DIR; };
		2B507FD02D7899E7009A07FE /* HumaModules.framework */ = {isa = PBXFileReference; explicitFileType = wrapper.framework; path = HumaModules.framework; sourceTree = BUILT_PRODUCTS_DIR; };
		2B507FD12D7899E7009A07FE /* HumaQuestionnaire.framework */ = {isa = PBXFileReference; explicitFileType = wrapper.framework; path = HumaQuestionnaire.framework; sourceTree = BUILT_PRODUCTS_DIR; };
		2B507FD22D7899E7009A07FE /* HumaWidgetKit.framework */ = {isa = PBXFileReference; explicitFileType = wrapper.framework; path = HumaWidgetKit.framework; sourceTree = BUILT_PRODUCTS_DIR; };
		2B75478B2DC36AAB00D10E65 /* MedicationRepository.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = MedicationRepository.swift; sourceTree = "<group>"; };
		2B75478F2DC36AFA00D10E65 /* CMSMedicationResponse.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = CMSMedicationResponse.swift; sourceTree = "<group>"; };
		2B7547942DC36B3B00D10E65 /* CMSMedicationRequest.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = CMSMedicationRequest.swift; sourceTree = "<group>"; };
		2B7EBAF02D786EA1005747CD /* HemoPluginSampleApp.app */ = {isa = PBXFileReference; explicitFileType = wrapper.application; includeInIndex = 0; path = HemoPluginSampleApp.app; sourceTree = BUILT_PRODUCTS_DIR; };
		2B7EBAFE2D786EC5005747CD /* Preview Assets.xcassets */ = {isa = PBXFileReference; lastKnownFileType = folder.assetcatalog; path = "Preview Assets.xcassets"; sourceTree = "<group>"; };
		2B7EBB002D786EC5005747CD /* Assets.xcassets */ = {isa = PBXFileReference; lastKnownFileType = folder.assetcatalog; path = Assets.xcassets; sourceTree = "<group>"; };
		2B7EBB012D786EC5005747CD /* ContentView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = ContentView.swift; sourceTree = "<group>"; };
		2B7EBB022D786EC5005747CD /* HemoPluginSampleAppApp.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = HemoPluginSampleAppApp.swift; sourceTree = "<group>"; };
		2B8489982DD5F35300CA53F5 /* BleedDetailsViewModel.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = BleedDetailsViewModel.swift; sourceTree = "<group>"; };
		2B84899D2DD5F36800CA53F5 /* BleedHistoryViewModel.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = BleedHistoryViewModel.swift; sourceTree = "<group>"; };
		2B8489A22DD5F37300CA53F5 /* PhotoLogViewModel.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = PhotoLogViewModel.swift; sourceTree = "<group>"; };
		2B8489A42DD5F37300CA53F5 /* PhotoLogView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = PhotoLogView.swift; sourceTree = "<group>"; };
		2BB39F432DD23477003762D7 /* DetailsViewModel.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = DetailsViewModel.swift; sourceTree = "<group>"; };
		2BB39F452DD23477003762D7 /* DetailsView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = DetailsView.swift; sourceTree = "<group>"; };
		2BB39F4E2DD24129003762D7 /* BleedHistoryListView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = BleedHistoryListView.swift; sourceTree = "<group>"; };
		2BC15BCA2DC526E800F07B80 /* OptionItem.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = OptionItem.swift; sourceTree = "<group>"; };
		2BC7EC822DC26A19007752AB /* Binding+Extensions.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = "Binding+Extensions.swift"; sourceTree = "<group>"; };
		2BD3D6942D5A1D9D0094DD4C /* HemoPlugin.framework */ = {isa = PBXFileReference; explicitFileType = wrapper.framework; includeInIndex = 0; path = HemoPlugin.framework; sourceTree = BUILT_PRODUCTS_DIR; };
		2BD3D69C2D5A1D9D0094DD4C /* HemoPluginTests.xctest */ = {isa = PBXFileReference; explicitFileType = wrapper.cfbundle; includeInIndex = 0; path = HemoPluginTests.xctest; sourceTree = BUILT_PRODUCTS_DIR; };
		2BD3D6B12D5A1DD50094DD4C /* HemoPlugin.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = HemoPlugin.h; sourceTree = "<group>"; };
		2BD3D6B42D5A1DD90094DD4C /* HemoPluginTests.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = HemoPluginTests.swift; sourceTree = "<group>"; };
		2BD5648F2DC4E21F0098CA65 /* HemoProfileQuestionnaireRequest.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = HemoProfileQuestionnaireRequest.swift; sourceTree = "<group>"; };
		2BD564D82DC4F8460098CA65 /* BooleanChoiceQuestionView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = BooleanChoiceQuestionView.swift; sourceTree = "<group>"; };
		2BE15B602D88412300E1618D /* TypeAliases.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = TypeAliases.swift; sourceTree = "<group>"; };
		2BF084752DCB9596003A785A /* AddBleedQuestionnaireSource.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = AddBleedQuestionnaireSource.swift; sourceTree = "<group>"; };
		2BF084792DCB9FE4003A785A /* MultilineTextQuestionView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = MultilineTextQuestionView.swift; sourceTree = "<group>"; };
		2BF0847D2DCBA119003A785A /* HumaSliderView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = HumaSliderView.swift; sourceTree = "<group>"; };
		2BF084812DCBEC19003A785A /* SliderQuestionView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = SliderQuestionView.swift; sourceTree = "<group>"; };
		2BF084852DCBEF0A003A785A /* PhotoPickerQuestionView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = PhotoPickerQuestionView.swift; sourceTree = "<group>"; };
		2BFBCE4E2DCDFFAB00AECDC6 /* MockUserObserveRepository.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = MockUserObserveRepository.swift; sourceTree = "<group>"; };
		2BFD31692DCCDACE008E4367 /* MockModuleResultSubmitRepository.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = MockModuleResultSubmitRepository.swift; sourceTree = "<group>"; };
		2BFD316D2DCCDB47008E4367 /* MockConfiguration.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = MockConfiguration.swift; sourceTree = "<group>"; };
		4A91E0470E10180B3078EF9E /* Pods-HemoPluginSampleApp.debug.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-HemoPluginSampleApp.debug.xcconfig"; path = "Target Support Files/Pods-HemoPluginSampleApp/Pods-HemoPluginSampleApp.debug.xcconfig"; sourceTree = "<group>"; };
		51C261135E79ED0D9D8D2928 /* Pods_HemoPlugin.framework */ = {isa = PBXFileReference; explicitFileType = wrapper.framework; includeInIndex = 0; path = Pods_HemoPlugin.framework; sourceTree = BUILT_PRODUCTS_DIR; };
		5C703870FCC13A774B77776B /* Pods_HemoPluginTests.framework */ = {isa = PBXFileReference; explicitFileType = wrapper.framework; includeInIndex = 0; path = Pods_HemoPluginTests.framework; sourceTree = BUILT_PRODUCTS_DIR; };
		5EE48250D6BA96A3C7604AEA /* Pods-HemoPlugin.debug.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-HemoPlugin.debug.xcconfig"; path = "Target Support Files/Pods-HemoPlugin/Pods-HemoPlugin.debug.xcconfig"; sourceTree = "<group>"; };
		6A3ABD4E3558E8F18B75EECA /* Pods_HemoPluginSampleApp.framework */ = {isa = PBXFileReference; explicitFileType = wrapper.framework; includeInIndex = 0; path = Pods_HemoPluginSampleApp.framework; sourceTree = BUILT_PRODUCTS_DIR; };
		71B8D94A2A81421355C5A6D5 /* Pods-HemoPluginTests.release.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-HemoPluginTests.release.xcconfig"; path = "Target Support Files/Pods-HemoPluginTests/Pods-HemoPluginTests.release.xcconfig"; sourceTree = "<group>"; };
		C91EE309E5D6C1103676E7D4 /* Pods-HemoPluginSampleApp.release.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-HemoPluginSampleApp.release.xcconfig"; path = "Target Support Files/Pods-HemoPluginSampleApp/Pods-HemoPluginSampleApp.release.xcconfig"; sourceTree = "<group>"; };
		ECFC25D94790DE267C89D9AD /* Pods-HemoPlugin.release.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-HemoPlugin.release.xcconfig"; path = "Target Support Files/Pods-HemoPlugin/Pods-HemoPlugin.release.xcconfig"; sourceTree = "<group>"; };
		F3E11B1CF0ECF8F674D34326 /* Pods-HemoPluginTests.debug.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-HemoPluginTests.debug.xcconfig"; path = "Target Support Files/Pods-HemoPluginTests/Pods-HemoPluginTests.debug.xcconfig"; sourceTree = "<group>"; };
/* End PBXFileReference section */

/* Begin PBXFrameworksBuildPhase section */
		2B7EBAED2D786EA1005747CD /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
				2B507FDB2D7899E7009A07FE /* HumaQuestionnaire.framework in Frameworks */,
				2B507FC82D7899C5009A07FE /* HemoPlugin.framework in Frameworks */,
				2B507FDD2D7899E7009A07FE /* HumaWidgetKit.framework in Frameworks */,
				00A90FA442F996C7551C02EC /* Pods_HemoPluginSampleApp.framework in Frameworks */,
				2B507FD32D7899E7009A07FE /* HumaFoundation.framework in Frameworks */,
				2B507FD92D7899E7009A07FE /* HumaModules.framework in Frameworks */,
				2B507FD52D7899E7009A07FE /* HumaLearnKit.framework in Frameworks */,
				2B507FD72D7899E7009A07FE /* HumaModuleKit.framework in Frameworks */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		2BD3D6912D5A1D9D0094DD4C /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
				7C2D70EACE19F6194EC82264 /* Pods_HemoPlugin.framework in Frameworks */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		2BD3D6992D5A1D9D0094DD4C /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
				2BD3D69D2D5A1D9D0094DD4C /* HemoPlugin.framework in Frameworks */,
				CE55B301C74579260FDE3D4E /* Pods_HemoPluginTests.framework in Frameworks */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXFrameworksBuildPhase section */

/* Begin PBXGroup section */
		19A62248E493C2FA42F607ED /* Pods */ = {
			isa = PBXGroup;
			children = (
				5EE48250D6BA96A3C7604AEA /* Pods-HemoPlugin.debug.xcconfig */,
				ECFC25D94790DE267C89D9AD /* Pods-HemoPlugin.release.xcconfig */,
				F3E11B1CF0ECF8F674D34326 /* Pods-HemoPluginTests.debug.xcconfig */,
				71B8D94A2A81421355C5A6D5 /* Pods-HemoPluginTests.release.xcconfig */,
				4A91E0470E10180B3078EF9E /* Pods-HemoPluginSampleApp.debug.xcconfig */,
				C91EE309E5D6C1103676E7D4 /* Pods-HemoPluginSampleApp.release.xcconfig */,
			);
			name = Pods;
			path = ../Pods;
			sourceTree = "<group>";
		};
		2B1A31BA2DA80E57008BA2AE /* Component */ = {
			isa = PBXGroup;
			children = (
				2BF0847D2DCBA119003A785A /* HumaSliderView.swift */,
				2B332CC82DC37F560056C4CD /* HTMLText.swift */,
				2B1A31B22DA80E57008BA2AE /* CircleLoadingView.swift */,
				2B1A31B42DA80E57008BA2AE /* DatePickerView.swift */,
				2B1A31B52DA80E57008BA2AE /* HalfSheetHelper.swift */,
				2B1A31B62DA80E57008BA2AE /* HeaderView.swift */,
				2B1A31B72DA80E57008BA2AE /* HumaSUIImageView.swift */,
				2B1A31B82DA80E57008BA2AE /* HumaSUITextField.swift */,
				2B2FED0E2DC127AC00A699CA /* TagView.swift */,
			);
			path = Component;
			sourceTree = "<group>";
		};
		2B1A31BC2DA80E57008BA2AE /* Extensions */ = {
			isa = PBXGroup;
			children = (
				2BC7EC822DC26A19007752AB /* Binding+Extensions.swift */,
				2B1A31BB2DA80E57008BA2AE /* View+Extensions.swift */,
			);
			path = Extensions;
			sourceTree = "<group>";
		};
		2B1A31C62DA80E57008BA2AE /* Styles */ = {
			isa = PBXGroup;
			children = (
				2B1A31BF2DA80E57008BA2AE /* CardStyleModifier.swift */,
				2B1A31C02DA80E57008BA2AE /* Dimensions.swift */,
				2B1A31C12DA80E57008BA2AE /* PillButtonStyle.swift */,
				2B1A31C22DA80E57008BA2AE /* ShimmerModifier.swift */,
				2B1A31C32DA80E57008BA2AE /* SwiftUI+Fonts.swift */,
				2B1A31C42DA80E57008BA2AE /* TempStyler.swift */,
				2B1A31C52DA80E57008BA2AE /* UnderlineButtonStyle.swift */,
			);
			path = Styles;
			sourceTree = "<group>";
		};
		2B1A31C72DA80E57008BA2AE /* SwiftUI */ = {
			isa = PBXGroup;
			children = (
				2B1A31FC2DA81113008BA2AE /* PreviewModels */,
				2B1A31BA2DA80E57008BA2AE /* Component */,
				2B1A31BC2DA80E57008BA2AE /* Extensions */,
				2B1A31C62DA80E57008BA2AE /* Styles */,
			);
			path = SwiftUI;
			sourceTree = "<group>";
		};
		2B1A31FC2DA81113008BA2AE /* PreviewModels */ = {
			isa = PBXGroup;
			children = (
				2B46C47B2DD4D631006D0619 /* MockFileRepository.swift */,
				2B46C4772DD4D3F4006D0619 /* MockModuleResultRepository.swift */,
				2BFBCE4E2DCDFFAB00AECDC6 /* MockUserObserveRepository.swift */,
				2BFD316D2DCCDB47008E4367 /* MockConfiguration.swift */,
				2BFD31692DCCDACE008E4367 /* MockModuleResultSubmitRepository.swift */,
				2B332CC42DC370F30056C4CD /* MockMedicationRepository.swift */,
				2B1A31FB2DA81113008BA2AE /* PreviewModels.swift */,
			);
			path = PreviewModels;
			sourceTree = "<group>";
		};
		2B1A32012DA8111D008BA2AE /* ViewModel */ = {
			isa = PBXGroup;
			children = (
				2B1A32002DA8111D008BA2AE /* BodyMapViewModel.swift */,
			);
			path = ViewModel;
			sourceTree = "<group>";
		};
		2B1A32042DA8111D008BA2AE /* BodyMap */ = {
			isa = PBXGroup;
			children = (
				2B1A32012DA8111D008BA2AE /* ViewModel */,
				2B1A32022DA8111D008BA2AE /* BodyMapView.swift */,
				2B1A32032DA8111D008BA2AE /* CircularInitialView.swift */,
			);
			path = BodyMap;
			sourceTree = "<group>";
		};
		2B1A32072DA8111D008BA2AE /* Views */ = {
			isa = PBXGroup;
			children = (
				2B8489A52DD5F37300CA53F5 /* PhotoLogView */,
				2B46C46E2DD4D3BF006D0619 /* BleedDetailsView */,
				2B46C4702DD4D3BF006D0619 /* BodyMapLogView */,
				2BB39F4D2DD2410C003762D7 /* BleedHistoryListView */,
				2BB39F462DD23477003762D7 /* DetailsView */,
				2BC7EC7D2DC23B32007752AB /* HemoProfileQuestionnaire */,
				2B2FED022DC127A500A699CA /* Questionnaire */,
				2B2FECE12DC123AA00A699CA /* ActiveProfileCard */,
				2B2FECE72DC123AA00A699CA /* ReusableListView */,
				2B2FECEB2DC123AA00A699CA /* SetupProfileCard */,
				2B1A32772DA813C7008BA2AE /* AddBleedFlow */,
				2B1A325A2DA81289008BA2AE /* Header */,
				2B1A32442DA8123A008BA2AE /* WidgetCard */,
				2B1A32042DA8111D008BA2AE /* BodyMap */,
				2B1A326D2DA813BD008BA2AE /* LoadingView.swift */,
			);
			path = Views;
			sourceTree = "<group>";
		};
		2B1A32082DA8111D008BA2AE /* Journal */ = {
			isa = PBXGroup;
			children = (
				2B1A32292DA811BB008BA2AE /* Models */,
				2B1A32152DA8117D008BA2AE /* API */,
				2B1A32072DA8111D008BA2AE /* Views */,
				2B1A32162DA8117D008BA2AE /* HemoJournalWidget.swift */,
				2B1A32172DA8117D008BA2AE /* HemoJournalWidgetConfig.swift */,
				2B1A32182DA8117D008BA2AE /* HemoJournalWidgetCoordinator.swift */,
			);
			path = Journal;
			sourceTree = "<group>";
		};
		2B1A32092DA8111D008BA2AE /* Widgets */ = {
			isa = PBXGroup;
			children = (
				2B1A32082DA8111D008BA2AE /* Journal */,
			);
			path = Widgets;
			sourceTree = "<group>";
		};
		2B1A32152DA8117D008BA2AE /* API */ = {
			isa = PBXGroup;
			children = (
				2B75478B2DC36AAB00D10E65 /* MedicationRepository.swift */,
				2B1A32132DA8117D008BA2AE /* AnyHumaNetworking+Route.swift */,
				2B1A32142DA8117D008BA2AE /* HemoJournalRepository.swift */,
			);
			path = API;
			sourceTree = "<group>";
		};
		2B1A32292DA811BB008BA2AE /* Models */ = {
			isa = PBXGroup;
			children = (
				2BD5648F2DC4E21F0098CA65 /* HemoProfileQuestionnaireRequest.swift */,
				2B7547942DC36B3B00D10E65 /* CMSMedicationRequest.swift */,
				2B75478F2DC36AFA00D10E65 /* CMSMedicationResponse.swift */,
				2B1A32282DA811BB008BA2AE /* WidgetDataResponse.swift */,
			);
			path = Models;
			sourceTree = "<group>";
		};
		2B1A32422DA8123A008BA2AE /* ViewModels */ = {
			isa = PBXGroup;
			children = (
				2B2FECFE2DC1240200A699CA /* HemoJournalCardViewModel+Preview.swift */,
				2B1A323F2DA8123A008BA2AE /* AnyHemoJournalCardViewModel.swift */,
				2B1A32402DA8123A008BA2AE /* HemoJournalCardViewModel.swift */,
			);
			path = ViewModels;
			sourceTree = "<group>";
		};
		2B1A32442DA8123A008BA2AE /* WidgetCard */ = {
			isa = PBXGroup;
			children = (
				2B1A32422DA8123A008BA2AE /* ViewModels */,
				2B1A32432DA8123A008BA2AE /* HemoJournalCardView.swift */,
			);
			path = WidgetCard;
			sourceTree = "<group>";
		};
		2B1A32582DA81289008BA2AE /* ViewModel */ = {
			isa = PBXGroup;
			children = (
				2B1A32552DA81289008BA2AE /* AnyHeaderViewModel.swift */,
				2B1A32562DA81289008BA2AE /* HeaderViewModel.swift */,
				2B1A32572DA81289008BA2AE /* PreviewHeaderViewModel.swift */,
			);
			path = ViewModel;
			sourceTree = "<group>";
		};
		2B1A325A2DA81289008BA2AE /* Header */ = {
			isa = PBXGroup;
			children = (
				2B1A32582DA81289008BA2AE /* ViewModel */,
				2B1A32592DA81289008BA2AE /* JournalHeaderView.swift */,
			);
			path = Header;
			sourceTree = "<group>";
		};
		2B1A32722DA813C7008BA2AE /* ViewModel */ = {
			isa = PBXGroup;
			children = (
				2B1A32712DA813C7008BA2AE /* AddBleedViewModel.swift */,
			);
			path = ViewModel;
			sourceTree = "<group>";
		};
		2B1A32742DA813C7008BA2AE /* AddBleedView */ = {
			isa = PBXGroup;
			children = (
				2B1A32722DA813C7008BA2AE /* ViewModel */,
				2B1A32732DA813C7008BA2AE /* AddBleedView.swift */,
			);
			path = AddBleedView;
			sourceTree = "<group>";
		};
		2B1A32772DA813C7008BA2AE /* AddBleedFlow */ = {
			isa = PBXGroup;
			children = (
				2BF084742DCB9566003A785A /* AddBleedQuestionnaire */,
				2B1A32742DA813C7008BA2AE /* AddBleedView */,
				2B1A32762DA813C7008BA2AE /* CustomBodyPartView.swift */,
			);
			path = AddBleedFlow;
			sourceTree = "<group>";
		};
		2B2FECDF2DC123AA00A699CA /* ViewModel */ = {
			isa = PBXGroup;
			children = (
				2B2FECDE2DC123AA00A699CA /* ActiveProfileCardViewModel.swift */,
			);
			path = ViewModel;
			sourceTree = "<group>";
		};
		2B2FECE12DC123AA00A699CA /* ActiveProfileCard */ = {
			isa = PBXGroup;
			children = (
				2B2FECDF2DC123AA00A699CA /* ViewModel */,
				2B2FECE02DC123AA00A699CA /* ActiveProfileCardView.swift */,
			);
			path = ActiveProfileCard;
			sourceTree = "<group>";
		};
		2B2FECE52DC123AA00A699CA /* ViewModel */ = {
			isa = PBXGroup;
			children = (
				2B2FECE42DC123AA00A699CA /* ReusableListViewModel.swift */,
			);
			path = ViewModel;
			sourceTree = "<group>";
		};
		2B2FECE72DC123AA00A699CA /* ReusableListView */ = {
			isa = PBXGroup;
			children = (
				2B2FECE52DC123AA00A699CA /* ViewModel */,
				2B2FECE62DC123AA00A699CA /* ReusableListView.swift */,
			);
			path = ReusableListView;
			sourceTree = "<group>";
		};
		2B2FECE92DC123AA00A699CA /* ViewModel */ = {
			isa = PBXGroup;
			children = (
				2B2FECE82DC123AA00A699CA /* SetupProfileCardViewModel.swift */,
			);
			path = ViewModel;
			sourceTree = "<group>";
		};
		2B2FECEB2DC123AA00A699CA /* SetupProfileCard */ = {
			isa = PBXGroup;
			children = (
				2B2FECE92DC123AA00A699CA /* ViewModel */,
				2B2FECEA2DC123AA00A699CA /* SetupProfileCardView.swift */,
			);
			path = SetupProfileCard;
			sourceTree = "<group>";
		};
		2B2FED022DC127A500A699CA /* Questionnaire */ = {
			isa = PBXGroup;
			children = (
				2B2FED082DC127AC00A699CA /* Models */,
				2B2FED1C2DC127AC00A699CA /* Views */,
				2B2FED0C2DC127AC00A699CA /* ViewModels */,
			);
			path = Questionnaire;
			sourceTree = "<group>";
		};
		2B2FED082DC127AC00A699CA /* Models */ = {
			isa = PBXGroup;
			children = (
				2BC15BCA2DC526E800F07B80 /* OptionItem.swift */,
				2B2FED032DC127AC00A699CA /* Answer.swift */,
				2B2FED042DC127AC00A699CA /* Question.swift */,
				2B2FED052DC127AC00A699CA /* QuestionType.swift */,
				2B2FED072DC127AC00A699CA /* ValidationRule.swift */,
			);
			path = Models;
			sourceTree = "<group>";
		};
		2B2FED0C2DC127AC00A699CA /* ViewModels */ = {
			isa = PBXGroup;
			children = (
				2B2FED0B2DC127AC00A699CA /* QuestionnaireViewModel.swift */,
			);
			path = ViewModels;
			sourceTree = "<group>";
		};
		2B2FED192DC127AC00A699CA /* QuestionViews */ = {
			isa = PBXGroup;
			children = (
				2BF084852DCBEF0A003A785A /* PhotoPickerQuestionView.swift */,
				2BF084812DCBEC19003A785A /* SliderQuestionView.swift */,
				2BF084792DCB9FE4003A785A /* MultilineTextQuestionView.swift */,
				2B2FED102DC127AC00A699CA /* AutocompleteQuestionView.swift */,
				2B2FED112DC127AC00A699CA /* DateQuestionView.swift */,
				2B2FED122DC127AC00A699CA /* MultipleChoiceQuestionView.swift */,
				2B2FED132DC127AC00A699CA /* NumericQuestionView.swift */,
				2B2FED142DC127AC00A699CA /* PickerQuestionView.swift */,
				2B2FED152DC127AC00A699CA /* SingleChoiceQuestionView.swift */,
				2BD564D82DC4F8460098CA65 /* BooleanChoiceQuestionView.swift */,
				2B2FED162DC127AC00A699CA /* TextQuestionView.swift */,
				2B2FED172DC127AC00A699CA /* TimeQuestionView.swift */,
				2B2FED182DC127AC00A699CA /* ValueUnitQuestionView.swift */,
			);
			path = QuestionViews;
			sourceTree = "<group>";
		};
		2B2FED1C2DC127AC00A699CA /* Views */ = {
			isa = PBXGroup;
			children = (
				2B2FED192DC127AC00A699CA /* QuestionViews */,
				2B2FED1A2DC127AC00A699CA /* QuestionnaireView.swift */,
				2B2FED1B2DC127AC00A699CA /* QuestionView.swift */,
			);
			path = Views;
			sourceTree = "<group>";
		};
		2B46C46E2DD4D3BF006D0619 /* BleedDetailsView */ = {
			isa = PBXGroup;
			children = (
				2B8489992DD5F35300CA53F5 /* ViewModel */,
				2B46C46D2DD4D3BF006D0619 /* BleedDetailsView.swift */,
			);
			path = BleedDetailsView;
			sourceTree = "<group>";
		};
		2B46C4702DD4D3BF006D0619 /* BodyMapLogView */ = {
			isa = PBXGroup;
			children = (
				2B46C46F2DD4D3BF006D0619 /* BodyMapLogView.swift */,
			);
			path = BodyMapLogView;
			sourceTree = "<group>";
		};
		2B7EBAFF2D786EC5005747CD /* Preview Content */ = {
			isa = PBXGroup;
			children = (
				2B7EBAFE2D786EC5005747CD /* Preview Assets.xcassets */,
			);
			path = "Preview Content";
			sourceTree = "<group>";
		};
		2B7EBB032D786EC5005747CD /* HemoPluginSampleApp */ = {
			isa = PBXGroup;
			children = (
				2B7EBAFF2D786EC5005747CD /* Preview Content */,
				2B7EBB002D786EC5005747CD /* Assets.xcassets */,
				2B7EBB012D786EC5005747CD /* ContentView.swift */,
				2B7EBB022D786EC5005747CD /* HemoPluginSampleAppApp.swift */,
			);
			path = HemoPluginSampleApp;
			sourceTree = "<group>";
		};
		2B8489992DD5F35300CA53F5 /* ViewModel */ = {
			isa = PBXGroup;
			children = (
				2B8489982DD5F35300CA53F5 /* BleedDetailsViewModel.swift */,
			);
			path = ViewModel;
			sourceTree = "<group>";
		};
		2B84899E2DD5F36800CA53F5 /* ViewModel */ = {
			isa = PBXGroup;
			children = (
				2B84899D2DD5F36800CA53F5 /* BleedHistoryViewModel.swift */,
			);
			path = ViewModel;
			sourceTree = "<group>";
		};
		2B8489A32DD5F37300CA53F5 /* ViewModel */ = {
			isa = PBXGroup;
			children = (
				2B8489A22DD5F37300CA53F5 /* PhotoLogViewModel.swift */,
			);
			path = ViewModel;
			sourceTree = "<group>";
		};
		2B8489A52DD5F37300CA53F5 /* PhotoLogView */ = {
			isa = PBXGroup;
			children = (
				2B8489A32DD5F37300CA53F5 /* ViewModel */,
				2B8489A42DD5F37300CA53F5 /* PhotoLogView.swift */,
			);
			path = PhotoLogView;
			sourceTree = "<group>";
		};
		2BB39F442DD23477003762D7 /* ViewModel */ = {
			isa = PBXGroup;
			children = (
				2BB39F432DD23477003762D7 /* DetailsViewModel.swift */,
			);
			path = ViewModel;
			sourceTree = "<group>";
		};
		2BB39F462DD23477003762D7 /* DetailsView */ = {
			isa = PBXGroup;
			children = (
				2BB39F442DD23477003762D7 /* ViewModel */,
				2BB39F452DD23477003762D7 /* DetailsView.swift */,
			);
			path = DetailsView;
			sourceTree = "<group>";
		};
		2BB39F4D2DD2410C003762D7 /* BleedHistoryListView */ = {
			isa = PBXGroup;
			children = (
				2B84899E2DD5F36800CA53F5 /* ViewModel */,
				2BB39F4E2DD24129003762D7 /* BleedHistoryListView.swift */,
			);
			path = BleedHistoryListView;
			sourceTree = "<group>";
		};
		2BC626422D9BEE80000C70A1 /* Source */ = {
			isa = PBXGroup;
			children = (
				2B1A32092DA8111D008BA2AE /* Widgets */,
				2B1A31C72DA80E57008BA2AE /* SwiftUI */,
			);
			path = Source;
			sourceTree = "<group>";
		};
		2BC7EC7D2DC23B32007752AB /* HemoProfileQuestionnaire */ = {
			isa = PBXGroup;
			children = (
				2B2FED092DC127AC00A699CA /* HemoProfileQuestionnaireSource.swift */,
			);
			path = HemoProfileQuestionnaire;
			sourceTree = "<group>";
		};
		2BCC77B92D940F260076F4BD /* Resources */ = {
			isa = PBXGroup;
			children = (
				2B1A32842DA81596008BA2AE /* Assets.xcassets */,
			);
			path = Resources;
			sourceTree = "<group>";
		};
		2BD3D68A2D5A1D9D0094DD4C = {
			isa = PBXGroup;
			children = (
				2BD3D6B22D5A1DD50094DD4C /* HemoPlugin */,
				2BD3D6B52D5A1DD90094DD4C /* HemoPluginTests */,
				2B7EBB032D786EC5005747CD /* HemoPluginSampleApp */,
				2BD3D6952D5A1D9D0094DD4C /* Products */,
				19A62248E493C2FA42F607ED /* Pods */,
				CC992E503B2FA31C55F8CE9A /* Frameworks */,
			);
			sourceTree = "<group>";
		};
		2BD3D6952D5A1D9D0094DD4C /* Products */ = {
			isa = PBXGroup;
			children = (
				2BD3D6942D5A1D9D0094DD4C /* HemoPlugin.framework */,
				2BD3D69C2D5A1D9D0094DD4C /* HemoPluginTests.xctest */,
				2B7EBAF02D786EA1005747CD /* HemoPluginSampleApp.app */,
			);
			name = Products;
			sourceTree = "<group>";
		};
		2BD3D6B22D5A1DD50094DD4C /* HemoPlugin */ = {
			isa = PBXGroup;
			children = (
				2BCC77B92D940F260076F4BD /* Resources */,
				2BC626422D9BEE80000C70A1 /* Source */,
				2BE15B602D88412300E1618D /* TypeAliases.swift */,
				2BD3D6B12D5A1DD50094DD4C /* HemoPlugin.h */,
			);
			path = HemoPlugin;
			sourceTree = "<group>";
		};
		2BD3D6B52D5A1DD90094DD4C /* HemoPluginTests */ = {
			isa = PBXGroup;
			children = (
				2BD3D6B42D5A1DD90094DD4C /* HemoPluginTests.swift */,
			);
			path = HemoPluginTests;
			sourceTree = "<group>";
		};
		2BF084742DCB9566003A785A /* AddBleedQuestionnaire */ = {
			isa = PBXGroup;
			children = (
				2BF084752DCB9596003A785A /* AddBleedQuestionnaireSource.swift */,
			);
			path = AddBleedQuestionnaire;
			sourceTree = "<group>";
		};
		CC992E503B2FA31C55F8CE9A /* Frameworks */ = {
			isa = PBXGroup;
			children = (
				2B507FCD2D7899E7009A07FE /* HumaFoundation.framework */,
				2B507FCE2D7899E7009A07FE /* HumaLearnKit.framework */,
				2B507FCF2D7899E7009A07FE /* HumaModuleKit.framework */,
				2B507FD02D7899E7009A07FE /* HumaModules.framework */,
				2B507FD12D7899E7009A07FE /* HumaQuestionnaire.framework */,
				2B507FD22D7899E7009A07FE /* HumaWidgetKit.framework */,
				51C261135E79ED0D9D8D2928 /* Pods_HemoPlugin.framework */,
				5C703870FCC13A774B77776B /* Pods_HemoPluginTests.framework */,
				6A3ABD4E3558E8F18B75EECA /* Pods_HemoPluginSampleApp.framework */,
			);
			name = Frameworks;
			sourceTree = "<group>";
		};
/* End PBXGroup section */

/* Begin PBXHeadersBuildPhase section */
		2BD3D68F2D5A1D9D0094DD4C /* Headers */ = {
			isa = PBXHeadersBuildPhase;
			buildActionMask = 2147483647;
			files = (
				2BD3D6B32D5A1DD50094DD4C /* HemoPlugin.h in Headers */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXHeadersBuildPhase section */

/* Begin PBXNativeTarget section */
		2B7EBAEF2D786EA1005747CD /* HemoPluginSampleApp */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 2B7EBAFD2D786EA2005747CD /* Build configuration list for PBXNativeTarget "HemoPluginSampleApp" */;
			buildPhases = (
				272E7F65C88777B10F8EA3F4 /* [CP] Check Pods Manifest.lock */,
				2B7EBAEC2D786EA1005747CD /* Sources */,
				2B7EBAED2D786EA1005747CD /* Frameworks */,
				2B7EBAEE2D786EA1005747CD /* Resources */,
				15F410FF8AE018B49BC23D4A /* [CP] Embed Pods Frameworks */,
				2B507FCC2D7899C5009A07FE /* Embed Frameworks */,
			);
			buildRules = (
			);
			dependencies = (
				2B507FCB2D7899C5009A07FE /* PBXTargetDependency */,
			);
			name = HemoPluginSampleApp;
			productName = HemoPluginSampleApp;
			productReference = 2B7EBAF02D786EA1005747CD /* HemoPluginSampleApp.app */;
			productType = "com.apple.product-type.application";
		};
		2BD3D6932D5A1D9D0094DD4C /* HemoPlugin */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 2BD3D6A52D5A1D9D0094DD4C /* Build configuration list for PBXNativeTarget "HemoPlugin" */;
			buildPhases = (
				5EE7AD6DE804DE2CABA6EF44 /* [CP] Check Pods Manifest.lock */,
				2BD3D68F2D5A1D9D0094DD4C /* Headers */,
				2BD3D6902D5A1D9D0094DD4C /* Sources */,
				2BD3D6912D5A1D9D0094DD4C /* Frameworks */,
				2BD3D6922D5A1D9D0094DD4C /* Resources */,
			);
			buildRules = (
			);
			dependencies = (
			);
			name = HemoPlugin;
			productName = HemoPlugin;
			productReference = 2BD3D6942D5A1D9D0094DD4C /* HemoPlugin.framework */;
			productType = "com.apple.product-type.framework";
		};
		2BD3D69B2D5A1D9D0094DD4C /* HemoPluginTests */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 2BD3D6AA2D5A1D9D0094DD4C /* Build configuration list for PBXNativeTarget "HemoPluginTests" */;
			buildPhases = (
				E6D9E709AFD7BEBFE074C8A3 /* [CP] Check Pods Manifest.lock */,
				2BD3D6982D5A1D9D0094DD4C /* Sources */,
				2BD3D6992D5A1D9D0094DD4C /* Frameworks */,
				2BD3D69A2D5A1D9D0094DD4C /* Resources */,
				54823F4D939040342E0678C4 /* [CP] Embed Pods Frameworks */,
			);
			buildRules = (
			);
			dependencies = (
				2BD3D69F2D5A1D9D0094DD4C /* PBXTargetDependency */,
			);
			name = HemoPluginTests;
			productName = HemoPluginTests;
			productReference = 2BD3D69C2D5A1D9D0094DD4C /* HemoPluginTests.xctest */;
			productType = "com.apple.product-type.bundle.unit-test";
		};
/* End PBXNativeTarget section */

/* Begin PBXProject section */
		2BD3D68B2D5A1D9D0094DD4C /* Project object */ = {
			isa = PBXProject;
			attributes = {
				BuildIndependentTargetsInParallel = 1;
				LastSwiftUpdateCheck = 1600;
				LastUpgradeCheck = 1600;
				TargetAttributes = {
					2B7EBAEF2D786EA1005747CD = {
						CreatedOnToolsVersion = 16.0;
					};
					2BD3D6932D5A1D9D0094DD4C = {
						CreatedOnToolsVersion = 16.0;
						LastSwiftMigration = 1600;
					};
					2BD3D69B2D5A1D9D0094DD4C = {
						CreatedOnToolsVersion = 16.0;
					};
				};
			};
			buildConfigurationList = 2BD3D68E2D5A1D9D0094DD4C /* Build configuration list for PBXProject "HemoPlugin" */;
			compatibilityVersion = "Xcode 14.0";
			developmentRegion = en;
			hasScannedForEncodings = 0;
			knownRegions = (
				en,
				Base,
			);
			mainGroup = 2BD3D68A2D5A1D9D0094DD4C;
			productRefGroup = 2BD3D6952D5A1D9D0094DD4C /* Products */;
			projectDirPath = "";
			projectRoot = "";
			targets = (
				2BD3D6932D5A1D9D0094DD4C /* HemoPlugin */,
				2BD3D69B2D5A1D9D0094DD4C /* HemoPluginTests */,
				2B7EBAEF2D786EA1005747CD /* HemoPluginSampleApp */,
			);
		};
/* End PBXProject section */

/* Begin PBXResourcesBuildPhase section */
		2B7EBAEE2D786EA1005747CD /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				2B7EBB062D786EC5005747CD /* Preview Assets.xcassets in Resources */,
				2B1A32872DA81596008BA2AE /* Assets.xcassets in Resources */,
				2B7EBB072D786EC5005747CD /* Assets.xcassets in Resources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		2BD3D6922D5A1D9D0094DD4C /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				2B1A32852DA81596008BA2AE /* Assets.xcassets in Resources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		2BD3D69A2D5A1D9D0094DD4C /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				2B1A32862DA81596008BA2AE /* Assets.xcassets in Resources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXResourcesBuildPhase section */

/* Begin PBXShellScriptBuildPhase section */
		15F410FF8AE018B49BC23D4A /* [CP] Embed Pods Frameworks */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			inputFileListPaths = (
				"${PODS_ROOT}/Target Support Files/Pods-HemoPluginSampleApp/Pods-HemoPluginSampleApp-frameworks-${CONFIGURATION}-input-files.xcfilelist",
			);
			name = "[CP] Embed Pods Frameworks";
			outputFileListPaths = (
				"${PODS_ROOT}/Target Support Files/Pods-HemoPluginSampleApp/Pods-HemoPluginSampleApp-frameworks-${CONFIGURATION}-output-files.xcfilelist",
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "\"${PODS_ROOT}/Target Support Files/Pods-HemoPluginSampleApp/Pods-HemoPluginSampleApp-frameworks.sh\"\n";
			showEnvVarsInLog = 0;
		};
		272E7F65C88777B10F8EA3F4 /* [CP] Check Pods Manifest.lock */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			inputFileListPaths = (
			);
			inputPaths = (
				"${PODS_PODFILE_DIR_PATH}/Podfile.lock",
				"${PODS_ROOT}/Manifest.lock",
			);
			name = "[CP] Check Pods Manifest.lock";
			outputFileListPaths = (
			);
			outputPaths = (
				"$(DERIVED_FILE_DIR)/Pods-HemoPluginSampleApp-checkManifestLockResult.txt",
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "diff \"${PODS_PODFILE_DIR_PATH}/Podfile.lock\" \"${PODS_ROOT}/Manifest.lock\" > /dev/null\nif [ $? != 0 ] ; then\n    # print error to STDERR\n    echo \"error: The sandbox is not in sync with the Podfile.lock. Run 'pod install' or update your CocoaPods installation.\" >&2\n    exit 1\nfi\n# This output is used by Xcode 'outputs' to avoid re-running this script phase.\necho \"SUCCESS\" > \"${SCRIPT_OUTPUT_FILE_0}\"\n";
			showEnvVarsInLog = 0;
		};
		54823F4D939040342E0678C4 /* [CP] Embed Pods Frameworks */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			inputFileListPaths = (
				"${PODS_ROOT}/Target Support Files/Pods-HemoPluginTests/Pods-HemoPluginTests-frameworks-${CONFIGURATION}-input-files.xcfilelist",
			);
			name = "[CP] Embed Pods Frameworks";
			outputFileListPaths = (
				"${PODS_ROOT}/Target Support Files/Pods-HemoPluginTests/Pods-HemoPluginTests-frameworks-${CONFIGURATION}-output-files.xcfilelist",
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "\"${PODS_ROOT}/Target Support Files/Pods-HemoPluginTests/Pods-HemoPluginTests-frameworks.sh\"\n";
			showEnvVarsInLog = 0;
		};
		5EE7AD6DE804DE2CABA6EF44 /* [CP] Check Pods Manifest.lock */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			inputFileListPaths = (
			);
			inputPaths = (
				"${PODS_PODFILE_DIR_PATH}/Podfile.lock",
				"${PODS_ROOT}/Manifest.lock",
			);
			name = "[CP] Check Pods Manifest.lock";
			outputFileListPaths = (
			);
			outputPaths = (
				"$(DERIVED_FILE_DIR)/Pods-HemoPlugin-checkManifestLockResult.txt",
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "diff \"${PODS_PODFILE_DIR_PATH}/Podfile.lock\" \"${PODS_ROOT}/Manifest.lock\" > /dev/null\nif [ $? != 0 ] ; then\n    # print error to STDERR\n    echo \"error: The sandbox is not in sync with the Podfile.lock. Run 'pod install' or update your CocoaPods installation.\" >&2\n    exit 1\nfi\n# This output is used by Xcode 'outputs' to avoid re-running this script phase.\necho \"SUCCESS\" > \"${SCRIPT_OUTPUT_FILE_0}\"\n";
			showEnvVarsInLog = 0;
		};
		E6D9E709AFD7BEBFE074C8A3 /* [CP] Check Pods Manifest.lock */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			inputFileListPaths = (
			);
			inputPaths = (
				"${PODS_PODFILE_DIR_PATH}/Podfile.lock",
				"${PODS_ROOT}/Manifest.lock",
			);
			name = "[CP] Check Pods Manifest.lock";
			outputFileListPaths = (
			);
			outputPaths = (
				"$(DERIVED_FILE_DIR)/Pods-HemoPluginTests-checkManifestLockResult.txt",
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "diff \"${PODS_PODFILE_DIR_PATH}/Podfile.lock\" \"${PODS_ROOT}/Manifest.lock\" > /dev/null\nif [ $? != 0 ] ; then\n    # print error to STDERR\n    echo \"error: The sandbox is not in sync with the Podfile.lock. Run 'pod install' or update your CocoaPods installation.\" >&2\n    exit 1\nfi\n# This output is used by Xcode 'outputs' to avoid re-running this script phase.\necho \"SUCCESS\" > \"${SCRIPT_OUTPUT_FILE_0}\"\n";
			showEnvVarsInLog = 0;
		};
/* End PBXShellScriptBuildPhase section */

/* Begin PBXSourcesBuildPhase section */
		2B7EBAEC2D786EA1005747CD /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				2B1A324D2DA8123A008BA2AE /* AnyHemoJournalCardViewModel.swift in Sources */,
				2B1A324F2DA8123A008BA2AE /* HemoJournalCardViewModel.swift in Sources */,
				2B1A32502DA8123A008BA2AE /* HemoJournalCardView.swift in Sources */,
				2B7EBB042D786EC5005747CD /* ContentView.swift in Sources */,
				2B8489AA2DD5F37300CA53F5 /* PhotoLogView.swift in Sources */,
				2B8489AB2DD5F37300CA53F5 /* PhotoLogViewModel.swift in Sources */,
				2BB39F472DD23477003762D7 /* DetailsViewModel.swift in Sources */,
				2BB39F482DD23477003762D7 /* DetailsView.swift in Sources */,
				2B46C4782DD4D3F4006D0619 /* MockModuleResultRepository.swift in Sources */,
				2B7EBB052D786EC5005747CD /* HemoPluginSampleAppApp.swift in Sources */,
				2BF084782DCB9597003A785A /* AddBleedQuestionnaireSource.swift in Sources */,
				2BCC77C92D94127A0076F4BD /* TypeAliases.swift in Sources */,
				2BF084802DCBA119003A785A /* HumaSliderView.swift in Sources */,
				2B1A31C82DA80E57008BA2AE /* Dimensions.swift in Sources */,
				2B1A322B2DA811BB008BA2AE /* WidgetDataResponse.swift in Sources */,
				2B1A31C92DA80E57008BA2AE /* DatePickerView.swift in Sources */,
				2B332CCB2DC37F560056C4CD /* HTMLText.swift in Sources */,
				2B1A31CA2DA80E57008BA2AE /* HumaSUIImageView.swift in Sources */,
				2B1A326E2DA813BD008BA2AE /* LoadingView.swift in Sources */,
				2B1A320A2DA8111D008BA2AE /* BodyMapView.swift in Sources */,
				2B1A320B2DA8111D008BA2AE /* BodyMapViewModel.swift in Sources */,
				2B7547922DC36B0B00D10E65 /* CMSMedicationResponse.swift in Sources */,
				2B1A320C2DA8111D008BA2AE /* CircularInitialView.swift in Sources */,
				2B7547972DC36B4300D10E65 /* CMSMedicationRequest.swift in Sources */,
				2B332CC72DC371010056C4CD /* MockMedicationRepository.swift in Sources */,
				2B1A327D2DA813C7008BA2AE /* AddBleedView.swift in Sources */,
				2B2FED1E2DC127AC00A699CA /* HemoProfileQuestionnaireSource.swift in Sources */,
				2B2FED1F2DC127AC00A699CA /* TimeQuestionView.swift in Sources */,
				2B84899F2DD5F36800CA53F5 /* BleedHistoryViewModel.swift in Sources */,
				2B2FED202DC127AC00A699CA /* NumericQuestionView.swift in Sources */,
				2B2FED212DC127AC00A699CA /* QuestionnaireViewModel.swift in Sources */,
				2BF084882DCBEF0A003A785A /* PhotoPickerQuestionView.swift in Sources */,
				2B2FED222DC127AC00A699CA /* QuestionType.swift in Sources */,
				2BC7EC852DC26A22007752AB /* Binding+Extensions.swift in Sources */,
				2BFD316A2DCCDACE008E4367 /* MockModuleResultSubmitRepository.swift in Sources */,
				2B2FED232DC127AC00A699CA /* QuestionnaireView.swift in Sources */,
				2B2FED242DC127AC00A699CA /* TextQuestionView.swift in Sources */,
				2B2FED262DC127AC00A699CA /* SingleChoiceQuestionView.swift in Sources */,
				2B2FED272DC127AC00A699CA /* AutocompleteQuestionView.swift in Sources */,
				2BFD316E2DCCDB48008E4367 /* MockConfiguration.swift in Sources */,
				2B2FED282DC127AC00A699CA /* Question.swift in Sources */,
				2B2FED292DC127AC00A699CA /* QuestionView.swift in Sources */,
				2B2FED2A2DC127AC00A699CA /* ValidationRule.swift in Sources */,
				2B2FED2B2DC127AC00A699CA /* TagView.swift in Sources */,
				2B2FED2C2DC127AC00A699CA /* PickerQuestionView.swift in Sources */,
				2B2FED2D2DC127AC00A699CA /* Answer.swift in Sources */,
				2BB39F4F2DD24131003762D7 /* BleedHistoryListView.swift in Sources */,
				2B2FED2E2DC127AC00A699CA /* ValueUnitQuestionView.swift in Sources */,
				2BFBCE4F2DCDFFAB00AECDC6 /* MockUserObserveRepository.swift in Sources */,
				2BF0847C2DCB9FE4003A785A /* MultilineTextQuestionView.swift in Sources */,
				2B2FED2F2DC127AC00A699CA /* DateQuestionView.swift in Sources */,
				2B2FED302DC127AC00A699CA /* MultipleChoiceQuestionView.swift in Sources */,
				2B1A327E2DA813C7008BA2AE /* CustomBodyPartView.swift in Sources */,
				2B1A327F2DA813C7008BA2AE /* AddBleedViewModel.swift in Sources */,
				2B46C47C2DD4D631006D0619 /* MockFileRepository.swift in Sources */,
				2B84899B2DD5F35300CA53F5 /* BleedDetailsViewModel.swift in Sources */,
				2B1A31FD2DA81113008BA2AE /* PreviewModels.swift in Sources */,
				2B1A31CB2DA80E57008BA2AE /* SwiftUI+Fonts.swift in Sources */,
				2B1A32192DA8117D008BA2AE /* HemoJournalWidget.swift in Sources */,
				2B1A321A2DA8117D008BA2AE /* AnyHumaNetworking+Route.swift in Sources */,
				2B2FED002DC1240200A699CA /* HemoJournalCardViewModel+Preview.swift in Sources */,
				2B1A32672DA81289008BA2AE /* HeaderViewModel.swift in Sources */,
				2B1A32692DA81289008BA2AE /* AnyHeaderViewModel.swift in Sources */,
				2B1A326A2DA81289008BA2AE /* JournalHeaderView.swift in Sources */,
				2B1A326B2DA81289008BA2AE /* PreviewHeaderViewModel.swift in Sources */,
				2B1A321B2DA8117D008BA2AE /* HemoJournalWidgetConfig.swift in Sources */,
				2B1A321C2DA8117D008BA2AE /* HemoJournalRepository.swift in Sources */,
				2BD564DB2DC4F8460098CA65 /* BooleanChoiceQuestionView.swift in Sources */,
				2B1A321D2DA8117D008BA2AE /* HemoJournalWidgetCoordinator.swift in Sources */,
				2BC15BCB2DC526E800F07B80 /* OptionItem.swift in Sources */,
				2BF084842DCBEC19003A785A /* SliderQuestionView.swift in Sources */,
				2B1A31CC2DA80E57008BA2AE /* HumaSUITextField.swift in Sources */,
				2B46C4732DD4D3BF006D0619 /* BleedDetailsView.swift in Sources */,
				2B46C4742DD4D3BF006D0619 /* BodyMapLogView.swift in Sources */,
				2B1A31CD2DA80E57008BA2AE /* TempStyler.swift in Sources */,
				2B1A31CE2DA80E57008BA2AE /* CircleLoadingView.swift in Sources */,
				2B1A31CF2DA80E57008BA2AE /* HeaderView.swift in Sources */,
				2B1A31D02DA80E57008BA2AE /* PillButtonStyle.swift in Sources */,
				2B75478E2DC36AAB00D10E65 /* MedicationRepository.swift in Sources */,
				2B1A31D12DA80E57008BA2AE /* HalfSheetHelper.swift in Sources */,
				2B1A31D32DA80E57008BA2AE /* CardStyleModifier.swift in Sources */,
				2B2FECEC2DC123AA00A699CA /* ReusableListView.swift in Sources */,
				2B2FECED2DC123AA00A699CA /* SetupProfileCardView.swift in Sources */,
				2B2FECEE2DC123AA00A699CA /* SetupProfileCardViewModel.swift in Sources */,
				2B2FECEF2DC123AA00A699CA /* ActiveProfileCardViewModel.swift in Sources */,
				2B2FECF02DC123AA00A699CA /* ActiveProfileCardView.swift in Sources */,
				2B2FECF12DC123AA00A699CA /* ReusableListViewModel.swift in Sources */,
				2B1A31D62DA80E57008BA2AE /* ShimmerModifier.swift in Sources */,
				2B1A31D72DA80E57008BA2AE /* UnderlineButtonStyle.swift in Sources */,
				2B1A31D82DA80E57008BA2AE /* View+Extensions.swift in Sources */,
				2BD564912DC4E2310098CA65 /* HemoProfileQuestionnaireRequest.swift in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		2BD3D6902D5A1D9D0094DD4C /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				2B1A325B2DA81289008BA2AE /* HeaderViewModel.swift in Sources */,
				2B8489A12DD5F36800CA53F5 /* BleedHistoryViewModel.swift in Sources */,
				2B1A325D2DA81289008BA2AE /* AnyHeaderViewModel.swift in Sources */,
				2B1A32792DA813C7008BA2AE /* AddBleedView.swift in Sources */,
				2B1A327A2DA813C7008BA2AE /* CustomBodyPartView.swift in Sources */,
				2B75478C2DC36AAB00D10E65 /* MedicationRepository.swift in Sources */,
				2BD564D92DC4F8460098CA65 /* BooleanChoiceQuestionView.swift in Sources */,
				2B1A327B2DA813C7008BA2AE /* AddBleedViewModel.swift in Sources */,
				2B1A325E2DA81289008BA2AE /* JournalHeaderView.swift in Sources */,
				2B1A325F2DA81289008BA2AE /* PreviewHeaderViewModel.swift in Sources */,
				2B1A326F2DA813BD008BA2AE /* LoadingView.swift in Sources */,
				2BC7EC842DC26A22007752AB /* Binding+Extensions.swift in Sources */,
				2BE15B612D88412300E1618D /* TypeAliases.swift in Sources */,
				2B46C4792DD4D3F4006D0619 /* MockModuleResultRepository.swift in Sources */,
				2B1A32232DA8117D008BA2AE /* HemoJournalWidget.swift in Sources */,
				2B1A322A2DA811BB008BA2AE /* WidgetDataResponse.swift in Sources */,
				2B1A32242DA8117D008BA2AE /* AnyHumaNetworking+Route.swift in Sources */,
				2B1A32252DA8117D008BA2AE /* HemoJournalWidgetConfig.swift in Sources */,
				2B1A32262DA8117D008BA2AE /* HemoJournalRepository.swift in Sources */,
				2B1A32272DA8117D008BA2AE /* HemoJournalWidgetCoordinator.swift in Sources */,
				2B46C4752DD4D3BF006D0619 /* BleedDetailsView.swift in Sources */,
				2B46C4762DD4D3BF006D0619 /* BodyMapLogView.swift in Sources */,
				2B1A31D92DA80E57008BA2AE /* Dimensions.swift in Sources */,
				2B1A31DA2DA80E57008BA2AE /* DatePickerView.swift in Sources */,
				2B1A31DB2DA80E57008BA2AE /* HumaSUIImageView.swift in Sources */,
				2B1A31DC2DA80E57008BA2AE /* SwiftUI+Fonts.swift in Sources */,
				2B1A31DD2DA80E57008BA2AE /* HumaSUITextField.swift in Sources */,
				2B1A31DE2DA80E57008BA2AE /* TempStyler.swift in Sources */,
				2B2FECF82DC123AA00A699CA /* ReusableListView.swift in Sources */,
				2B2FECF92DC123AA00A699CA /* SetupProfileCardView.swift in Sources */,
				2BC15BCD2DC526E800F07B80 /* OptionItem.swift in Sources */,
				2B2FECFA2DC123AA00A699CA /* SetupProfileCardViewModel.swift in Sources */,
				2B7547902DC36B0B00D10E65 /* CMSMedicationResponse.swift in Sources */,
				2B332CC92DC37F560056C4CD /* HTMLText.swift in Sources */,
				2B2FECFF2DC1240200A699CA /* HemoJournalCardViewModel+Preview.swift in Sources */,
				2B2FED322DC127AC00A699CA /* HemoProfileQuestionnaireSource.swift in Sources */,
				2B2FED332DC127AC00A699CA /* TimeQuestionView.swift in Sources */,
				2B2FED342DC127AC00A699CA /* NumericQuestionView.swift in Sources */,
				2B2FED352DC127AC00A699CA /* QuestionnaireViewModel.swift in Sources */,
				2B2FED362DC127AC00A699CA /* QuestionType.swift in Sources */,
				2B2FED372DC127AC00A699CA /* QuestionnaireView.swift in Sources */,
				2BB39F492DD23477003762D7 /* DetailsViewModel.swift in Sources */,
				2BB39F4A2DD23477003762D7 /* DetailsView.swift in Sources */,
				2B84899A2DD5F35300CA53F5 /* BleedDetailsViewModel.swift in Sources */,
				2BF0847A2DCB9FE4003A785A /* MultilineTextQuestionView.swift in Sources */,
				2B2FED382DC127AC00A699CA /* TextQuestionView.swift in Sources */,
				2B2FED3A2DC127AC00A699CA /* SingleChoiceQuestionView.swift in Sources */,
				2B2FED3B2DC127AC00A699CA /* AutocompleteQuestionView.swift in Sources */,
				2BF084862DCBEF0A003A785A /* PhotoPickerQuestionView.swift in Sources */,
				2BF084762DCB9597003A785A /* AddBleedQuestionnaireSource.swift in Sources */,
				2B2FED3C2DC127AC00A699CA /* Question.swift in Sources */,
				2B2FED3D2DC127AC00A699CA /* QuestionView.swift in Sources */,
				2B2FED3E2DC127AC00A699CA /* ValidationRule.swift in Sources */,
				2B2FED3F2DC127AC00A699CA /* TagView.swift in Sources */,
				2B2FED402DC127AC00A699CA /* PickerQuestionView.swift in Sources */,
				2B2FED412DC127AC00A699CA /* Answer.swift in Sources */,
				2B2FED422DC127AC00A699CA /* ValueUnitQuestionView.swift in Sources */,
				2B2FED432DC127AC00A699CA /* DateQuestionView.swift in Sources */,
				2B2FED442DC127AC00A699CA /* MultipleChoiceQuestionView.swift in Sources */,
				2BD564902DC4E2310098CA65 /* HemoProfileQuestionnaireRequest.swift in Sources */,
				2BFBCE502DCDFFAB00AECDC6 /* MockUserObserveRepository.swift in Sources */,
				2B2FECFB2DC123AA00A699CA /* ActiveProfileCardViewModel.swift in Sources */,
				2B2FECFC2DC123AA00A699CA /* ActiveProfileCardView.swift in Sources */,
				2B2FECFD2DC123AA00A699CA /* ReusableListViewModel.swift in Sources */,
				2B1A31DF2DA80E57008BA2AE /* CircleLoadingView.swift in Sources */,
				2B46C47D2DD4D631006D0619 /* MockFileRepository.swift in Sources */,
				2B1A32102DA8111D008BA2AE /* BodyMapView.swift in Sources */,
				2BFD316C2DCCDACE008E4367 /* MockModuleResultSubmitRepository.swift in Sources */,
				2B1A32112DA8111D008BA2AE /* BodyMapViewModel.swift in Sources */,
				2B1A32122DA8111D008BA2AE /* CircularInitialView.swift in Sources */,
				2B1A31FF2DA81113008BA2AE /* PreviewModels.swift in Sources */,
				2B1A31E02DA80E57008BA2AE /* HeaderView.swift in Sources */,
				2BF084822DCBEC19003A785A /* SliderQuestionView.swift in Sources */,
				2B1A31E12DA80E57008BA2AE /* PillButtonStyle.swift in Sources */,
				2BFD316F2DCCDB48008E4367 /* MockConfiguration.swift in Sources */,
				2B1A31E22DA80E57008BA2AE /* HalfSheetHelper.swift in Sources */,
				2B7547962DC36B4300D10E65 /* CMSMedicationRequest.swift in Sources */,
				2B1A31E42DA80E57008BA2AE /* CardStyleModifier.swift in Sources */,
				2B8489A82DD5F37300CA53F5 /* PhotoLogView.swift in Sources */,
				2B8489A92DD5F37300CA53F5 /* PhotoLogViewModel.swift in Sources */,
				2B1A31E72DA80E57008BA2AE /* ShimmerModifier.swift in Sources */,
				2B1A32452DA8123A008BA2AE /* AnyHemoJournalCardViewModel.swift in Sources */,
				2BB39F512DD24131003762D7 /* BleedHistoryListView.swift in Sources */,
				2B1A32472DA8123A008BA2AE /* HemoJournalCardViewModel.swift in Sources */,
				2B332CC52DC371010056C4CD /* MockMedicationRepository.swift in Sources */,
				2B1A32482DA8123A008BA2AE /* HemoJournalCardView.swift in Sources */,
				2B1A31E82DA80E57008BA2AE /* UnderlineButtonStyle.swift in Sources */,
				2BF0847E2DCBA119003A785A /* HumaSliderView.swift in Sources */,
				2B1A31E92DA80E57008BA2AE /* View+Extensions.swift in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		2BD3D6982D5A1D9D0094DD4C /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				2B1A32612DA81289008BA2AE /* HeaderViewModel.swift in Sources */,
				2B8489A02DD5F36800CA53F5 /* BleedHistoryViewModel.swift in Sources */,
				2B1A32632DA81289008BA2AE /* AnyHeaderViewModel.swift in Sources */,
				2B1A32812DA813C7008BA2AE /* AddBleedView.swift in Sources */,
				2B1A32822DA813C7008BA2AE /* CustomBodyPartView.swift in Sources */,
				2B75478D2DC36AAB00D10E65 /* MedicationRepository.swift in Sources */,
				2BD564DA2DC4F8460098CA65 /* BooleanChoiceQuestionView.swift in Sources */,
				2B1A32832DA813C7008BA2AE /* AddBleedViewModel.swift in Sources */,
				2B1A32642DA81289008BA2AE /* JournalHeaderView.swift in Sources */,
				2B1A32652DA81289008BA2AE /* PreviewHeaderViewModel.swift in Sources */,
				2B1A32702DA813BD008BA2AE /* LoadingView.swift in Sources */,
				2BC7EC832DC26A22007752AB /* Binding+Extensions.swift in Sources */,
				2BD3D6B62D5A1DD90094DD4C /* HemoPluginTests.swift in Sources */,
				2B46C47A2DD4D3F4006D0619 /* MockModuleResultRepository.swift in Sources */,
				2B1A321E2DA8117D008BA2AE /* HemoJournalWidget.swift in Sources */,
				2B1A322C2DA811BB008BA2AE /* WidgetDataResponse.swift in Sources */,
				2B1A321F2DA8117D008BA2AE /* AnyHumaNetworking+Route.swift in Sources */,
				2B1A32202DA8117D008BA2AE /* HemoJournalWidgetConfig.swift in Sources */,
				2B1A32212DA8117D008BA2AE /* HemoJournalRepository.swift in Sources */,
				2B1A32222DA8117D008BA2AE /* HemoJournalWidgetCoordinator.swift in Sources */,
				2B46C4712DD4D3BF006D0619 /* BleedDetailsView.swift in Sources */,
				2B46C4722DD4D3BF006D0619 /* BodyMapLogView.swift in Sources */,
				2B1A31EA2DA80E57008BA2AE /* Dimensions.swift in Sources */,
				2B1A31EB2DA80E57008BA2AE /* DatePickerView.swift in Sources */,
				2B1A31EC2DA80E57008BA2AE /* HumaSUIImageView.swift in Sources */,
				2B1A31ED2DA80E57008BA2AE /* SwiftUI+Fonts.swift in Sources */,
				2B1A31EE2DA80E57008BA2AE /* HumaSUITextField.swift in Sources */,
				2B1A31EF2DA80E57008BA2AE /* TempStyler.swift in Sources */,
				2B2FECF22DC123AA00A699CA /* ReusableListView.swift in Sources */,
				2B2FECF32DC123AA00A699CA /* SetupProfileCardView.swift in Sources */,
				2BC15BCC2DC526E800F07B80 /* OptionItem.swift in Sources */,
				2B2FECF42DC123AA00A699CA /* SetupProfileCardViewModel.swift in Sources */,
				2B7547912DC36B0B00D10E65 /* CMSMedicationResponse.swift in Sources */,
				2B332CCA2DC37F560056C4CD /* HTMLText.swift in Sources */,
				2B2FED012DC1240200A699CA /* HemoJournalCardViewModel+Preview.swift in Sources */,
				2B2FED462DC127AC00A699CA /* HemoProfileQuestionnaireSource.swift in Sources */,
				2B2FED472DC127AC00A699CA /* TimeQuestionView.swift in Sources */,
				2B2FED482DC127AC00A699CA /* NumericQuestionView.swift in Sources */,
				2B2FED492DC127AC00A699CA /* QuestionnaireViewModel.swift in Sources */,
				2B2FED4A2DC127AC00A699CA /* QuestionType.swift in Sources */,
				2B2FED4B2DC127AC00A699CA /* QuestionnaireView.swift in Sources */,
				2BB39F4B2DD23477003762D7 /* DetailsViewModel.swift in Sources */,
				2BB39F4C2DD23477003762D7 /* DetailsView.swift in Sources */,
				2B84899C2DD5F35300CA53F5 /* BleedDetailsViewModel.swift in Sources */,
				2BF0847B2DCB9FE4003A785A /* MultilineTextQuestionView.swift in Sources */,
				2B2FED4C2DC127AC00A699CA /* TextQuestionView.swift in Sources */,
				2B2FED4E2DC127AC00A699CA /* SingleChoiceQuestionView.swift in Sources */,
				2B2FED4F2DC127AC00A699CA /* AutocompleteQuestionView.swift in Sources */,
				2BF084872DCBEF0A003A785A /* PhotoPickerQuestionView.swift in Sources */,
				2BF084772DCB9597003A785A /* AddBleedQuestionnaireSource.swift in Sources */,
				2B2FED502DC127AC00A699CA /* Question.swift in Sources */,
				2B2FED512DC127AC00A699CA /* QuestionView.swift in Sources */,
				2B2FED522DC127AC00A699CA /* ValidationRule.swift in Sources */,
				2B2FED532DC127AC00A699CA /* TagView.swift in Sources */,
				2B2FED542DC127AC00A699CA /* PickerQuestionView.swift in Sources */,
				2B2FED552DC127AC00A699CA /* Answer.swift in Sources */,
				2B2FED562DC127AC00A699CA /* ValueUnitQuestionView.swift in Sources */,
				2B2FED572DC127AC00A699CA /* DateQuestionView.swift in Sources */,
				2B2FED582DC127AC00A699CA /* MultipleChoiceQuestionView.swift in Sources */,
				2BD564922DC4E2310098CA65 /* HemoProfileQuestionnaireRequest.swift in Sources */,
				2BFBCE512DCDFFAB00AECDC6 /* MockUserObserveRepository.swift in Sources */,
				2B2FECF52DC123AA00A699CA /* ActiveProfileCardViewModel.swift in Sources */,
				2B2FECF62DC123AA00A699CA /* ActiveProfileCardView.swift in Sources */,
				2B2FECF72DC123AA00A699CA /* ReusableListViewModel.swift in Sources */,
				2B1A31F02DA80E57008BA2AE /* CircleLoadingView.swift in Sources */,
				2B46C47E2DD4D631006D0619 /* MockFileRepository.swift in Sources */,
				2B1A320D2DA8111D008BA2AE /* BodyMapView.swift in Sources */,
				2BFD316B2DCCDACE008E4367 /* MockModuleResultSubmitRepository.swift in Sources */,
				2B1A320E2DA8111D008BA2AE /* BodyMapViewModel.swift in Sources */,
				2B1A320F2DA8111D008BA2AE /* CircularInitialView.swift in Sources */,
				2B1A31FE2DA81113008BA2AE /* PreviewModels.swift in Sources */,
				2B1A31F12DA80E57008BA2AE /* HeaderView.swift in Sources */,
				2BF084832DCBEC19003A785A /* SliderQuestionView.swift in Sources */,
				2B1A31F22DA80E57008BA2AE /* PillButtonStyle.swift in Sources */,
				2BFD31702DCCDB48008E4367 /* MockConfiguration.swift in Sources */,
				2B1A31F32DA80E57008BA2AE /* HalfSheetHelper.swift in Sources */,
				2B7547952DC36B4300D10E65 /* CMSMedicationRequest.swift in Sources */,
				2B1A31F52DA80E57008BA2AE /* CardStyleModifier.swift in Sources */,
				2B8489A62DD5F37300CA53F5 /* PhotoLogView.swift in Sources */,
				2B8489A72DD5F37300CA53F5 /* PhotoLogViewModel.swift in Sources */,
				2B1A31F82DA80E57008BA2AE /* ShimmerModifier.swift in Sources */,
				2B1A32492DA8123A008BA2AE /* AnyHemoJournalCardViewModel.swift in Sources */,
				2BB39F502DD24131003762D7 /* BleedHistoryListView.swift in Sources */,
				2B1A324B2DA8123A008BA2AE /* HemoJournalCardViewModel.swift in Sources */,
				2B332CC62DC371010056C4CD /* MockMedicationRepository.swift in Sources */,
				2B1A324C2DA8123A008BA2AE /* HemoJournalCardView.swift in Sources */,
				2B1A31F92DA80E57008BA2AE /* UnderlineButtonStyle.swift in Sources */,
				2BF0847F2DCBA119003A785A /* HumaSliderView.swift in Sources */,
				2B1A31FA2DA80E57008BA2AE /* View+Extensions.swift in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXSourcesBuildPhase section */

/* Begin PBXTargetDependency section */
		2B507FCB2D7899C5009A07FE /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			target = 2BD3D6932D5A1D9D0094DD4C /* HemoPlugin */;
			targetProxy = 2B507FCA2D7899C5009A07FE /* PBXContainerItemProxy */;
		};
		2BD3D69F2D5A1D9D0094DD4C /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			target = 2BD3D6932D5A1D9D0094DD4C /* HemoPlugin */;
			targetProxy = 2BD3D69E2D5A1D9D0094DD4C /* PBXContainerItemProxy */;
		};
/* End PBXTargetDependency section */

/* Begin XCBuildConfiguration section */
		2B7EBAFB2D786EA2005747CD /* Debug */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 4A91E0470E10180B3078EF9E /* Pods-HemoPluginSampleApp.debug.xcconfig */;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				ASSETCATALOG_COMPILER_GLOBAL_ACCENT_COLOR_NAME = AccentColor;
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_ASSET_PATHS = "\"HemoPluginSampleApp/Preview Content\"";
				DEVELOPMENT_TEAM = D67VZ62WN4;
				ENABLE_PREVIEWS = YES;
				GENERATE_INFOPLIST_FILE = YES;
				INFOPLIST_KEY_UIApplicationSceneManifest_Generation = YES;
				INFOPLIST_KEY_UIApplicationSupportsIndirectInputEvents = YES;
				INFOPLIST_KEY_UILaunchScreen_Generation = YES;
				INFOPLIST_KEY_UISupportedInterfaceOrientations_iPad = "UIInterfaceOrientationPortrait UIInterfaceOrientationPortraitUpsideDown UIInterfaceOrientationLandscapeLeft UIInterfaceOrientationLandscapeRight";
				INFOPLIST_KEY_UISupportedInterfaceOrientations_iPhone = "UIInterfaceOrientationPortrait UIInterfaceOrientationLandscapeLeft UIInterfaceOrientationLandscapeRight";
				IPHONEOS_DEPLOYMENT_TARGET = 15.0;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
				);
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = com.HemoPluginSampleApp;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SWIFT_EMIT_LOC_STRINGS = YES;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
			};
			name = Debug;
		};
		2B7EBAFC2D786EA2005747CD /* Release */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = C91EE309E5D6C1103676E7D4 /* Pods-HemoPluginSampleApp.release.xcconfig */;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				ASSETCATALOG_COMPILER_GLOBAL_ACCENT_COLOR_NAME = AccentColor;
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_ASSET_PATHS = "\"HemoPluginSampleApp/Preview Content\"";
				DEVELOPMENT_TEAM = D67VZ62WN4;
				ENABLE_PREVIEWS = YES;
				GENERATE_INFOPLIST_FILE = YES;
				INFOPLIST_KEY_UIApplicationSceneManifest_Generation = YES;
				INFOPLIST_KEY_UIApplicationSupportsIndirectInputEvents = YES;
				INFOPLIST_KEY_UILaunchScreen_Generation = YES;
				INFOPLIST_KEY_UISupportedInterfaceOrientations_iPad = "UIInterfaceOrientationPortrait UIInterfaceOrientationPortraitUpsideDown UIInterfaceOrientationLandscapeLeft UIInterfaceOrientationLandscapeRight";
				INFOPLIST_KEY_UISupportedInterfaceOrientations_iPhone = "UIInterfaceOrientationPortrait UIInterfaceOrientationLandscapeLeft UIInterfaceOrientationLandscapeRight";
				IPHONEOS_DEPLOYMENT_TARGET = 15.0;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
				);
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = com.HemoPluginSampleApp;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SWIFT_EMIT_LOC_STRINGS = YES;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
			};
			name = Release;
		};
		2BD3D6A62D5A1D9D0094DD4C /* Debug */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 5EE48250D6BA96A3C7604AEA /* Pods-HemoPlugin.debug.xcconfig */;
			buildSettings = {
				BUILD_LIBRARY_FOR_DISTRIBUTION = YES;
				CLANG_ENABLE_MODULES = YES;
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEFINES_MODULE = YES;
				DEVELOPMENT_TEAM = MMNBVS2YRP;
				DYLIB_COMPATIBILITY_VERSION = 1;
				DYLIB_CURRENT_VERSION = 1;
				DYLIB_INSTALL_NAME_BASE = "@rpath";
				GENERATE_INFOPLIST_FILE = YES;
				INFOPLIST_KEY_NSHumanReadableCopyright = "";
				INSTALL_PATH = "$(LOCAL_LIBRARY_DIR)/Frameworks";
				IPHONEOS_DEPLOYMENT_TARGET = 15.0;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
					"@loader_path/Frameworks",
				);
				MARKETING_VERSION = 1.0;
				MODULE_VERIFIER_SUPPORTED_LANGUAGES = "objective-c objective-c++";
				MODULE_VERIFIER_SUPPORTED_LANGUAGE_STANDARDS = "gnu17 gnu++20";
				PRODUCT_BUNDLE_IDENTIFIER = com.HemoPlugin;
				PRODUCT_NAME = "$(TARGET_NAME:c99extidentifier)";
				SKIP_INSTALL = YES;
				SWIFT_EMIT_LOC_STRINGS = YES;
				SWIFT_INSTALL_OBJC_HEADER = NO;
				SWIFT_OPTIMIZATION_LEVEL = "-Onone";
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
			};
			name = Debug;
		};
		2BD3D6A72D5A1D9D0094DD4C /* Release */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = ECFC25D94790DE267C89D9AD /* Pods-HemoPlugin.release.xcconfig */;
			buildSettings = {
				BUILD_LIBRARY_FOR_DISTRIBUTION = YES;
				CLANG_ENABLE_MODULES = YES;
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEFINES_MODULE = YES;
				DEVELOPMENT_TEAM = MMNBVS2YRP;
				DYLIB_COMPATIBILITY_VERSION = 1;
				DYLIB_CURRENT_VERSION = 1;
				DYLIB_INSTALL_NAME_BASE = "@rpath";
				GENERATE_INFOPLIST_FILE = YES;
				INFOPLIST_KEY_NSHumanReadableCopyright = "";
				INSTALL_PATH = "$(LOCAL_LIBRARY_DIR)/Frameworks";
				IPHONEOS_DEPLOYMENT_TARGET = 15.0;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
					"@loader_path/Frameworks",
				);
				MARKETING_VERSION = 1.0;
				MODULE_VERIFIER_SUPPORTED_LANGUAGES = "objective-c objective-c++";
				MODULE_VERIFIER_SUPPORTED_LANGUAGE_STANDARDS = "gnu17 gnu++20";
				PRODUCT_BUNDLE_IDENTIFIER = com.HemoPlugin;
				PRODUCT_NAME = "$(TARGET_NAME:c99extidentifier)";
				SKIP_INSTALL = YES;
				SWIFT_EMIT_LOC_STRINGS = YES;
				SWIFT_INSTALL_OBJC_HEADER = NO;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
			};
			name = Release;
		};
		2BD3D6A82D5A1D9D0094DD4C /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				ASSETCATALOG_COMPILER_GENERATE_SWIFT_ASSET_SYMBOL_EXTENSIONS = YES;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++20";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				COPY_PHASE_STRIP = NO;
				CURRENT_PROJECT_VERSION = 1;
				DEBUG_INFORMATION_FORMAT = dwarf;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_TESTABILITY = YES;
				GCC_C_LANGUAGE_STANDARD = gnu17;
				GCC_DYNAMIC_NO_PIC = NO;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_OPTIMIZATION_LEVEL = 0;
				GCC_PREPROCESSOR_DEFINITIONS = (
					"DEBUG=1",
					"$(inherited)",
				);
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 18.0;
				LOCALIZATION_PREFERS_STRING_CATALOGS = YES;
				MTL_ENABLE_DEBUG_INFO = INCLUDE_SOURCE;
				MTL_FAST_MATH = YES;
				ONLY_ACTIVE_ARCH = YES;
				SDKROOT = iphoneos;
				SWIFT_ACTIVE_COMPILATION_CONDITIONS = "DEBUG $(inherited)";
				SWIFT_OPTIMIZATION_LEVEL = "-Onone";
				VERSIONING_SYSTEM = "apple-generic";
				VERSION_INFO_PREFIX = "";
			};
			name = Debug;
		};
		2BD3D6A92D5A1D9D0094DD4C /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				ASSETCATALOG_COMPILER_GENERATE_SWIFT_ASSET_SYMBOL_EXTENSIONS = YES;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++20";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				COPY_PHASE_STRIP = NO;
				CURRENT_PROJECT_VERSION = 1;
				DEBUG_INFORMATION_FORMAT = "dwarf-with-dsym";
				ENABLE_NS_ASSERTIONS = NO;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				GCC_C_LANGUAGE_STANDARD = gnu17;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 18.0;
				LOCALIZATION_PREFERS_STRING_CATALOGS = YES;
				MTL_ENABLE_DEBUG_INFO = NO;
				MTL_FAST_MATH = YES;
				SDKROOT = iphoneos;
				SWIFT_COMPILATION_MODE = wholemodule;
				VALIDATE_PRODUCT = YES;
				VERSIONING_SYSTEM = "apple-generic";
				VERSION_INFO_PREFIX = "";
			};
			name = Release;
		};
		2BD3D6AB2D5A1D9D0094DD4C /* Debug */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = F3E11B1CF0ECF8F674D34326 /* Pods-HemoPluginTests.debug.xcconfig */;
			buildSettings = {
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_TEAM = D67VZ62WN4;
				GENERATE_INFOPLIST_FILE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 15.0;
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = com.HemoPluginTests;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SWIFT_EMIT_LOC_STRINGS = NO;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
			};
			name = Debug;
		};
		2BD3D6AC2D5A1D9D0094DD4C /* Release */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 71B8D94A2A81421355C5A6D5 /* Pods-HemoPluginTests.release.xcconfig */;
			buildSettings = {
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_TEAM = D67VZ62WN4;
				GENERATE_INFOPLIST_FILE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 15.0;
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = com.HemoPluginTests;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SWIFT_EMIT_LOC_STRINGS = NO;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
			};
			name = Release;
		};
/* End XCBuildConfiguration section */

/* Begin XCConfigurationList section */
		2B7EBAFD2D786EA2005747CD /* Build configuration list for PBXNativeTarget "HemoPluginSampleApp" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				2B7EBAFB2D786EA2005747CD /* Debug */,
				2B7EBAFC2D786EA2005747CD /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		2BD3D68E2D5A1D9D0094DD4C /* Build configuration list for PBXProject "HemoPlugin" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				2BD3D6A82D5A1D9D0094DD4C /* Debug */,
				2BD3D6A92D5A1D9D0094DD4C /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		2BD3D6A52D5A1D9D0094DD4C /* Build configuration list for PBXNativeTarget "HemoPlugin" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				2BD3D6A62D5A1D9D0094DD4C /* Debug */,
				2BD3D6A72D5A1D9D0094DD4C /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		2BD3D6AA2D5A1D9D0094DD4C /* Build configuration list for PBXNativeTarget "HemoPluginTests" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				2BD3D6AB2D5A1D9D0094DD4C /* Debug */,
				2BD3D6AC2D5A1D9D0094DD4C /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
/* End XCConfigurationList section */
	};
	rootObject = 2BD3D68B2D5A1D9D0094DD4C /* Project object */;
}
