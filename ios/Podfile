# Uncomment the next line to define a global platform for your project
platform :ios, '15.0'
use_frameworks!

source 'https://github.com/CocoaPods/Specs.git'
source '**************:huma-engineering/huma-ios-specs.git'

workspace 'HumaServierPlugin'

def use_local_pods
  # change to true for SDK development
  true
end

def sdk_version
  '2.0.2'
end

def sdk_path
  '../../huma-ios-sdk'
end

def pod_spec (pod_name)
  if use_local_pods
    pod pod_name, :path => sdk_path
  else
    pod pod_name, sdk_version
  end
end

def swiftLint
  pod 'SwiftLint', '0.47.0'
end

def plugin_dependencies
  pod_spec 'HumaFoundation'
  pod_spec 'HumaQuestionnaire'
  pod_spec 'HumaModuleKit'
  pod_spec 'HumaModules'
  pod_spec 'HumaWidgetKit'
  pod_spec 'HumaLearnKit'
  pod_spec 'HumaHealthKit'
  pod_spec 'HumaActionsKit'
  pod_spec 'HumaAppKit'
  pod_spec 'HumaAuth'
  pod_spec 'HumaMessaging'
  pod_spec 'HumaObjectStorage'
  pod_spec 'HumaOnboarding'
  pod_spec 'HumaProfileKit'
end

target 'HumaServierPlugin' do
  project 'HumaServierPlugin/HumaServierPlugin.xcodeproj'
  plugin_dependencies
  swiftLint
end

 post_install do |installer|
   installer.pods_project.build_configurations.each do |config|

     installer.pods_project.targets.each do |target|
         target.build_configurations.each do |config|
             config.build_settings['IPHONEOS_DEPLOYMENT_TARGET'] = '15.0'
         end
     end
   end
 end
