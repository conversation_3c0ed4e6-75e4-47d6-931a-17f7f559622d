//
//  AnyHumaNetworking+Route.swift
//  HumaRochePlugin
//
//  Created by <PERSON> on 21/02/2025.
//  Copyright © 2025 Huma Therapeutics Ltd. All rights reserved.
//

import HumaFoundation

extension AnyNetworking {
    func getWidgetData(widgetType: String, widgetID: String, completion: @escaping RequestCompletion<GetWidgetDataResponse>) {
        request(
            Endpoint.getWidgetData(widgetType: widgetType, widgetID: widgetID),
            completion: completion
        )
    }

    func setPregnancyDate(date: Date, completion: @escaping RequestCompletion<SetPregnancyDateResponse>) {
        request(
            Endpoint.setPregnancyDate(payload: .init(pregnancyDate: date.iso8601ShortDateUTC)),
            completion: completion
        )
    }
}

extension Endpoint {
    static func getWidgetData(widgetType: String, widgetID: String) -> Endpoint {
        .init(
            path: "/api/sdk/v1/user/\(Endpoint.variableUserIDMacro)/widget/\(widgetType)/\(widgetID)",
            method: .get
        )
    }

    static func setPregnancyDate(payload: SetPregnancyDateRequest) -> Endpoint {
        .init(
            path: "/api/cds/v1/user/\(Endpoint.variableUserIDMacro)/pregnancy",
            method: .post,
            body: .json(payload, encoder: .huma)
        )
    }
}
