// !$*UTF8*$!
{
	archiveVersion = 1;
	classes = {
	};
	objectVersion = 56;
	objects = {

/* Begin PBXBuildFile section */
		2BD5646E2DC4DA040098CA65 /* SurgeryChecklistConfig.swift in Sources */ = {isa = PBXBuildFile; fileRef = 2BD564492DC4DA040098CA65 /* SurgeryChecklistConfig.swift */; };
		2BD5646F2DC4DA040098CA65 /* AnySurgeryChecklistViewModel.swift in Sources */ = {isa = PBXBuildFile; fileRef = 2BD564682DC4DA040098CA65 /* AnySurgeryChecklistViewModel.swift */; };
		2BD564702DC4DA040098CA65 /* View+Extensions.swift in Sources */ = {isa = PBXBuildFile; fileRef = 2BD564542DC4DA040098CA65 /* View+Extensions.swift */; };
		2BD564712DC4DA040098CA65 /* SeparatedVStack.swift in Sources */ = {isa = PBXBuildFile; fileRef = 2BD564582DC4DA040098CA65 /* SeparatedVStack.swift */; };
		2BD564722DC4DA040098CA65 /* Array+Extension.swift in Sources */ = {isa = PBXBuildFile; fileRef = 2BD564512DC4DA040098CA65 /* Array+Extension.swift */; };
		2BD564732DC4DA040098CA65 /* SurgeryChecklistPhaseView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 2BD564662DC4DA040098CA65 /* SurgeryChecklistPhaseView.swift */; };
		2BD564742DC4DA040098CA65 /* SurgeryChecklistDateView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 2BD564622DC4DA040098CA65 /* SurgeryChecklistDateView.swift */; };
		2BD564752DC4DA040098CA65 /* SurgeryChecklistViewModel.swift in Sources */ = {isa = PBXBuildFile; fileRef = 2BD5646B2DC4DA040098CA65 /* SurgeryChecklistViewModel.swift */; };
		2BD564762DC4DA040098CA65 /* TempStyler.swift in Sources */ = {isa = PBXBuildFile; fileRef = 2BD564602DC4DA040098CA65 /* TempStyler.swift */; };
		2BD564772DC4DA040098CA65 /* CircleLoadingView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 2BD564572DC4DA040098CA65 /* CircleLoadingView.swift */; };
		2BD564782DC4DA040098CA65 /* ShimmerModifier.swift in Sources */ = {isa = PBXBuildFile; fileRef = 2BD5645E2DC4DA040098CA65 /* ShimmerModifier.swift */; };
		2BD564792DC4DA040098CA65 /* SurgeryChecklistModel+Preview.swift in Sources */ = {isa = PBXBuildFile; fileRef = 2BD5644B2DC4DA040098CA65 /* SurgeryChecklistModel+Preview.swift */; };
		2BD5647A2DC4DA040098CA65 /* SurgeryChecklistModel.swift in Sources */ = {isa = PBXBuildFile; fileRef = 2BD5644A2DC4DA040098CA65 /* SurgeryChecklistModel.swift */; };
		2BD5647B2DC4DA040098CA65 /* SurgeryChecklistHeaderView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 2BD564642DC4DA040098CA65 /* SurgeryChecklistHeaderView.swift */; };
		2BD5647C2DC4DA040098CA65 /* AnyHumaNetworking+Route.swift in Sources */ = {isa = PBXBuildFile; fileRef = 2BD564452DC4DA040098CA65 /* AnyHumaNetworking+Route.swift */; };
		2BD5647D2DC4DA040098CA65 /* SurgeryChecklistViewModel+Preview.swift in Sources */ = {isa = PBXBuildFile; fileRef = 2BD5644E2DC4DA040098CA65 /* SurgeryChecklistViewModel+Preview.swift */; };
		2BD5647E2DC4DA040098CA65 /* CardStyleModifier.swift in Sources */ = {isa = PBXBuildFile; fileRef = 2BD5645B2DC4DA040098CA65 /* CardStyleModifier.swift */; };
		2BD5647F2DC4DA040098CA65 /* SurgeryChecklistDetailView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 2BD5646A2DC4DA040098CA65 /* SurgeryChecklistDetailView.swift */; };
		2BD564802DC4DA040098CA65 /* SurgeryChecklistFooterView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 2BD564632DC4DA040098CA65 /* SurgeryChecklistFooterView.swift */; };
		2BD564812DC4DA040098CA65 /* Int+Extensions.swift in Sources */ = {isa = PBXBuildFile; fileRef = 2BD564522DC4DA040098CA65 /* Int+Extensions.swift */; };
		2BD564822DC4DA040098CA65 /* SurgeryChecklistResponse+UI.swift in Sources */ = {isa = PBXBuildFile; fileRef = 2BD5644D2DC4DA040098CA65 /* SurgeryChecklistResponse+UI.swift */; };
		2BD564832DC4DA040098CA65 /* SurgeryChecklistResponse.swift in Sources */ = {isa = PBXBuildFile; fileRef = 2BD5644C2DC4DA040098CA65 /* SurgeryChecklistResponse.swift */; };
		2BD564842DC4DA040098CA65 /* Analytics+SurgeryChechlist.swift in Sources */ = {isa = PBXBuildFile; fileRef = 2BD564482DC4DA040098CA65 /* Analytics+SurgeryChechlist.swift */; };
		2BD564852DC4DA040098CA65 /* SurgeryChecklistWidget.swift in Sources */ = {isa = PBXBuildFile; fileRef = 2BD564502DC4DA040098CA65 /* SurgeryChecklistWidget.swift */; };
		2BD564862DC4DA040098CA65 /* OnActionCompatible.swift in Sources */ = {isa = PBXBuildFile; fileRef = 2BD5645C2DC4DA040098CA65 /* OnActionCompatible.swift */; };
		2BD564872DC4DA040098CA65 /* SwiftUI+Fonts.swift in Sources */ = {isa = PBXBuildFile; fileRef = 2BD5645F2DC4DA040098CA65 /* SwiftUI+Fonts.swift */; };
		2BD564882DC4DA040098CA65 /* PillButtonStyle.swift in Sources */ = {isa = PBXBuildFile; fileRef = 2BD5645D2DC4DA040098CA65 /* PillButtonStyle.swift */; };
		2BD564892DC4DA040098CA65 /* BouncyButtonStyle.swift in Sources */ = {isa = PBXBuildFile; fileRef = 2BD5645A2DC4DA040098CA65 /* BouncyButtonStyle.swift */; };
		2BD5648A2DC4DA040098CA65 /* CheckmarkView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 2BD564562DC4DA040098CA65 /* CheckmarkView.swift */; };
		2BD5648B2DC4DA040098CA65 /* SurgeryChecklistLoaderView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 2BD564652DC4DA040098CA65 /* SurgeryChecklistLoaderView.swift */; };
		2BD5648C2DC4DA040098CA65 /* SurgeryChecklistCardView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 2BD564692DC4DA040098CA65 /* SurgeryChecklistCardView.swift */; };
		2BD5648D2DC4DA040098CA65 /* SurgeryChecklistRepository.swift in Sources */ = {isa = PBXBuildFile; fileRef = 2BD564462DC4DA040098CA65 /* SurgeryChecklistRepository.swift */; };
		2BD5648E2DC4DA040098CA65 /* SurgeryChecklist.xcassets in Resources */ = {isa = PBXBuildFile; fileRef = 2BD564532DC4DA040098CA65 /* SurgeryChecklist.xcassets */; };
		2BF9DCCD2D8316A9008AE247 /* HumaServierPlugin.h in Headers */ = {isa = PBXBuildFile; fileRef = 2BF9DCCB2D8316A9008AE247 /* HumaServierPlugin.h */; settings = {ATTRIBUTES = (Public, ); }; };
		2F20C4D3D47162452BDF356C /* Pods_HumaServierPlugin.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 99AF5571BCE2FD1C71A6AEE2 /* Pods_HumaServierPlugin.framework */; };
/* End PBXBuildFile section */

/* Begin PBXFileReference section */
		2BD564452DC4DA040098CA65 /* AnyHumaNetworking+Route.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = "AnyHumaNetworking+Route.swift"; sourceTree = "<group>"; };
		2BD564462DC4DA040098CA65 /* SurgeryChecklistRepository.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = SurgeryChecklistRepository.swift; sourceTree = "<group>"; };
		2BD564482DC4DA040098CA65 /* Analytics+SurgeryChechlist.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = "Analytics+SurgeryChechlist.swift"; sourceTree = "<group>"; };
		2BD564492DC4DA040098CA65 /* SurgeryChecklistConfig.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = SurgeryChecklistConfig.swift; sourceTree = "<group>"; };
		2BD5644A2DC4DA040098CA65 /* SurgeryChecklistModel.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = SurgeryChecklistModel.swift; sourceTree = "<group>"; };
		2BD5644B2DC4DA040098CA65 /* SurgeryChecklistModel+Preview.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = "SurgeryChecklistModel+Preview.swift"; sourceTree = "<group>"; };
		2BD5644C2DC4DA040098CA65 /* SurgeryChecklistResponse.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = SurgeryChecklistResponse.swift; sourceTree = "<group>"; };
		2BD5644D2DC4DA040098CA65 /* SurgeryChecklistResponse+UI.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = "SurgeryChecklistResponse+UI.swift"; sourceTree = "<group>"; };
		2BD5644E2DC4DA040098CA65 /* SurgeryChecklistViewModel+Preview.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = "SurgeryChecklistViewModel+Preview.swift"; sourceTree = "<group>"; };
		2BD564502DC4DA040098CA65 /* SurgeryChecklistWidget.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = SurgeryChecklistWidget.swift; sourceTree = "<group>"; };
		2BD564512DC4DA040098CA65 /* Array+Extension.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = "Array+Extension.swift"; sourceTree = "<group>"; };
		2BD564522DC4DA040098CA65 /* Int+Extensions.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = "Int+Extensions.swift"; sourceTree = "<group>"; };
		2BD564532DC4DA040098CA65 /* SurgeryChecklist.xcassets */ = {isa = PBXFileReference; lastKnownFileType = folder.assetcatalog; path = SurgeryChecklist.xcassets; sourceTree = "<group>"; };
		2BD564542DC4DA040098CA65 /* View+Extensions.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = "View+Extensions.swift"; sourceTree = "<group>"; };
		2BD564562DC4DA040098CA65 /* CheckmarkView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = CheckmarkView.swift; sourceTree = "<group>"; };
		2BD564572DC4DA040098CA65 /* CircleLoadingView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = CircleLoadingView.swift; sourceTree = "<group>"; };
		2BD564582DC4DA040098CA65 /* SeparatedVStack.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = SeparatedVStack.swift; sourceTree = "<group>"; };
		2BD5645A2DC4DA040098CA65 /* BouncyButtonStyle.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = BouncyButtonStyle.swift; sourceTree = "<group>"; };
		2BD5645B2DC4DA040098CA65 /* CardStyleModifier.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = CardStyleModifier.swift; sourceTree = "<group>"; };
		2BD5645C2DC4DA040098CA65 /* OnActionCompatible.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = OnActionCompatible.swift; sourceTree = "<group>"; };
		2BD5645D2DC4DA040098CA65 /* PillButtonStyle.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = PillButtonStyle.swift; sourceTree = "<group>"; };
		2BD5645E2DC4DA040098CA65 /* ShimmerModifier.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = ShimmerModifier.swift; sourceTree = "<group>"; };
		2BD5645F2DC4DA040098CA65 /* SwiftUI+Fonts.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = "SwiftUI+Fonts.swift"; sourceTree = "<group>"; };
		2BD564602DC4DA040098CA65 /* TempStyler.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = TempStyler.swift; sourceTree = "<group>"; };
		2BD564622DC4DA040098CA65 /* SurgeryChecklistDateView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = SurgeryChecklistDateView.swift; sourceTree = "<group>"; };
		2BD564632DC4DA040098CA65 /* SurgeryChecklistFooterView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = SurgeryChecklistFooterView.swift; sourceTree = "<group>"; };
		2BD564642DC4DA040098CA65 /* SurgeryChecklistHeaderView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = SurgeryChecklistHeaderView.swift; sourceTree = "<group>"; };
		2BD564652DC4DA040098CA65 /* SurgeryChecklistLoaderView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = SurgeryChecklistLoaderView.swift; sourceTree = "<group>"; };
		2BD564662DC4DA040098CA65 /* SurgeryChecklistPhaseView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = SurgeryChecklistPhaseView.swift; sourceTree = "<group>"; };
		2BD564682DC4DA040098CA65 /* AnySurgeryChecklistViewModel.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = AnySurgeryChecklistViewModel.swift; sourceTree = "<group>"; };
		2BD564692DC4DA040098CA65 /* SurgeryChecklistCardView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = SurgeryChecklistCardView.swift; sourceTree = "<group>"; };
		2BD5646A2DC4DA040098CA65 /* SurgeryChecklistDetailView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = SurgeryChecklistDetailView.swift; sourceTree = "<group>"; };
		2BD5646B2DC4DA040098CA65 /* SurgeryChecklistViewModel.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = SurgeryChecklistViewModel.swift; sourceTree = "<group>"; };
		2BF9DCBE2D8315D1008AE247 /* HumaServierPlugin.framework */ = {isa = PBXFileReference; explicitFileType = wrapper.framework; includeInIndex = 0; path = HumaServierPlugin.framework; sourceTree = BUILT_PRODUCTS_DIR; };
		2BF9DCCB2D8316A9008AE247 /* HumaServierPlugin.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = HumaServierPlugin.h; sourceTree = "<group>"; };
		99AF5571BCE2FD1C71A6AEE2 /* Pods_HumaServierPlugin.framework */ = {isa = PBXFileReference; explicitFileType = wrapper.framework; includeInIndex = 0; path = Pods_HumaServierPlugin.framework; sourceTree = BUILT_PRODUCTS_DIR; };
		CAE5A25DFDCD1752DB77F4FE /* Pods-HumaServierPlugin.release.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-HumaServierPlugin.release.xcconfig"; path = "Target Support Files/Pods-HumaServierPlugin/Pods-HumaServierPlugin.release.xcconfig"; sourceTree = "<group>"; };
		F862DF12727BEE812F15F932 /* Pods-HumaServierPlugin.debug.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-HumaServierPlugin.debug.xcconfig"; path = "Target Support Files/Pods-HumaServierPlugin/Pods-HumaServierPlugin.debug.xcconfig"; sourceTree = "<group>"; };
/* End PBXFileReference section */

/* Begin PBXFrameworksBuildPhase section */
		2BF9DCBB2D8315D1008AE247 /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
				2F20C4D3D47162452BDF356C /* Pods_HumaServierPlugin.framework in Frameworks */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXFrameworksBuildPhase section */

/* Begin PBXGroup section */
		08A015DA3AE9D48D85035509 /* Pods */ = {
			isa = PBXGroup;
			children = (
				F862DF12727BEE812F15F932 /* Pods-HumaServierPlugin.debug.xcconfig */,
				CAE5A25DFDCD1752DB77F4FE /* Pods-HumaServierPlugin.release.xcconfig */,
			);
			name = Pods;
			path = ../Pods;
			sourceTree = "<group>";
		};
		2BD564472DC4DA040098CA65 /* API */ = {
			isa = PBXGroup;
			children = (
				2BD564452DC4DA040098CA65 /* AnyHumaNetworking+Route.swift */,
				2BD564462DC4DA040098CA65 /* SurgeryChecklistRepository.swift */,
			);
			path = API;
			sourceTree = "<group>";
		};
		2BD5644F2DC4DA040098CA65 /* Models */ = {
			isa = PBXGroup;
			children = (
				2BD564482DC4DA040098CA65 /* Analytics+SurgeryChechlist.swift */,
				2BD564492DC4DA040098CA65 /* SurgeryChecklistConfig.swift */,
				2BD5644A2DC4DA040098CA65 /* SurgeryChecklistModel.swift */,
				2BD5644B2DC4DA040098CA65 /* SurgeryChecklistModel+Preview.swift */,
				2BD5644C2DC4DA040098CA65 /* SurgeryChecklistResponse.swift */,
				2BD5644D2DC4DA040098CA65 /* SurgeryChecklistResponse+UI.swift */,
				2BD5644E2DC4DA040098CA65 /* SurgeryChecklistViewModel+Preview.swift */,
			);
			path = Models;
			sourceTree = "<group>";
		};
		2BD564552DC4DA040098CA65 /* Extensions */ = {
			isa = PBXGroup;
			children = (
				2BD564512DC4DA040098CA65 /* Array+Extension.swift */,
				2BD564522DC4DA040098CA65 /* Int+Extensions.swift */,
				2BD564532DC4DA040098CA65 /* SurgeryChecklist.xcassets */,
				2BD564542DC4DA040098CA65 /* View+Extensions.swift */,
			);
			path = Extensions;
			sourceTree = "<group>";
		};
		2BD564592DC4DA040098CA65 /* Helpers */ = {
			isa = PBXGroup;
			children = (
				2BD564562DC4DA040098CA65 /* CheckmarkView.swift */,
				2BD564572DC4DA040098CA65 /* CircleLoadingView.swift */,
				2BD564582DC4DA040098CA65 /* SeparatedVStack.swift */,
			);
			path = Helpers;
			sourceTree = "<group>";
		};
		2BD564612DC4DA040098CA65 /* Styles */ = {
			isa = PBXGroup;
			children = (
				2BD5645A2DC4DA040098CA65 /* BouncyButtonStyle.swift */,
				2BD5645B2DC4DA040098CA65 /* CardStyleModifier.swift */,
				2BD5645C2DC4DA040098CA65 /* OnActionCompatible.swift */,
				2BD5645D2DC4DA040098CA65 /* PillButtonStyle.swift */,
				2BD5645E2DC4DA040098CA65 /* ShimmerModifier.swift */,
				2BD5645F2DC4DA040098CA65 /* SwiftUI+Fonts.swift */,
				2BD564602DC4DA040098CA65 /* TempStyler.swift */,
			);
			path = Styles;
			sourceTree = "<group>";
		};
		2BD564672DC4DA040098CA65 /* Components */ = {
			isa = PBXGroup;
			children = (
				2BD564622DC4DA040098CA65 /* SurgeryChecklistDateView.swift */,
				2BD564632DC4DA040098CA65 /* SurgeryChecklistFooterView.swift */,
				2BD564642DC4DA040098CA65 /* SurgeryChecklistHeaderView.swift */,
				2BD564652DC4DA040098CA65 /* SurgeryChecklistLoaderView.swift */,
				2BD564662DC4DA040098CA65 /* SurgeryChecklistPhaseView.swift */,
			);
			path = Components;
			sourceTree = "<group>";
		};
		2BD5646C2DC4DA040098CA65 /* Widget */ = {
			isa = PBXGroup;
			children = (
				2BD564672DC4DA040098CA65 /* Components */,
				2BD564682DC4DA040098CA65 /* AnySurgeryChecklistViewModel.swift */,
				2BD564692DC4DA040098CA65 /* SurgeryChecklistCardView.swift */,
				2BD5646A2DC4DA040098CA65 /* SurgeryChecklistDetailView.swift */,
				2BD5646B2DC4DA040098CA65 /* SurgeryChecklistViewModel.swift */,
			);
			path = Widget;
			sourceTree = "<group>";
		};
		2BD5646D2DC4DA040098CA65 /* Views */ = {
			isa = PBXGroup;
			children = (
				2BD564552DC4DA040098CA65 /* Extensions */,
				2BD564592DC4DA040098CA65 /* Helpers */,
				2BD564612DC4DA040098CA65 /* Styles */,
				2BD5646C2DC4DA040098CA65 /* Widget */,
			);
			path = Views;
			sourceTree = "<group>";
		};
		2BF9DCB42D8315D1008AE247 = {
			isa = PBXGroup;
			children = (
				2BF9DCCC2D8316A9008AE247 /* HumaServierPlugin */,
				2BF9DCBF2D8315D1008AE247 /* Products */,
				08A015DA3AE9D48D85035509 /* Pods */,
				800B3D5626D4ED692B0B25E0 /* Frameworks */,
			);
			sourceTree = "<group>";
		};
		2BF9DCBF2D8315D1008AE247 /* Products */ = {
			isa = PBXGroup;
			children = (
				2BF9DCBE2D8315D1008AE247 /* HumaServierPlugin.framework */,
			);
			name = Products;
			sourceTree = "<group>";
		};
		2BF9DCCC2D8316A9008AE247 /* HumaServierPlugin */ = {
			isa = PBXGroup;
			children = (
				2BF9DCCE2D8317D1008AE247 /* Source */,
				2BF9DCCB2D8316A9008AE247 /* HumaServierPlugin.h */,
			);
			path = HumaServierPlugin;
			sourceTree = "<group>";
		};
		2BF9DCCE2D8317D1008AE247 /* Source */ = {
			isa = PBXGroup;
			children = (
				2BD564472DC4DA040098CA65 /* API */,
				2BD5644F2DC4DA040098CA65 /* Models */,
				2BD564502DC4DA040098CA65 /* SurgeryChecklistWidget.swift */,
				2BD5646D2DC4DA040098CA65 /* Views */,
			);
			path = Source;
			sourceTree = "<group>";
		};
		800B3D5626D4ED692B0B25E0 /* Frameworks */ = {
			isa = PBXGroup;
			children = (
				99AF5571BCE2FD1C71A6AEE2 /* Pods_HumaServierPlugin.framework */,
			);
			name = Frameworks;
			sourceTree = "<group>";
		};
/* End PBXGroup section */

/* Begin PBXHeadersBuildPhase section */
		2BF9DCB92D8315D1008AE247 /* Headers */ = {
			isa = PBXHeadersBuildPhase;
			buildActionMask = 2147483647;
			files = (
				2BF9DCCD2D8316A9008AE247 /* HumaServierPlugin.h in Headers */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXHeadersBuildPhase section */

/* Begin PBXNativeTarget section */
		2BF9DCBD2D8315D1008AE247 /* HumaServierPlugin */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 2BF9DCC42D8315D1008AE247 /* Build configuration list for PBXNativeTarget "HumaServierPlugin" */;
			buildPhases = (
				46B0D5D8C782E9F32C6F9E3D /* [CP] Check Pods Manifest.lock */,
				2BF9DCB92D8315D1008AE247 /* Headers */,
				2BF9DCBA2D8315D1008AE247 /* Sources */,
				2BF9DCBB2D8315D1008AE247 /* Frameworks */,
				2BF9DCBC2D8315D1008AE247 /* Resources */,
			);
			buildRules = (
			);
			dependencies = (
			);
			name = HumaServierPlugin;
			productName = HumaServierPlugin;
			productReference = 2BF9DCBE2D8315D1008AE247 /* HumaServierPlugin.framework */;
			productType = "com.apple.product-type.framework";
		};
/* End PBXNativeTarget section */

/* Begin PBXProject section */
		2BF9DCB52D8315D1008AE247 /* Project object */ = {
			isa = PBXProject;
			attributes = {
				BuildIndependentTargetsInParallel = 1;
				LastUpgradeCheck = 1600;
				TargetAttributes = {
					2BF9DCBD2D8315D1008AE247 = {
						CreatedOnToolsVersion = 16.0;
					};
				};
			};
			buildConfigurationList = 2BF9DCB82D8315D1008AE247 /* Build configuration list for PBXProject "HumaServierPlugin" */;
			compatibilityVersion = "Xcode 14.0";
			developmentRegion = en;
			hasScannedForEncodings = 0;
			knownRegions = (
				en,
				Base,
			);
			mainGroup = 2BF9DCB42D8315D1008AE247;
			productRefGroup = 2BF9DCBF2D8315D1008AE247 /* Products */;
			projectDirPath = "";
			projectRoot = "";
			targets = (
				2BF9DCBD2D8315D1008AE247 /* HumaServierPlugin */,
			);
		};
/* End PBXProject section */

/* Begin PBXResourcesBuildPhase section */
		2BF9DCBC2D8315D1008AE247 /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				2BD5648E2DC4DA040098CA65 /* SurgeryChecklist.xcassets in Resources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXResourcesBuildPhase section */

/* Begin PBXShellScriptBuildPhase section */
		46B0D5D8C782E9F32C6F9E3D /* [CP] Check Pods Manifest.lock */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			inputFileListPaths = (
			);
			inputPaths = (
				"${PODS_PODFILE_DIR_PATH}/Podfile.lock",
				"${PODS_ROOT}/Manifest.lock",
			);
			name = "[CP] Check Pods Manifest.lock";
			outputFileListPaths = (
			);
			outputPaths = (
				"$(DERIVED_FILE_DIR)/Pods-HumaServierPlugin-checkManifestLockResult.txt",
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "diff \"${PODS_PODFILE_DIR_PATH}/Podfile.lock\" \"${PODS_ROOT}/Manifest.lock\" > /dev/null\nif [ $? != 0 ] ; then\n    # print error to STDERR\n    echo \"error: The sandbox is not in sync with the Podfile.lock. Run 'pod install' or update your CocoaPods installation.\" >&2\n    exit 1\nfi\n# This output is used by Xcode 'outputs' to avoid re-running this script phase.\necho \"SUCCESS\" > \"${SCRIPT_OUTPUT_FILE_0}\"\n";
			showEnvVarsInLog = 0;
		};
/* End PBXShellScriptBuildPhase section */

/* Begin PBXSourcesBuildPhase section */
		2BF9DCBA2D8315D1008AE247 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				2BD5646E2DC4DA040098CA65 /* SurgeryChecklistConfig.swift in Sources */,
				2BD5646F2DC4DA040098CA65 /* AnySurgeryChecklistViewModel.swift in Sources */,
				2BD564702DC4DA040098CA65 /* View+Extensions.swift in Sources */,
				2BD564712DC4DA040098CA65 /* SeparatedVStack.swift in Sources */,
				2BD564722DC4DA040098CA65 /* Array+Extension.swift in Sources */,
				2BD564732DC4DA040098CA65 /* SurgeryChecklistPhaseView.swift in Sources */,
				2BD564742DC4DA040098CA65 /* SurgeryChecklistDateView.swift in Sources */,
				2BD564752DC4DA040098CA65 /* SurgeryChecklistViewModel.swift in Sources */,
				2BD564762DC4DA040098CA65 /* TempStyler.swift in Sources */,
				2BD564772DC4DA040098CA65 /* CircleLoadingView.swift in Sources */,
				2BD564782DC4DA040098CA65 /* ShimmerModifier.swift in Sources */,
				2BD564792DC4DA040098CA65 /* SurgeryChecklistModel+Preview.swift in Sources */,
				2BD5647A2DC4DA040098CA65 /* SurgeryChecklistModel.swift in Sources */,
				2BD5647B2DC4DA040098CA65 /* SurgeryChecklistHeaderView.swift in Sources */,
				2BD5647C2DC4DA040098CA65 /* AnyHumaNetworking+Route.swift in Sources */,
				2BD5647D2DC4DA040098CA65 /* SurgeryChecklistViewModel+Preview.swift in Sources */,
				2BD5647E2DC4DA040098CA65 /* CardStyleModifier.swift in Sources */,
				2BD5647F2DC4DA040098CA65 /* SurgeryChecklistDetailView.swift in Sources */,
				2BD564802DC4DA040098CA65 /* SurgeryChecklistFooterView.swift in Sources */,
				2BD564812DC4DA040098CA65 /* Int+Extensions.swift in Sources */,
				2BD564822DC4DA040098CA65 /* SurgeryChecklistResponse+UI.swift in Sources */,
				2BD564832DC4DA040098CA65 /* SurgeryChecklistResponse.swift in Sources */,
				2BD564842DC4DA040098CA65 /* Analytics+SurgeryChechlist.swift in Sources */,
				2BD564852DC4DA040098CA65 /* SurgeryChecklistWidget.swift in Sources */,
				2BD564862DC4DA040098CA65 /* OnActionCompatible.swift in Sources */,
				2BD564872DC4DA040098CA65 /* SwiftUI+Fonts.swift in Sources */,
				2BD564882DC4DA040098CA65 /* PillButtonStyle.swift in Sources */,
				2BD564892DC4DA040098CA65 /* BouncyButtonStyle.swift in Sources */,
				2BD5648A2DC4DA040098CA65 /* CheckmarkView.swift in Sources */,
				2BD5648B2DC4DA040098CA65 /* SurgeryChecklistLoaderView.swift in Sources */,
				2BD5648C2DC4DA040098CA65 /* SurgeryChecklistCardView.swift in Sources */,
				2BD5648D2DC4DA040098CA65 /* SurgeryChecklistRepository.swift in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXSourcesBuildPhase section */

/* Begin XCBuildConfiguration section */
		2BF9DCC52D8315D1008AE247 /* Debug */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = F862DF12727BEE812F15F932 /* Pods-HumaServierPlugin.debug.xcconfig */;
			buildSettings = {
				BUILD_LIBRARY_FOR_DISTRIBUTION = YES;
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEFINES_MODULE = YES;
				DEVELOPMENT_TEAM = D67VZ62WN4;
				DYLIB_COMPATIBILITY_VERSION = 1;
				DYLIB_CURRENT_VERSION = 1;
				DYLIB_INSTALL_NAME_BASE = "@rpath";
				ENABLE_MODULE_VERIFIER = YES;
				GENERATE_INFOPLIST_FILE = YES;
				INFOPLIST_KEY_NSHumanReadableCopyright = "";
				INSTALL_PATH = "$(LOCAL_LIBRARY_DIR)/Frameworks";
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
					"@loader_path/Frameworks",
				);
				MARKETING_VERSION = 1.0;
				MODULE_VERIFIER_SUPPORTED_LANGUAGES = "objective-c objective-c++";
				MODULE_VERIFIER_SUPPORTED_LANGUAGE_STANDARDS = "gnu17 gnu++20";
				PRODUCT_BUNDLE_IDENTIFIER = com.HumaServierPlugin;
				PRODUCT_NAME = "$(TARGET_NAME:c99extidentifier)";
				SKIP_INSTALL = YES;
				SWIFT_EMIT_LOC_STRINGS = YES;
				SWIFT_INSTALL_OBJC_HEADER = NO;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
			};
			name = Debug;
		};
		2BF9DCC62D8315D1008AE247 /* Release */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = CAE5A25DFDCD1752DB77F4FE /* Pods-HumaServierPlugin.release.xcconfig */;
			buildSettings = {
				BUILD_LIBRARY_FOR_DISTRIBUTION = YES;
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEFINES_MODULE = YES;
				DEVELOPMENT_TEAM = D67VZ62WN4;
				DYLIB_COMPATIBILITY_VERSION = 1;
				DYLIB_CURRENT_VERSION = 1;
				DYLIB_INSTALL_NAME_BASE = "@rpath";
				ENABLE_MODULE_VERIFIER = YES;
				GENERATE_INFOPLIST_FILE = YES;
				INFOPLIST_KEY_NSHumanReadableCopyright = "";
				INSTALL_PATH = "$(LOCAL_LIBRARY_DIR)/Frameworks";
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
					"@loader_path/Frameworks",
				);
				MARKETING_VERSION = 1.0;
				MODULE_VERIFIER_SUPPORTED_LANGUAGES = "objective-c objective-c++";
				MODULE_VERIFIER_SUPPORTED_LANGUAGE_STANDARDS = "gnu17 gnu++20";
				PRODUCT_BUNDLE_IDENTIFIER = com.HumaServierPlugin;
				PRODUCT_NAME = "$(TARGET_NAME:c99extidentifier)";
				SKIP_INSTALL = YES;
				SWIFT_EMIT_LOC_STRINGS = YES;
				SWIFT_INSTALL_OBJC_HEADER = NO;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
			};
			name = Release;
		};
		2BF9DCC72D8315D1008AE247 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				ASSETCATALOG_COMPILER_GENERATE_SWIFT_ASSET_SYMBOL_EXTENSIONS = YES;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++20";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				COPY_PHASE_STRIP = NO;
				CURRENT_PROJECT_VERSION = 1;
				DEBUG_INFORMATION_FORMAT = dwarf;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_TESTABILITY = YES;
				ENABLE_USER_SCRIPT_SANDBOXING = YES;
				GCC_C_LANGUAGE_STANDARD = gnu17;
				GCC_DYNAMIC_NO_PIC = NO;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_OPTIMIZATION_LEVEL = 0;
				GCC_PREPROCESSOR_DEFINITIONS = (
					"DEBUG=1",
					"$(inherited)",
				);
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 18.0;
				LOCALIZATION_PREFERS_STRING_CATALOGS = YES;
				MTL_ENABLE_DEBUG_INFO = INCLUDE_SOURCE;
				MTL_FAST_MATH = YES;
				ONLY_ACTIVE_ARCH = YES;
				SDKROOT = iphoneos;
				SWIFT_ACTIVE_COMPILATION_CONDITIONS = "DEBUG $(inherited)";
				SWIFT_OPTIMIZATION_LEVEL = "-Onone";
				VERSIONING_SYSTEM = "apple-generic";
				VERSION_INFO_PREFIX = "";
			};
			name = Debug;
		};
		2BF9DCC82D8315D1008AE247 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				ASSETCATALOG_COMPILER_GENERATE_SWIFT_ASSET_SYMBOL_EXTENSIONS = YES;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++20";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				COPY_PHASE_STRIP = NO;
				CURRENT_PROJECT_VERSION = 1;
				DEBUG_INFORMATION_FORMAT = "dwarf-with-dsym";
				ENABLE_NS_ASSERTIONS = NO;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_USER_SCRIPT_SANDBOXING = YES;
				GCC_C_LANGUAGE_STANDARD = gnu17;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 18.0;
				LOCALIZATION_PREFERS_STRING_CATALOGS = YES;
				MTL_ENABLE_DEBUG_INFO = NO;
				MTL_FAST_MATH = YES;
				SDKROOT = iphoneos;
				SWIFT_COMPILATION_MODE = wholemodule;
				VALIDATE_PRODUCT = YES;
				VERSIONING_SYSTEM = "apple-generic";
				VERSION_INFO_PREFIX = "";
			};
			name = Release;
		};
/* End XCBuildConfiguration section */

/* Begin XCConfigurationList section */
		2BF9DCB82D8315D1008AE247 /* Build configuration list for PBXProject "HumaServierPlugin" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				2BF9DCC72D8315D1008AE247 /* Debug */,
				2BF9DCC82D8315D1008AE247 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		2BF9DCC42D8315D1008AE247 /* Build configuration list for PBXNativeTarget "HumaServierPlugin" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				2BF9DCC52D8315D1008AE247 /* Debug */,
				2BF9DCC62D8315D1008AE247 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
/* End XCConfigurationList section */
	};
	rootObject = 2BF9DCB52D8315D1008AE247 /* Project object */;
}
