//
//  AnySurgeryChecklistViewModel.swift
//  HumaSurgeryChecklistWidget
//
//  Created by <PERSON> on 16.12.2024.
//

import SwiftUI

// MARK: - Interface

protocol AnySurgeryChecklistViewModel: ObservableObject {

    var config: SurgeryChecklistConfig { get }
    var content: ContentState<SurgeryChecklistModel> { get }

    func loadData() async

    func handleOnCheck(_ phase: SurgeryChecklistItemModel)
    func handleOnExpand(_ phase: SurgeryChecklistItemModel)
    func handleOnEditDate()
    func handleOnShowAll()
    func handleOnBack()
}

// MARK: - Configs

extension AnySurgeryChecklistViewModel {
    var headerConfig: SurgeryChecklistHeaderView.Config {
        .init(
            title: config.title,
            showProgressBar: content.result?.surgeryDate != nil ? config.showProgress : false,
            maxCount: config.totalCount,
            progress: content.result?.progress ?? .zero
        )
    }

    var detailHeaderConfig: SurgeryChecklistHeaderView.Config {
        .init(
            title: "Checklist completion",
            showProgressBar: content.result?.surgeryDate != nil ? config.showProgress : false,
            maxCount: config.totalCount,
            progress: content.result?.progress ?? .zero
        )
    }

    func footerConfig(from model: SurgeryChecklistModel) -> SurgeryChecklistFooterView.Config? {
        guard let surgeryDate = model.surgeryDate,
              let title = attributedSurgeryTitle(for: surgeryDate).converted else {
            return nil
        }
        let dateText = surgeryDate.globalAppFormat
        return .init(title: title, dateText: dateText)
    }
}

// MARK: - Private Helpers

private extension AnySurgeryChecklistViewModel {
    func attributedSurgeryTitle(
        for surgeryDate: Date,
        using calendar: Calendar = .current
    ) -> NSAttributedString {
        let today = Date().startOfDay()
        let surgeryDay = surgeryDate.startOfDay()
        let dayDifference = surgeryDay.days(since: today)

        let defaultAttributes: [NSAttributedString.Key: Any] = [
            .font: UIFont.default,
            .foregroundColor: UIColor.charcoalGrey
        ]
        let boldAttributes: [NSAttributedString.Key: Any] = [
            .font: UIFont.bold,
            .foregroundColor: UIColor.charcoalGrey
        ]

        /// Surgery day is today
        if dayDifference == 0 {
            let result = NSMutableAttributedString()
            result.append(NSAttributedString(string: "Surgery is ", attributes: defaultAttributes))
            result.append(NSAttributedString(string: "today", attributes: boldAttributes))
            return result

        /// Surgery is in the future
        } else if dayDifference > 0 {

            let unit = (dayDifference == 1) ? "day" : "days"
            let result = NSMutableAttributedString()
            result.append(NSAttributedString(string: "Surgery in ", attributes: defaultAttributes))
            result.append(NSAttributedString(string: "\(dayDifference)", attributes: boldAttributes))
            result.append(NSAttributedString(string: " \(unit)", attributes: defaultAttributes))
            return result

        /// Surgery is in the past
        } else {
            let daysAgo = abs(dayDifference)
            let unit = (daysAgo == 1) ? "day ago" : "days ago"
            let result = NSMutableAttributedString()
            result.append(NSAttributedString(string: "Surgery was ", attributes: defaultAttributes))
            result.append(NSAttributedString(string: "\(daysAgo)", attributes: boldAttributes))
            result.append(NSAttributedString(string: " \(unit)", attributes: defaultAttributes))
            return result
        }
    }
}

// MARK: - Bundle

class SurgeryChecklistBundle: NSObject {}
