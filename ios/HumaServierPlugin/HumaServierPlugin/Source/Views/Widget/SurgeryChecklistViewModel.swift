//
//  SurgeryChecklistViewModel.swift
//  HumaSurgeryChecklistWidget
//
//  Created by <PERSON> on 02.12.2024.
//

import SwiftUI
import HumaFoundation

// MARK: - View Model

final class SurgeryChecklistViewModel: AnySurgeryChecklistViewModel {

    let info: WidgetConfigInfo
    @Published private(set) var config: SurgeryChecklistConfig
    @Published private(set) var content: ContentState<SurgeryChecklistModel> = .loading

    // MARK: - Events

    typealias DateUpdateHandler = (previusDate: Date?, submitter: AnyDatePickerSubmitter)

    /// To notificat UIKit updates.
    let onChange = VoidEvent()
    /// Event to open detail page on tap of `show all` footer button.
    let onShowAll = TriggeredEvent<SurgeryChecklistModel>()
    /// Event to go back from
    let onGoBack = VoidEvent()
    /// Even to open date picker, paramer is previusly selected date (if present).
    let onEditDate = TriggeredEvent<DateUpdateHandler>()

    // MARK: - Private Properties

    private let repository: AnySurgeryChecklistRepository
    private let onboardingManager: AnyLateOnboardingManager
    private let analytics: AnyAnalytics

    private var response: SurgeryChecklistResponse?
    private var isLoadingData = false

    // MARK: - Init

    init(
        info: WidgetConfigInfo,
        config: SurgeryChecklistConfig,
        content: ContentState<SurgeryChecklistModel> = .loading,
        repository: AnySurgeryChecklistRepository,
        onboardingManager: AnyLateOnboardingManager,
        analytics: AnyAnalytics
    ) {
        self.info = info
        self.config = config
        self.content = content
        self.repository = repository
        self.onboardingManager = onboardingManager
        self.analytics = analytics
    }

    func loadData() async {
        guard !isLoadingData else { return }
        isLoadingData = true

        do {
            let newResponse = try await repository.getWidgetData(widgetType: info.type, widgetID: info.id)
            Task { @MainActor in
                let previusContent = content.result
                let newContent = newResponse.uiModel(previus: previusContent)

                response = newResponse
                content = .results(newContent)
                onChange.trigger()
                isLoadingData = false
            }
        } catch {
            isLoadingData = false
            content = .empty
            onChange.trigger()
        }
    }

    func handleOnCheck(_ phase: SurgeryChecklistItemModel) {
        guard onboardingManager.ensureOnboardingCompletion() else { return }

        guard let originalPhase = (response?.checklists ?? [])
            .flatMap(\.checklist)
            .first(where:  { $0.constantID == phase.id  }) else {
            return
        }
        toggleCheck(id: phase.id)
        Task {
            if phase.isCompleted {
                try await repository.deleteCheckmark(originalPhase)
                trackCheckmarkIncompleted(originalPhase)

            } else {
                try await repository.completeCheckmark(originalPhase)
                trackCheckmarkCompleted(originalPhase)
            }
            await loadData()
        }
    }

    func handleOnExpand(_ phase: SurgeryChecklistItemModel)  {
        content.update { content in
            guard let section = content.checklists.firstIndex { $0.checklist.contains(where: { $0.id == phase.id }) },
            let index = content.checklists[section].checklist.firstIndex(where: { $0.id == phase.id }) else {
                return
            }
            trackExpandEvent(content.checklists[section].checklist[index])
            content.checklists[section].checklist[index].isExpanded.toggle()
        }
    }

    func handleOnEditDate() {
        guard onboardingManager.ensureOnboardingCompletion() else { return }
        onEditDate.trigger((content.result?.surgeryDate, self))
    }

    func handleOnShowAll() {
        guard onboardingManager.ensureOnboardingCompletion() else { return }
        trackDetailsOpenEvent()

        Task { @MainActor in
            content.result.map {
                onShowAll.trigger($0)
            }
        }
    }

    func handleOnBack() {
        Task { @MainActor in
            onGoBack.trigger()
        }
    }
}

// MARK: - Private Helpers

private extension SurgeryChecklistViewModel {
    func toggleCheck(id: String) {
        content.update { content in
            guard let section = content.checklists.firstIndex { $0.checklist.contains(where: { $0.id == id }) },
                  let index = content.checklists[section].checklist.firstIndex(where: { $0.id == id }) else {
                return
            }
            content.checklists[section].checklist[index].isCompleted.toggle()
        }
    }
}

// MARK: - Analytics

private extension SurgeryChecklistViewModel {
    func trackDetailsOpenEvent() {
        analytics.track(.surgeryChecklistDetailsOpen)
    }

    func trackCheckmarkCompleted(_ item: SurgeryChecklistResponse.Item) {
        analytics.track(.surgeryChecklistItemCompleted(item.id))
    }

    func trackCheckmarkIncompleted(_ item: SurgeryChecklistResponse.Item) {
        analytics.track(.surgeryChecklistItemIncompleted(item.id))
    }

    func trackExpandEvent(_ item: SurgeryChecklistItemModel) {
        if item.isExpanded {
            analytics.track(.surgeryChecklistItemCollapsed(item.id))
        } else {
            analytics.track(.surgeryChecklistItemExpanded(item.id))
        }
    }

    func trackDateChangedEvent(previus previusDate: Date?, new newDate: Date) {
        if previusDate.isNil {
            analytics.track(.surgeryChecklistDateAdded)
        } else {
            analytics.track(.surgeryChecklistDateUpdated)
        }
    }
}

// MARK: - AnyDatePickerSubmitter

extension SurgeryChecklistViewModel: AnyDatePickerSubmitter {
    func saveDate(_ date: Date, completion: @escaping BoolCompletion) {
        let previusDate = response?.surgeryDate
        repository.updateDate(date) { [weak self] result in
            guard let self else { return }
            switch result {
            case .success:
                trackDateChangedEvent(previus: previusDate, new: date)
                refreshSurgeryDate(date)
                completion(true)

            case .failure(let error):
                Logger.e("Failed to update surgery date: \(error)")
                completion(false)
            }
        }
    }

    private func refreshSurgeryDate(_ newDate: Date) {
        content.update { reponse in
            reponse.surgeryDate = newDate
            onChange.trigger()
        }
        Task {
            await loadData()
        }
    }
}
