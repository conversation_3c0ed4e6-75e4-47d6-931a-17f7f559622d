//
//  SurgeryChecklistDetailView.swift
//  HumaSurgeryChecklistWidget
//
//  Created by <PERSON> on 19.11.2024.
//

import SwiftUI

struct SurgeryChecklistDetailView<ViewModel: AnySurgeryChecklistViewModel>: View {
    @StateObject var viewModel: ViewModel

    // MARK: - Body

    var body: some View {
        ZStack {
            loadingView
                .opacity(viewModel.content.isLoading)
            if let result = viewModel.content.result {
                contentView(model: result)
                    .opacity(viewModel.content.shouldShowResults)
            }
        }
        .task {
            await viewModel.loadData()
        }
    }
}

// MARK: - Private Helpers

private extension SurgeryChecklistDetailView {
    var loadingView: some View {
        SurgeryChecklistLoader(config: viewModel.config)
    }

    func contentView(model: SurgeryChecklistModel) -> some View {
        ZStack(alignment: .top) {
            ScrollView {
                VStack {
                    backButton
                        .padding(.top, 16)
                        .padding(.bottom, 8)
                        .padding(.leading, 2)

                    Group {
                        title
                            .padding(.bottom, 4)
                        subtitle

                        mainInfoCardView(model: model)
                            .padding(.top, 20)
                            .padding(.bottom, 8)

                        listView(model: model)
                    }
                    .padding(.horizontal, 20)
                }

                Spacer()
                    .frame(minHeight: 40)
            }
            statusBarBlur
        }
        .background(Color.offWhiteBackground)
    }

    var title: some View {
        Text(viewModel.config.title)
            .font(.header)
            .aligned(to: .leading)
    }

    var subtitle: some View {
        viewModel.config.description.map {
            Text($0)
                .font(.default)
                .multilineTextAlignment(.leading)
                .aligned(to: .leading)
        }
    }

    var backButton: some View {
        Button(action: viewModel.handleOnBack) {
            Image("sc_back_arrow", bundle: Bundle(for: SurgeryChecklistBundle.self))
            .frame(square: 60)
        }
        .bouncyStyle()
        .frame(square: 60)
        .aligned(to: .leading)
    }

    var statusBarBlur: some View {
        Color.clear
            .background(.ultraThinMaterial)
            .mask(
                LinearGradient(
                    colors: [Color.clear, Color.black],
                    startPoint: .bottom,
                    endPoint: .top
                )
            )
            .ignoresSafeArea(edges: .top)
            .frame(height: 30)
    }

    func mainInfoCardView(model: SurgeryChecklistModel) -> some View {
        VStack(spacing: 16) {
            SurgeryChecklistHeaderView(
                config: viewModel.detailHeaderConfig,
                showsCount: true,
                showsChevron: false,
                showsAddDate: viewModel.config.editDate
            )
            separator
                .visible(viewModel.config.showDate)
            dateView(model: model)
        }
        .padding()
        .enableCardStyling()
    }

    var separator: some View {
        Color.veryLightGrey.frame(height: 1)
    }

    @ViewBuilder
    func dateView(model: SurgeryChecklistModel) -> some View {
        if viewModel.config.showDate {
            SurgeryChecklistDateView(
                date: model.surgeryDate,
                showsEdit: viewModel.config.editDate
            )
            .on(.edit) {
                viewModel.handleOnEditDate()
            }
        }
    }

    func listView(model: SurgeryChecklistModel) -> some View {
        VStack(spacing: 16) {
            ForEach(model.checklists, id: \.id) { group in
                groupHeaderView(item: group)
                    .padding(.top, 16)
                ForEach(group.checklist, id: \.id) { phase in
                    SurgeryChecklistItemView(item: phase)
                        .on(.cheсk) { _ in
                            viewModel.handleOnCheck(phase)
                        }
                        .on(.expand) { _ in
                            viewModel.handleOnExpand(phase)
                        }
                        .padding(.vertical, 12)
                        .padding(.horizontal, 16)
                        .enableCardStyling()
                }
            }
        }
    }

    func groupHeaderView(item: SurgeryChecklistPhaseModel) -> some View {
        HStack {
            Text(item.title)
                .bold()
            Spacer()
            if viewModel.config.showOffset, let description = item.description {
                Text(description)
                    .foregroundColor(.grey)
                    .font(.small)
            }
        }
    }
}

// MARK: - Preview

#Preview("DEFAULT") {
    SurgeryChecklistDetailView(viewModel: PreviewSurgeryChecklistViewModel.preview)
}

#Preview("MULTILINE") {
    SurgeryChecklistDetailView(viewModel: PreviewSurgeryChecklistViewModel.preview1)
}

#Preview("NO DATE") {
    SurgeryChecklistDetailView(viewModel: PreviewSurgeryChecklistViewModel.preview2)
}

#Preview("NO PROGRESS") {
    SurgeryChecklistDetailView(viewModel: PreviewSurgeryChecklistViewModel.preview3)
}

#Preview("NO EDIT DATE") {
    SurgeryChecklistDetailView(viewModel: PreviewSurgeryChecklistViewModel.preview5)
}

#Preview("NO OFFSET") {
    SurgeryChecklistDetailView(viewModel: PreviewSurgeryChecklistViewModel.preview6)
}

#Preview("NO ANYTHING") {
    SurgeryChecklistDetailView(viewModel: PreviewSurgeryChecklistViewModel.preview7)
}
