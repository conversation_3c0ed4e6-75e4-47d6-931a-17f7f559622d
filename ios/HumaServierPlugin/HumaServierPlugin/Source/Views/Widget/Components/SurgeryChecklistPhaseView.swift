//
//  SurgeryChecklistPhaseView.swift
//  HumaSurgeryChecklistWidget
//
//  Created by <PERSON> on 18.11.2024.
//

import SwiftUI

// TODO: - Rename
protocol Onable {
    associatedtype Action: Hashable
    typealias ActionHandler = (Action) -> Void

    var handlers: [Action: ActionHandler] { get set }

}

extension Onable {
    func on(_ action: Action, handler: @escaping ActionHandler) -> Self {
        updatedCopy { $0.handlers[action] = handler }
    }

    func handleAction(_ action: Action) {
        handlers[action]?(action)
    }

    func updatedCopy(_ update: (inout Self) -> Void) -> Self {
        var copy = self
        update(&copy)
        return copy
    }
}

struct SurgeryChecklistItemView: View, Onable {
    enum Action {
        case cheсk
        case expand
    }

    var item: SurgeryChecklistItemModel
    var handlers: [Action : ActionHandler] = [:]

    init(item: SurgeryChecklistItemModel) {
        self.item = item
    }

    // MARK: - Body

    var body: some View {
        VStack(alignment: .leading, spacing: 8) {
            HStack {
                checkmarkButton
                    .expandTouchArea()
                HStack {
                    titleText
                    Spacer()
                    expandChevron
                        .hidden(item.description == nil)
                }
                .expandTouchArea()
                .onTapGesture {
                    withAnimation(.easeInOut(duration: 0.2)) {
                        handleAction(.expand)
                    }
                }
                .background(.white)
            }
            if item.isExpanded, let description = item.description {
                descriptionText(description)
            }
        }
        .animation(.easeInOut(duration: 0.2), value: item.isExpanded)
    }
}

// MARK: - Private Helpers

private extension SurgeryChecklistItemView {
    var checkmarkButton: some View {
        Group {
            if item.isActive {
                CheckmarkView(isChecked: item.isCompleted, isLoading: false)
                    .frame(square: 24)
                    .onTapGesture {
                        handleAction(.cheсk)
                    }
                    .opacity(1)
            } else {
                CheckmarkView(isChecked: item.isCompleted, isLoading: false)
                    .frame(square: 24)
                    .opacity(0.6)
            }
        }
    }

    var titleText: some View {
        Text(item.title)
            .font(.medium)
            .foregroundColor(.charcoalGrey)
            .lineSpacing(4)
            .opacity(item.isCompleted || !item.isActive ? 0.6 : 1)
    }

    var expandChevron: some View {
        Image(systemName: "chevron.up")
            .font(.default)
            .foregroundColor(.charcoalGrey)
            .rotationEffect(.degrees(item.isExpanded ? 0 : 180))
            .animation(.easeInOut(duration: 0.2), value: item.isExpanded)
            .opacity(item.isActive ? 1 : 0.6)
    }


    func descriptionText(_ description: String) -> some View {
        Text(description)
            .font(.small)
            .foregroundColor(.charcoalGrey)
            .padding(.leading, 32)
            .transition(.limitedSlideAndFade)
            .opacity(item.isActive ? 1 : 0.6)
    }
}

// MARK: - Extensions

private extension AnyTransition {
    static var limitedSlideAndFade: AnyTransition {
        AnyTransition.modifier(
            active: OffsetFadeModifier(offset: -10, opacity: 0),
            identity: OffsetFadeModifier(offset: 0, opacity: 1)
        )
    }
}

private struct OffsetFadeModifier: ViewModifier {
    let offset: CGFloat
    let opacity: Double

    func body(content: Content) -> some View {
        content
            .offset(y: offset)
            .opacity(opacity)
    }
}

// MARK: - Preview

#Preview {
    ZStack {
        Color.lightGrey3
            .ignoresSafeArea()
        ScrollView {
            VStack {
                Group {
                    SurgeryChecklistItemView(
                        item: .init(
                            id: UUID().uuidString,
                            title: "How easy will it be for me to get to the kitchen?",
                            description: "Find out how to keep essentials within easy reach and what adjustments might make meal prep easier.",
                            isActive: true,
                            isCompleted: false,
                            isExpanded: false,
                            isLoading: false
                        )
                    )
                    SurgeryChecklistItemView(
                        item: .init(
                            id: UUID().uuidString,
                            title: "How easy will it be for me to get to the kitchen?",
                            description: "Find out how to keep essentials within easy reach and what adjustments might make meal prep easier.",
                            isActive: false,
                            isCompleted: false,
                            isExpanded: false,
                            isLoading: false
                        )
                    )
                }
                .background(.white)
                .padding()
            }
        }
    }
}
