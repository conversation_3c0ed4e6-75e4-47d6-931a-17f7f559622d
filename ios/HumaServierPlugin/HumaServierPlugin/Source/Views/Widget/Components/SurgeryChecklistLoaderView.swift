//
//  SurgeryChecklistLoader.swift
//  HumaSurgeryChecklistWidget
//
//  Created by <PERSON> on 18.11.2024.
//

import SwiftUI

struct SurgeryChecklistLoader: View {
    let config: SurgeryChecklistConfig

    // MARK: - Body

    var body: some View {
        VStack(alignment: .leading, spacing: 8) {
            title
            progress
                .visible(config.showProgress)

            RoundedRectangle(cornerRadius: 4)
                .frame(width: 140, height: 16)
                .padding(.top, 8)
                .aligned(to: .leading)

            dateView
                .visible(config.showDate)
        }
        .padding(.top, 8)
        .padding(.bottom, 4)
        .frame(maxWidth: .infinity)
        .shimmer()
    }
}


// MARK: - Private Helpers

extension SurgeryChecklistLoader {
    var title: some View {
        HStack {
            VStack {
                RoundedRectangle(cornerRadius: 8)
                    .frame(width: 200, height: 14)
                    .aligned(to: .leading)

                RoundedRectangle(cornerRadius: 8)
                    .frame(width: 100, height: 14)
                    .aligned(to: .leading)
            }
            Spacer()

            RoundedRectangle(cornerRadius: 8)
                .frame(width: 40, height: 10)
                .aligned(to: .top)
        }
    }

    var progress: some View {
        RoundedRectangle(cornerRadius: 4)
            .padding(.top, 4)
            .frame(maxWidth: .infinity)
            .frame(height: 12)
            .aligned(to: .leading)
    }

    var dateView: some View {
        RoundedRectangle(cornerRadius: 4)
            .frame(width: 80, height: 8)
            .aligned(to: .leading)
    }
}

// MARK: - Preview

#Preview {
    ZStack {
        Color.lightGrey3
            .ignoresSafeArea()
        ScrollView {
            VStack(spacing: 40) {
                SurgeryChecklistCardView(
                    viewModel: PreviewSurgeryChecklistViewModel.preview1
                )
                
                SurgeryChecklistLoader(config: .preview0)
                    .padding(.horizontal, 16)
                    .padding(.vertical, 10)
                    .enableCardStyling()
                    .padding()

                SurgeryChecklistLoader(config: .preview2)
                    .padding(.horizontal, 16)
                    .padding(.vertical, 10)
                    .enableCardStyling()
                    .padding()

                SurgeryChecklistLoader(config: .preview3)
                    .padding(.horizontal, 16)
                    .padding(.vertical, 10)
                    .enableCardStyling()
                    .padding()

                SurgeryChecklistLoader(config: .preview4)
                    .padding(.horizontal, 16)
                    .padding(.vertical, 10)
                    .enableCardStyling()
                    .padding()
            }
        }
    }

}
