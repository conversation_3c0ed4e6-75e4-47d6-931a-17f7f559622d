//
//  SurgeryChecklistHeaderView.swift
//  HumaSurgeryChecklistWidget
//
//  Created by <PERSON> on 18.11.2024.
//

import SwiftUI

struct SurgeryChecklistHeaderView: View, OnActionCompatible {
    enum Action {
        case addDate
    }
    struct Config {
        let title: String
        let showProgressBar: Bool
        let maxCount: Int
        var progress: Int

        var isCompleted: Bool { progress == maxCount }
    }

    // MARK: - Public Properties

    let config: Config
    let showsCount: Bool
    let showsChevron: Bool
    let showsAddDate: Bool

    var handlers: [Action: ActionHandler] = [:]

    // MARK: - Life Cycle

    var body: some View {
        VStack(alignment: .leading, spacing: 12) {
            HStack(spacing: .zero) {
                titleText
                    .frame(minHeight: 24)
                    .animation(.default, value: config.progress)

                Spacer()

                counView
                    .frame(height: 24)
                    .frame(maxHeight: .infinity, alignment: .top)
             }
            .frame(minHeight: 24)
            .clipped()

            progressBar
                .transition(.opacity)
                .animation(.default, value: showsCount)
                .visible(config.showProgressBar && showsCount)

            Group {
                separator
                placeholderTitleText
                placeholderDescriptionText
                placeholderEditButton
                    .visible(showsAddDate)
            }
            .animation(.default, value: showsCount)
            .hidden(showsCount)
        }
    }
}

// MARK: - Private Helpers

private extension SurgeryChecklistHeaderView {
    var counView: some View {
        HStack(spacing: 4) {
            countLabel
                .transition(.opacity)
                .animation(.easeInOut(duration: 0.4), value: showsCount)

            chevronIcon
                .visible(showsChevron)
        }
        .visible(showsCount)
    }

    var countLabel: some View {
        HStack(spacing: .zero) {
            Text("\(config.progress)")
                .font(.xSmallBold)
                .foregroundColor(.lightGrey1)
                .numericTextTransition()
                .animation(.default, value: config.progress)
            Text(" " + "of \(config.maxCount)")
                .font(.xSmall)
                .foregroundColor(.lightGrey1)
        }
    }

    var chevronIcon: some View {
        Image("sc_chevron", bundle: Bundle(for: SurgeryChecklistBundle.self))
            .renderingMode(.template)
            .foregroundColor(.lightGrey2)
    }

    var titleText: some View {
        Text(config.title)
            .multilineTextAlignment(.leading)
            .font(.semibold)
            .foregroundColor(.grey)
            .animation(.easeInOut(duration: 0.3), value: config.isCompleted)
    }

    var separator: some View {
        Color.veryLightGrey.frame(height: 1)
    }

    var progressBar: some View {
        GeometryReader { geometry in
            ZStack(alignment: .leading) {
                progressBarBackgeound
                progressBarLine(geometry)
            }
        }
        .frame(height: 8)
    }

    var progressBarBackgeound: some View {
        Capsule()
            .frame(height: 8)
            .foregroundColor(.veryLightGrey)
    }

    func progressBarLine(_ geometry: GeometryProxy) -> some View {
        Capsule()
            .frame(
                width: geometry.size.width * CGFloat(config.progress) / CGFloat(config.maxCount),
                height: 8
            )
            .foregroundColor(.charcoalGrey)
            .animation(.easeInOut(duration: 0.3), value: config.progress)
    }

    var placeholderTitleText: some View {
        Text("No surgery date defined")
            .font(.semibold)
            .foregroundColor(.grey)
    }

    var placeholderDescriptionText: some View {
        Text("Your surgery date is not yet scheduled. Please contact your clinician to add it, or update it yourself.")
            .frame(maxWidth: .infinity, alignment: .leading)
            .multilineTextAlignment(.leading)
            .font(.small)
            .foregroundColor(.grey)
    }

    var placeholderEditButton: some View {
        Button("Add date") {
            handleAction(.addDate)
        }
        .pillStyle(.medium)
    }
}

// MARK: - Preview

#Preview {
    /// Helper view with buttons to test different states of checklist header
    struct SurgeryChecklistHeader_PreviewContaner: View {
        @State var config: SurgeryChecklistHeaderView.Config
        @State var showsButtons: Bool
        let showsChevron: Bool
        let showsCount: Bool
        let showsAddDate: Bool

        init(
            title: String = "Total Knee Reconstruction",
            progress: Int = 9,
            maxCount: Int = 10,
            progressBar: Bool = true,
            showsCount: Bool = true,
            showsButtons: Bool = true,
            showsChevron: Bool = true,
            showsAddDate: Bool = true
        ) {
            self.config = .init(
                title: title,
                showProgressBar: progressBar,
                maxCount: maxCount,
                progress: progress
            )
            self.showsCount = showsCount
            self.showsButtons = showsButtons
            self.showsChevron = showsChevron
            self.showsAddDate = showsAddDate
        }

        var body: some View {
            VStack {
                SurgeryChecklistHeaderView(
                    config: config,
                    showsCount: showsCount,
                    showsChevron: showsChevron,
                    showsAddDate: showsAddDate
                )
                .padding()
                .background(.white)

                buttonsView
            }
            .padding(.horizontal)
        }

        var buttonsView: some View {
            HStack {
                Spacer()
                Group {
                    Text("PREVIEW:")
                        .bold()
                        .foregroundStyle(.purple)

                    Button("-") {
                        let newCount = max(config.progress - 1, .zero)
                        withAnimation { config.progress = newCount }
                    }
                    .bouncyStyle()
                    .font(.largeTitle)
                    .foregroundStyle(.purple)

                    Button("+") {
                        let newCount = min(config.progress + 1, config.maxCount)
                        withAnimation { config.progress = newCount }
                    }
                    .bouncyStyle()
                    .font(.largeTitle)
                    .foregroundStyle(.purple)
                }
                .visible(showsButtons)
            }
        }
    }

    return ZStack {
        Color.lightGrey3
            .ignoresSafeArea()
        ScrollView {
            VStack(spacing: 32) {
                SurgeryChecklistHeader_PreviewContaner()
                SurgeryChecklistHeader_PreviewContaner(
                    title: "Total Knee Reconstruction Surgery Long Title"
                )
                SurgeryChecklistHeader_PreviewContaner(
                    showsChevron: false
                )
                SurgeryChecklistHeader_PreviewContaner(
                    title: "Total Knee Reconstruction Surgery Long Title",
                    showsChevron: false
                )
                SurgeryChecklistHeader_PreviewContaner(
                    showsCount: false,
                    showsButtons: false
                )
                SurgeryChecklistHeader_PreviewContaner(
                    showsCount: false,
                    showsButtons: false
                )
            }
        }
    }
}
