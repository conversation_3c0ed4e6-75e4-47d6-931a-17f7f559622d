//
//  SurgeryChecklistFooterView.swift
//  HumaSurgeryChecklistWidget
//
//  Created by <PERSON> on 17.12.2024.
//

import SwiftUI

struct SurgeryChecklistFooterView: View {
    struct Config: Hashable {
        let title: AttributedString
        let dateText: String
    }

    // MARK: - Public Properties

    let config: Config
    let showDate: Bool

    // MARK: - Life Cycle

    var body: some View {
        HStack {
            VStack(alignment: .leading, spacing: 4) {
                titleText
                dateText
                    .visible(showDate)
            }
        }
    }
}

// MARK: - Private Helpers

private extension SurgeryChecklistFooterView {
    var calendarIcon: some View {
        Image("sc_calendar", bundle: Bundle(for: SurgeryChecklistBundle.self))
            .renderingMode(.template)
            .frame(square: 24)
            .foregroundColor(.grey)
            .padding(.trailing, 10)
    }

    var dateText: some View {
        Text("Date: \(config.dateText)")
            .font(.xSmall)
            .foregroundColor(.grey)
    }

    var titleText: some View {
        Text(config.title)
            .foregroundColor(.grey)
    }
}

// MARK: - Preview

#Preview {
    ZStack {
        Color.lightGrey3
            .ignoresSafeArea()
        ScrollView {
            VStack {
                SurgeryChecklistFooterView(
                    config: .init(
                        title: .init(stringLiteral: "Today is surgery date"),
                        dateText: "30-Dec-2024"
                    ),
                    showDate: true
                )
                .padding()
                .background(.white)
                .padding()

                SurgeryChecklistFooterView(
                    config: .init(
                        title: .init(stringLiteral: "Today is surgery date"),
                        dateText: "30-Dec-2024"
                    ),
                    showDate: false
                )
                .padding()
                .background(.white)
                .padding()
            }
        }
    }
}
