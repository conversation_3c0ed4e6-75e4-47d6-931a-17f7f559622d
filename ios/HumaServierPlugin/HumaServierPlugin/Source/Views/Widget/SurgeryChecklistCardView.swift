//
//  SurgeryChecklistCardView.swift
//  HumaSurgeryChecklistWidget
//
//  Created by <PERSON> on 19.11.2024.
//

import SwiftUI

struct SurgeryChecklistCardView<ViewModel: AnySurgeryChecklistViewModel>: View {
    @StateObject var viewModel: ViewModel

    // MARK: - Body

    var body: some View {
        ZStack(alignment: .center) {
            loadingView
                .transition(.opacity)
                .animation(.default, value: viewModel.content.isLoading)
                .visible(viewModel.content.isLoading)

            if let result = viewModel.content.result {
                contentView(response: result)
                    .transition(.opacity)
                    .animation(.default, value: viewModel.content.shouldShowResults)
                    .visible(viewModel.content.shouldShowResults)
            }
        }
        .padding(16)
        .enableCardStyling()
        .padding(.vertical, 8)
        .padding(.horizontal, 24)
        .if(viewModel.content.result?.surgeryDate != nil) {
            $0.button {
                viewModel.handleOnShowAll()
            }
            .minorBouncyStyle()
        }
        .task {
            await viewModel.loadData()
        }
    }
}

// MARK: - Private Helpers

private extension SurgeryChecklistCardView {

    func contentView(response: SurgeryChecklistModel) -> some View {
        VStack(spacing: 8) {
            headerView(response: response)

            if let config = viewModel.footerConfig(from: response) {
                footerDateView(config: config)
                    .padding(.top, 8)
            }
        }
    }

    func headerView(response: SurgeryChecklistModel) -> some View {
        SurgeryChecklistHeaderView(
            config: viewModel.headerConfig,
            showsCount: !response.surgeryDate.isNil,
            showsChevron: true,
            showsAddDate: viewModel.config.editDate
        )
        .on(.addDate) { viewModel.handleOnEditDate() }
    }

    func footerDateView(config: SurgeryChecklistFooterView.Config) -> some View {
        SurgeryChecklistFooterView(
            config: config,
            showDate: viewModel.config.showDate
        )
        .frame(maxWidth: .infinity, alignment: .leading)
    }

    var loadingView: some View {
        SurgeryChecklistLoader(config: viewModel.config)
    }
}

// MARK: - Preview

#Preview {
    ZStack {
        Color.lightGrey3
            .ignoresSafeArea()
        ScrollView {
            VStack(spacing: 8) {
                Text("No date")
                    .textCase(.uppercase)
                    .font(.default)
                    .frame(maxWidth: .infinity, alignment: .leading)
                    .padding(.horizontal, 24)

                SurgeryChecklistCardView(
                    viewModel: PreviewSurgeryChecklistViewModel.previewNoDate
                )
                SurgeryChecklistCardView(
                    viewModel: PreviewSurgeryChecklistViewModel.previewNoDate1
                )
                SurgeryChecklistCardView(
                    viewModel: PreviewSurgeryChecklistViewModel.previewNoDate2
                )
                Spacer(minLength: 20)

                Text("Future date")
                    .textCase(.uppercase)
                    .font(.default)
                    .frame(maxWidth: .infinity, alignment: .leading)
                    .padding(.horizontal, 24)

                SurgeryChecklistCardView(
                    viewModel: PreviewSurgeryChecklistViewModel.preview
                )
                SurgeryChecklistCardView(
                    viewModel: PreviewSurgeryChecklistViewModel.preview1
                )
                SurgeryChecklistCardView(
                    viewModel: PreviewSurgeryChecklistViewModel.preview2
                )
                SurgeryChecklistCardView(
                    viewModel: PreviewSurgeryChecklistViewModel.preview3
                )
                SurgeryChecklistCardView(
                    viewModel: PreviewSurgeryChecklistViewModel.preview4
                )
                Spacer(minLength: 20)

                Text("Today")
                    .textCase(.uppercase)
                    .font(.default)
                    .frame(maxWidth: .infinity, alignment: .leading)
                    .padding(.horizontal, 24)

                SurgeryChecklistCardView(
                    viewModel: PreviewSurgeryChecklistViewModel.previewToday
                )
                SurgeryChecklistCardView(
                    viewModel: PreviewSurgeryChecklistViewModel.previewToday1
                )
                SurgeryChecklistCardView(
                    viewModel: PreviewSurgeryChecklistViewModel.previewToday2
                )
                SurgeryChecklistCardView(
                    viewModel: PreviewSurgeryChecklistViewModel.previewToday3
                )
                SurgeryChecklistCardView(
                    viewModel: PreviewSurgeryChecklistViewModel.previewToday4
                )
                Spacer(minLength: 20)

                Text("Past Date")
                    .textCase(.uppercase)
                    .font(.default)
                    .frame(maxWidth: .infinity, alignment: .leading)
                    .padding(.horizontal, 24)

                SurgeryChecklistCardView(viewModel: PreviewSurgeryChecklistViewModel.previewPastDate
                )
                SurgeryChecklistCardView(viewModel: PreviewSurgeryChecklistViewModel.previewPastDate1
                )
                SurgeryChecklistCardView(viewModel: PreviewSurgeryChecklistViewModel.previewPastDate2
                )
                SurgeryChecklistCardView(viewModel: PreviewSurgeryChecklistViewModel.previewPastDate3
                )
                SurgeryChecklistCardView(viewModel: PreviewSurgeryChecklistViewModel.previewPastDate4
                )
                Spacer(minLength: 20)
            }
        }
    }
}
