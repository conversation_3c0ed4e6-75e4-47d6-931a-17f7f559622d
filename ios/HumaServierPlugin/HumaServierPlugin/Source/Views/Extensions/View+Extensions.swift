//
//  View+Extensions.swift
//  HumaSurgeryChecklistWidget
//
//  Created by <PERSON> on 10.11.2024.
//

import SwiftUI

// MARK: - Helpers

extension View {
    func disableAnimation() -> some View {
        animation(nil, value: UUID())
    }

    func updatedCopy(_ update: (inout Self) -> Void) -> Self {
        var copy = self
        update(&copy)
        return copy
    }

    func button(_ action: @escaping VoidCompletion) -> <PERSON><PERSON><Self> {
        But<PERSON>(action: action, label: { self })
    }

    func controller() -> UIHostingController<Self> {
        .init(rootView: self)
    }

    func expandTouchArea() -> some View {
        contentShape(Rectangle())
    }

    func hideKeyboard() {
        UIApplication.shared.hideKeyboard()
    }
}

extension UIApplication {
    func hideKeyboard() {
        sendAction(#selector(UIResponder.resignFirstResponder), to: nil, from: nil, for: nil)
    }
}

extension Text {
    @ViewBuilder
    func numericTextTransition() -> some View {
        if #available(iOS 16.0, *) {
            self.contentTransition(.numericText())
        } else {
            self
        }
    }

    @ViewBuilder
    func italicIfAvalible() -> some View {
        if #available(iOS 16.0, *) {
            self.italic()
        } else {
            self
        }
    }
}

extension ScrollView {
    @ViewBuilder
    func disableAcrollIndicators() -> some View {
        if #available(iOS 16.0, *) {
            self.scrollIndicators(.never)
        } else {
            self
        }
    }
}

extension NSAttributedString {
    var converted: AttributedString? { AttributedString(self) }
}

// MARK: - Shape / Size

enum CornerRadiusStyle {
    case value(CGFloat)
    case circle
    case capsule
}

struct CornerRadiusIndependent {
    let topLeft: CGFloat
    let topRight: CGFloat
    let bottomLeft: CGFloat
    let bottomRight: CGFloat

    func offset(by value: CGFloat) -> Self {
        .init(
            topLeft: topLeft + value,
            topRight: topRight + value,
            bottomLeft: bottomLeft + value,
            bottomRight: bottomRight + value
        )
    }
}

extension View {
    /// Clips self to shape `RoundedRectangle` or `Circle` according to given enum case.
    @ViewBuilder
    func cornerRadius(_ radius: CornerRadiusStyle) -> some View {
        switch radius {
        case .value(let value):
            clipShape(RoundedRectangle(cornerRadius: value, style: .continuous))
        case .circle:
            clipShape(Circle())
        case .capsule:
            clipShape(Capsule())
        }
    }

    /// Clips self to `RoundedCorner` with given radius with indipendent value for each corner
    func cornerRadius(_ radius: CornerRadiusIndependent) -> some View {
        cornerRadius(.topLeft, radius.topLeft)
            .cornerRadius(.topRight, radius.topRight)
            .cornerRadius(.bottomLeft, radius.bottomLeft)
            .cornerRadius(.bottomRight, radius.bottomRight)
    }

    /// Clips self to shape `RoundedRectangle` with given radius.
    func cornerRadius(_ radius: CGFloat) -> some View {
        clipShape(RoundedRectangle(cornerRadius: radius, style: .continuous))
    }

    /// Clips self to shape `RoundedCorner` with given radius.
    func cornerRadius(_ corners: UIRectCorner, _ radius: CGFloat) -> some View {
        clipShape(RoundedCorner(radius: radius, corners: corners) )
    }

    func clipAndStroke(cornerRadius: CGFloat, color: Color) -> some View {
        clipShape(RoundedRectangle(cornerRadius: cornerRadius))
        .overlay {
            RoundedRectangle(cornerRadius: cornerRadius, style: .continuous)
                .stroke(color)
        }
    }

    func frame(size: CGSize, alignment: Alignment = .center) -> some View {
        frame(width: size.width, height: size.height, alignment: alignment)
    }

    func frame(square size: CGFloat?, alignment: Alignment = .center) -> some View {
        frame(width: size, height: size, alignment: alignment)
    }

    func opacity(_ isVisible: Bool) -> some View {
        opacity(isVisible ? 1 : 0)
    }

    func scaleEffect(_ isVisible: Bool) -> some View {
        scaleEffect(isVisible ? 1 : 0.5)
    }

    func reverseMask<Mask: View>(alignment: Alignment = .center, @ViewBuilder _ mask: () -> Mask) -> some View {
        self.mask {
            Rectangle()
                .overlay(alignment: alignment) {
                    mask()
                        .blendMode(.destinationOut)
                }
        }
    }

    func upsideDown() -> some View {
        rotationEffect(.radians(.pi))
    }

    @ViewBuilder
    func aligned(to edge: Edge) -> some View {
        switch edge {
        case .top:
            VStack(spacing: .zero) {
                self
                Spacer(minLength: .zero)
            }
        case .bottom:
            VStack(spacing: .zero) {
                Spacer(minLength: .zero)
                self
            }
        case .leading:
            HStack(spacing: .zero) {
                self
                Spacer(minLength: .zero)
            }
        case .trailing:
            HStack(spacing: .zero) {
                Spacer(minLength: .zero)
                self
            }
        }
    }
}

struct RoundedCorner: Shape {
    var radius: CGFloat = .infinity
    var corners: UIRectCorner = .allCorners

    func path(in rect: CGRect) -> Path {
        let path = UIBezierPath(
            roundedRect: rect,
            byRoundingCorners: corners,
            cornerRadii: CGSize(width: radius, height: radius)
        )
        return Path(path.cgPath)
    }
}

// MARK: - Conditions

enum ViewState {
    case enabled
    case disabled
    case hidden

    var isHidden: Bool {
        switch self {
        case .hidden:
            true
        case .disabled, .enabled:
            false
        }
    }

    var isDisabled: Bool {
        switch self {
        case .disabled, .hidden:
            true
        case .enabled:
            false
        }
    }
}

extension View {
    /// Applies the given transform if the given condition evaluates to `true`.
    /// - Parameters:
    ///   - condition: The condition to evaluate.
    ///   - transform: The transform to apply to the source `View`.
    /// - Returns: Either the original `View` or the modified `View` if the condition is `true`.
    @ViewBuilder func `if`<Content: View>(_ condition: Bool, transform: (Self) -> Content) -> some View {
        if condition {
            transform(self)
        } else {
            self
        }
    }

    /// Applies the transformation depending on the value
    /// - Parameters:
    ///   - condition: The condition to evaluate.
    ///   - buildIf: The transform to apply to the source `View` if the condition is `true`.
    ///   - else: The transform to apply to the source `View` if the condition is `false`.
    /// - Returns: Modified `View`.
    @ViewBuilder func `if`<Content: View, EitherContent: View>(
        _ condition: Bool,
        buildIf: (Self) -> Content,
        else buildEither: (Self) -> EitherContent
    ) -> some View {
        if condition {
            buildIf(self)
        } else {
            buildEither(self)
        }
    }

    /// Applies the given transform if the given optional is not `nil`.
    /// - Parameters:
    ///   - condition: The condition to evaluate.
    ///   - transform: The transform to apply to the source `View`.
    /// - Returns: Either the original `View` or the modified `View` if the condition is `true`.
    @ViewBuilder func `if`<Content: View, Value>(_ value: Value?, transform: (Self, Value) -> Content) -> some View {
        if let value {
            transform(self, value)
        } else {
            self
        }
    }

    /// Applies the transformation depending on the value is `nil` or not.
    /// - Parameters:
    ///   - condition: The condition to evaluate.
    ///   - buildIf: The transform to apply to the source `View` if the condition is `true`.
    ///   - else: The transform to apply to the source `View` if the condition is `false`.
    /// - Returns: Modified `View`.
    @ViewBuilder func `if`<Content: View, EitherContent: View, Value>(
        _ value: Value?,
        buildIf: (Self, Value) -> Content,
        else buildEither: (Self) -> EitherContent) -> some View {
        if let value {
            buildIf(self, value)
        } else {
            buildEither(self)
        }
    }

    @ViewBuilder
    func hidden(_ isHidden: Bool) -> some View {
        if !isHidden { self }
    }

    @ViewBuilder
    func visible(_ isVisible: Bool) -> some View {
        if isVisible { self }
    }

    @ViewBuilder
    func updateState(_ state: ViewState) -> some View {
        hidden(state.isHidden).disabled(state.isDisabled)
    }

    func taskIf(
        _ condition: Bool,
        priority: TaskPriority = .userInitiated,
        _ action: @escaping @Sendable () async -> Void
    ) -> some View {
        self.if(condition) {
            $0.task(priority: priority, action)
        }
    }
}

// MARK: - Lazy List

extension Sequence {
    /// Transforms each element of the sequence into a SwiftUI view and presents them in a scrollable list.
    ///
    /// This method takes a transformation closure that you provide and applies it to each element in the sequence.
    /// The result is a vertical stack of the resulting views, which can be scrolled if the content exceeds the visible area.
    ///
    /// - Parameter transform: A closure that takes an element of the sequence as its argument
    ///   and returns a SwiftUI view. The closure is applied to each element in the sequence.
    ///
    /// - Returns: A scrollable SwiftUI view containing the views generated from each element of the sequence.
    ///
    /// Example usage:
    /// ```
    /// [1, 2, 3].list { item in
    ///     Text.Kern("Item \(item)")
    /// }
    /// ```
    /// In this example, a list of three text views will be created, each displaying "Item 1", "Item 2", and "Item 3".
    func list<Content: View>(transform: @escaping (Element) -> Content) -> some View {
        ScrollView {
            LazyVStack(spacing: .zero) {
                ForEach(Array(enumerated()), id: \.offset) { _, element in
                    transform(element)
                }
            }
        }
    }
}
