//
//  Int+Extensions.swift
//  HumaSurgeryChecklistWidget
//
//  Created by <PERSON> on 22.11.2024.
//

extension Int {
    /// Returns an array of integers centered around the current integer,
    /// wrapping around within the range of 0...9.
    ///
    /// - Parameter offset: The number of integers to include before and after the current integer.
    /// - Returns: An array of integers with `offset` preceding numbers, the current number,
    ///            and `offset` succeeding numbers, wrapping around as needed.
    ///
    /// # Example:
    /// ```
    /// 5.surroundingNumbers(offset: 2) // [3, 4, 5, 6, 7]
    /// 1.surroundingNumbers(offset: 2) // [9, 0, 1, 2, 3]
    /// ```
    func surroundingNumbers(offset: Int) -> [Int] {
        let range = -offset...offset
        return range.map { (self + $0 + 10) % 10 }
    }

    /// Returns the string representation of the integer with a minimum length of `minLength` characters.
    /// If the integer has fewer digits, it will be padded with leading zeros.
    ///
    /// - Parameter minLength: The minimum number of characters the string should have.
    /// - Returns: A string representation of the integer with at least `minLength` characters.
    ///
    /// # Example:
    /// ```
    /// 5.toPaddedString(minLength: 3) // "005"
    /// 123.toPaddedString(minLength: 3) // "123"
    /// ```
    func toPaddedString(minLength: Int) -> String {
        return String(format: "%0\(minLength)d", self)
    }
}
