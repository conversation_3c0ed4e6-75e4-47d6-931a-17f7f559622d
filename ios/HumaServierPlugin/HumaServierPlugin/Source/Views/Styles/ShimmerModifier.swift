//
//  ShimmerModifier.swift
//  HumaSurgeryChecklistWidget
//
//  Created by <PERSON> on 10.01.2025.
//

import SwiftUI

// MARK: - Style

struct ShimmerModifierStyle {
    struct ColorStyle {
        let edgeColor: Color
        let middleColor: Color
        let backgroundColor: Color
        let locations: [CGFloat]

        var gradientStops: [Gradient.Stop] {
            return [
                .init(color: edgeColor, location: 0.0),
                .init(color: middleColor, location: 0.4),
                .init(color: middleColor, location: 0.6),
                .init(color: edgeColor, location: 1.0),
            ]
        }
    }

    struct AnimationStyle {
        let duration: Double
        let repeatCount: Float
        let startPoint: CGPoint
        let endPoint: CGPoint
    }

    var color: ColorStyle
    var animation: AnimationStyle

    static var `default`: ShimmerModifierStyle {
        .init(
            color: .init(
                edgeColor: .clear,
                middleColor:  Color.black.opacity(0.1),
                backgroundColor: Color.veryLightGrey,
                locations: [0, 0.4, 0.6, 1.0]
            ),
            animation: .init(
                duration: 2.0,
                repeatCount: .infinity,
                startPoint: CGPoint(x: 0, y: 0),
                endPoint: CGPoint(x: 1, y: 1)
            )
        )
    }

    init(
        color: ColorStyle,
        animation: AnimationStyle
    ) {
        self.color = color
        self.animation = animation
    }
}

// MARK: - Modifier

struct ShimmerModifier: ViewModifier {
    @State private var phase: CGFloat = 0

    let style: ShimmerModifierStyle

    func body(content: Content) -> some View {
        content
            .foregroundColor(style.color.backgroundColor)
            .overlay(
                GeometryReader { proxy in
                    let size = proxy.size
                    LinearGradient(
                        gradient: Gradient(stops: style.color.gradientStops),
                        startPoint: UnitPoint(
                            x: style.animation.startPoint.x,
                            y: style.animation.startPoint.y
                        ),
                        endPoint: UnitPoint(
                            x: style.animation.endPoint.x,
                            y: style.animation.endPoint.y
                        )
                    )
                    .offset(
                        x: -2 * size.width + (4 * size.width * phase),
                        y: .zero
                    )
                    .blendMode(.plusLighter)
                    .mask(content)

                }
            )
            .onAppear {
                withAnimation(
                    .linear(duration: style.animation.duration)
                    .repeatForever(autoreverses: false)
                ) {
                    phase = 1
                }
            }
    }
}

// MARK: - Extension

extension View {
    func shimmer(style: ShimmerModifierStyle = .default) -> some View {
        modifier(ShimmerModifier(style: style))
    }
}
