//
//  CardStyleModifier.swift
//  HumaSurgeryChecklistWidget
//
//  Created by <PERSON> on 09.11.2024.
//

import SwiftUI

struct CardStyleModifier: ViewModifier {
    let style: TempStyler

    func body(content: Content) -> some View {
        let colorStyle = style.color.generalCard
        let appearanceStyle = style.appearance.generalCard

        content
            .background(colorStyle.background)
            .cornerRadius(appearanceStyle.cornerRadius)
            .overlay(
                RoundedRectangle(cornerRadius: appearanceStyle.cornerRadius)
                    .stroke(
                        colorStyle.borderColor,
                        lineWidth: appearanceStyle.borderWidth
                    )
            )
            .background {
                colorStyle.shadowColor
                    .cornerRadius(appearanceStyle.cornerRadius)
                    .offset(
                        x: appearanceStyle.shadowOffset.height,
                        y: appearanceStyle.shadowOffset.width
                    )
            }
    }
}

// MARK: - View Extension

extension View {
    func enableCardStyling(style: TempStyler = .default) -> some View {
        modifier(CardStyleModifier(style: style))
    }
}
