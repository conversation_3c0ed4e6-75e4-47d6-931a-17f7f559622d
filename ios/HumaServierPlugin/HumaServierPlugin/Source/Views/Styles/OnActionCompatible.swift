//
//  OnActionCompatible.swift
//  Pods
//
//  Created by <PERSON> on 16.12.2024.
//

import SwiftUI

// MARK: - OnActionCompatible

protocol OnActionCompatible: View {
    associatedtype Action: Hashable
    typealias ActionHandler = (Action) -> Void

    var handlers: [Action: ActionHandler] { get set }
}

// MARK: - Extensions

extension OnActionCompatible {
    func on(_ action: Action, handler: @escaping ActionHandler) -> Self {
        updatedCopy { $0.handlers[action] = handler }
    }

    func on(_ action: Action, handler: @escaping VoidCompletion) -> Self {
        updatedCopy { $0.handlers[action] = { _ in handler() }}
    }

    func handleAction(_ action: Action) {
        handlers[action]?(action)
    }
}
