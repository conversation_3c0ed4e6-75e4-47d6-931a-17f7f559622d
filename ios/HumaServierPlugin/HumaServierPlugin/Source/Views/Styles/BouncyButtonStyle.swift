//
//  BouncyButtonStyle.swift
//  HumaSurgeryChecklistWidget
//
//  Created by <PERSON> on 17.12.2024.
//

import SwiftUI

struct BouncyButtonStyle: ButtonStyle {
    enum Effect: CGFloat {
        case `default` = 0.95
        case minor = 0.99
    }
    let effect: Effect
    @Environment(\.isEnabled) var isEnabled

    func makeBody(configuration: Configuration) -> some View {
        let isPressed = configuration.isPressed

        configuration.label
            .scaleEffect(isPressed ? effect.rawValue : 1)
            .opacity(isEnabled ? 1 : 0.4)
    }
}

extension Button {
    func bouncyStyle() -> some View {
        buttonStyle(BouncyButtonStyle(effect: .default))
    }

    func minorBouncyStyle() -> some View {
        buttonStyle(BouncyButtonStyle(effect: .minor))
    }
}

// MARK: - Previews

#Preview {
    ZStack {
        Color.lightGrey3
            .ignoresSafeArea()

        But<PERSON>("Default <PERSON><PERSON>") {}
            .bouncyStyle()
    }
}
