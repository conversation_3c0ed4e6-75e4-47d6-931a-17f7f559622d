//
//  PillButtonStyle.swift
//  HumaSurgeryChecklistWidget
//
//  Created by <PERSON> on 09.11.2024.
//

import SwiftUI

struct PillButtonStyle: ButtonStyle {
    @Environment(\.isEnabled) var isEnabled
    @Binding var isLoading: Bool
    private let style: Style

    struct Style {
        struct Colors {
            let text: Color
            let background: Color
            let border: Color

            init(text: Color, background: Color, border: Color) {
                self.text = text
                self.background = background
                self.border = border
            }

            static func enabledColors(fill: Bool) -> Self {
                .init(
                    text: fill ? .white : .charcoalGrey,
                    background: fill ? .charcoalGrey : .white,
                    border:  .charcoalGrey
                )
            }

            static func disabledColors(fill: Bool) -> Self {
                .init(
                    text: fill ? .white : .lightGrey3,
                    background: fill ? .lightGrey3 : .white,
                    border: fill ? .lightGrey3 : .veryLightGrey
                )
            }
        }

        let padding: EdgeInsets
        let fontSize: CGFloat
        let fill: Bool
        let colors: Colors
        let disabledColors: Colors

        init(padding: EdgeInsets, fontSize: CGFloat, fill: Bool = false) {
            self.padding = padding
            self.fontSize = fontSize
            self.fill = fill
            self.colors = .enabledColors(fill: fill)
            self.disabledColors = .disabledColors(fill: fill)
        }

        static var small: Self {
            .init(
                padding: .symmetric(vertical: 4, horizontal: 16),
                fontSize: 12
            )
        }

        static var smallFill: Self {
            .init(
                padding: .symmetric(vertical: 4, horizontal: 16),
                fontSize: 12,
                fill: true
            )
        }

        static var medium: Self {
            .init(
                padding: .symmetric(vertical: 8, horizontal: 16),
                fontSize: 14
            )
        }

        static var mediumFill: Self {
            .init(
                padding: .symmetric(vertical: 8, horizontal: 16),
                fontSize: 14,
                fill: true
            )
        }
    }

    init(isLoading: Binding<Bool> = .constant(false), style: Style) {
        self._isLoading = isLoading
        self.style = style
    }

    func makeBody(configuration: Configuration) -> some View {
        let isPressed = configuration.isPressed

        configuration.label
            .font(.system(size: style.fontSize))
            .padding(style.padding)
            .clipShape(Capsule())
            .opacity(isLoading ? 0 : 1)
            .animation(.default, value: isLoading)
            .foregroundColor(foregroundColor)
            .if(!style.fill) {
                $0.overlay {
                    Capsule()
                        .stroke(style: .init())
                        .fill(borderColor)
                }
            }
            .if(style.fill) {
                $0.background {
                    Capsule()
                        .fill(backgroundColor(isPressed: isPressed))
                }
            }
            .overlay {
                progress
                    .padding(.oneValue(style.padding.top))
                    .opacity(isLoading ? 1 : 0)
                    .animation(.default, value: isLoading)
            }
            .scaleEffect(isPressed ? 0.95 : 1)
    }
}

// MARK: - Private Helpers

private extension PillButtonStyle {
    var progress: some View {
        // TODO: Use lottie animation as UIKit button
        ProgressView()
            .tint(style.colors.text)
            .layoutPriority(-1)
    }

    var foregroundColor: Color {
        isEnabled ? style.colors.text : style.disabledColors.text
    }

    var borderColor: Color {
        isEnabled ? style.colors.border : style.disabledColors.border
    }

    func backgroundColor(isPressed: Bool) -> Color {
        isEnabled ? style.colors.background : style.disabledColors.background
    }
}

extension Button {
    func pillStyle(
        isLoading: Binding<Bool> = .constant(false),
        style: PillButtonStyle.Style = .small
    ) -> some View {
        buttonStyle(PillButtonStyle(isLoading: isLoading, style: style))
    }

    func pillStyle(
        _ style: PillButtonStyle.Style = .small
    ) -> some View {
        buttonStyle(PillButtonStyle(style: style))
    }
}

// MARK: - Previews

#Preview {
    struct PillLoadingMock: View {
        @State var isLoading = false
        let style: PillButtonStyle.Style

        init(_ style: PillButtonStyle.Style = .small) {
            self.style = style
        }

        var body: some View {
            Button("Loading Edit") {
                isLoading = true
                executeOnMainThread(after: 3) { isLoading = false }
            }
            .pillStyle(isLoading: $isLoading, style: style)
        }
    }

    return ScrollView {
        VStack {
            PillLoadingMock()
            Button("Edit") {}
                .pillStyle()
            Button("Edit") {}
                .pillStyle()
                .disabled(true)

            Spacer()
                .frame(height: 40)

            PillLoadingMock(.medium)
            Button("Edit") {}
                .pillStyle(.medium)
            Button("Edit") {}
                .pillStyle(.medium)
                .disabled(true)

            Spacer()
                .frame(height: 40)

            PillLoadingMock(.smallFill)
            Button("Edit") {}
                .pillStyle(.smallFill)
            Button("Edit") {}
                .pillStyle(.smallFill)
                .disabled(true)

            Spacer()
                .frame(height: 40)

            PillLoadingMock(.mediumFill)
            Button("Edit") {}
                .pillStyle(.mediumFill)
            Button("Edit") {}
                .pillStyle(.mediumFill)
                .disabled(true)
        }
        .padding(40)
    }
}
