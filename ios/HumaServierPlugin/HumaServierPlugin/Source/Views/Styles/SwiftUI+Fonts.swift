//
//  SwiftUI+Fonts.swift
//  HumaSurgeryChecklistWidget
//
//  Created by <PERSON> on 01.12.2024.
//

import SwiftUI
import HumaFoundation

extension SwiftUI.Font {
    private static let common = CommonTypeStyle()

    /// VictorSerifSmooth-Regular 30
    static var header: Font { common.fontUI(styled: .header, sized: 30) }

    // MARK: - Regular

    /// NotoSans with `sized` parameter
    static func regular(sized: CGFloat) -> Font { common.fontUI(styled: .regular, sized: sized) }

    /// NotoSans 16
    static var `default`: Font { common.fontUI(styled: .regular, sized: .default) }

    /// NotoSans 14
    static var small: Font { common.fontUI(styled: .regular, sized: .small) }

    /// NotoSans 12
    static var xSmall: Font { common.fontUI(styled: .regular, sized: .xSmall) }

    /// NotoSans-Medium 16
    static var medium: Font { common.fontUI(styled: .medium, sized: .default) }

    /// NotoSans-Medium 12
    static var xSmallMedium: Font { common.fontUI(styled: .medium, sized: .xSmall) }

    /// NotoSans-Semibold 16
    static var semibold: Font { common.fontUI(styled: .semibold, sized: .default) }

    /// NotoSans-Bold 16
    static var bold: Font { common.fontUI(styled: .bold, sized: .default) }

    /// NotoSans-Bold 16
    static var xSmallBold: Font { common.fontUI(styled: .bold, sized: .xSmall) }
}
