//
//  CircleLoadingView.swift
//  HumaSurgeryChecklistWidget
//
//  Created by <PERSON> on 17.12.2024.
//

import SwiftUI

struct CircleLoadingView: View {
    let color: Color
    let strokeStyle: StrokeStyle?

    // MARK: - Private Properties

    private enum Constants {
        static let speed: CGFloat = 1.25
        static let lineWidth: CGFloat = 2
    }

    @State private var isLoading = false

    // MARK: - Life Cycle

    init(color: Color, strokeStyle: StrokeStyle? = nil) {
        self.color = color
        self.strokeStyle = strokeStyle
    }

    var body: some View {
        Circle()
            .trim(from: 0, to: 0.75)
            .stroke(color, style: strokeStyle ?? defaultStrokeStyle)
            .rotationEffect(.degrees(isLoading ? 360 : 0))
            .animation(animation, value: isLoading)
            .onAppear {
                isLoading = true
            }
    }
}

// MARK: - Private helpers

private extension CircleLoadingView {
    var defaultStrokeStyle: StrokeStyle {
        .init(lineWidth: Constants.lineWidth, lineCap: .round)
    }

    var animation: Animation {
        .linear(duration: 1 / Constants.speed)
        .repeatForever(autoreverses: false)
    }
}

// MARK: - Previews

#Preview {
    CircleLoadingView(color: .charcoalGrey)
        .frame(square: 20)
}
