//
//  CheckmarkView.swift
//  HumaSurgeryChecklistWidget
//
//  Created by <PERSON> on 13.11.2024.
//

import SwiftUI

struct CheckmarkView: View {
    let isChecked: Bool
    let isLoading: Bool

    // MARK: - Private Properties

    private enum Constants {
        static let loadingSpeed: CGFloat = 1.25
        static let circleLine: CGFloat = 1.6
        static let chekmarkLine: CGFloat = 1.8
        static let chekmarkPadding: CGFloat = 8
        static let transitionDuration: TimeInterval = 0.75
    }

    @State private var innerTrimEnd: CGFloat = 1
    @State private var hasAppeared = false

    // MARK: - Life Cycle

    init(isChecked: Bool, isLoading: Bool) {
        self.isChecked = isChecked
        self.isLoading = isLoading
    }

    var body: some View {
        ZStack {
            outerCircleBorder
            loadingView
            innerCircleFill
            checkmarkIcon
        }
        .onAppear { hasAppeared = true }
        .onChange(of: isChecked) { newValue in
            if hasAppeared { animateCheckmark(newValue) }
        }
    }
}

// MARK: - Private Helpers

private extension CheckmarkView {
    var outerCircleBorder: some View {
        Circle()
            .trim(from: 0, to: 1)
            .stroke(Color.charcoalGrey, style: circleStroke)
            .opacity(!isLoading)
            .animation(hasAppeared ? .default : .none, value: isLoading)
    }

    var loadingView: some View {
        CircleLoadingView(color: .charcoalGrey, strokeStyle: circleStroke)
            .opacity(isLoading)
            .animation(animationEffect, value: isLoading)
    }

    var innerCircleFill: some View {
        Circle()
            .foregroundColor(.charcoalGrey)
            .opacity(isChecked)
            .animation(animationEffect, value: isChecked)
    }

    var checkmarkIcon: some View {
        Checkmark()
            .trim(from: .zero, to: innerTrimEnd)
            .stroke(.white, style: checkmarkStroke)
            .padding(Constants.chekmarkPadding)
            .opacity(isChecked)
            .animation(animationEffect, value: isChecked)
    }
}

// MARK: - Styles

private extension CheckmarkView {
    var checkmarkStroke: StrokeStyle {
        StrokeStyle(lineWidth: Constants.chekmarkLine, lineCap: .round, lineJoin: .round)
    }

    var circleStroke: StrokeStyle {
        StrokeStyle(lineWidth: Constants.circleLine, lineCap: .round)
    }

    var animationEffect: Animation? {
        hasAppeared ? .default : .none
    }

    func animateCheckmark(_ isChecked: Bool) {
        innerTrimEnd = 0

        withAnimation(
            .linear(duration: 0.3 * Constants.transitionDuration)
            .delay(0.5 * Constants.transitionDuration)
        ) {
            innerTrimEnd = 1.0
        }
    }
}

// MARK: - Shape

private struct Checkmark: Shape {
    func path(in rect: CGRect) -> Path {
        let width = rect.width
        let height = rect.height
        var path = Path()
        path.move(to: CGPoint(x: 0.05 * width, y: 0.5 * height))
        path.addLine(to: CGPoint(x: 0.4 * width, y: 0.9 * height))
        path.addLine(to: CGPoint(x: width, y: 0.25 * height))
        return path
    }
}

// MARK: - Preview

#Preview {
    CheckmarkView_PreviewContaner()
}

private struct CheckmarkView_PreviewContaner: View {
    @State var isChecked = false
    @State var isLoading = false

    var body: some View {
        CheckmarkView(isChecked: isChecked, isLoading: isLoading)
            .frame(square: 28)
            .padding()
            .onTapGesture {
                isLoading = true
                executeOnMainThread(after: 2) {
                    isChecked.toggle()
                    isLoading = false
                }
            }
    }
}
