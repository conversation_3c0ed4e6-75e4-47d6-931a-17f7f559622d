//
//  SeparatedVStack.swift
//  HumaSurgeryChecklistWidget
//
//  Created by <PERSON> on 12.11.2024.
//

import SwiftUI

struct SeparatedVStack: View {
    private let views: [AnyView]

    init(@ViewArrayBuilder content: () -> [AnyView]) {
        self.views = content()
    }

    var body: some View {
        VStack {
            ForEach(views.indices, id: \.self) { index in
                views[index]
                if index < views.count - 1 {
                    Color.veryLightGrey.frame(height: 1)
                }
            }
        }
    }
}

@resultBuilder
struct ViewArrayBuilder {
    static func buildBlock(_ components: any View...) -> [AnyView] {
        components.map { AnyView($0) }
    }
}

#Preview {
    SeparatedVStack {
        Text("Item 1")
        Text("Item 2")
        Text("Item 3")
    }
    .padding()
}
