//
//  SurgeryChecklistRepository.swift
//  HumaSurgeryChecklistWidget
//
//  Created by <PERSON> on 26.11.2024.
//

import HumaFoundation

extension UIFont {
    static let commonStyle = CommonTypeStyle()

    static var `default`: UIFont { commonStyle.font(styled: .regular, sized: .default) }
    static var bold: UIFont { commonStyle.font(styled: .bold, sized: .default) }
}

/// Interface for repository which provides actions.
protocol AnySurgeryChecklistRepository: AnyObject {
    func getWidgetData(
        widgetType: String,
        widgetID: String,
        completion: @escaping RepositoryCompletion<SurgeryChecklistResponse>
    )
    func completeCheckmark(
       _ item: SurgeryChecklistResponse.Item,
       completion: @escaping RepositoryCompletion<Void>
    )
    func deleteCheckmark(
       _ item: SurgeryChecklistResponse.Item,
       completion: @escaping RepositoryCompletion<Void>
    )
    func updateDate(
       _ date: Date,
       completion: @escaping RepositoryCompletion<Void>
    )
}

extension AnySurgeryChecklistRepository {
    func getWidgetData(
        widgetType: String,
        widgetID: String
    ) async throws -> SurgeryChecklistResponse {
        try await withCheckedThrowingContinuation { continuation in
            getWidgetData(widgetType: widgetType, widgetID: widgetID) { result in
                continuation.resume(with: result)
            }
        }
    }

    func completeCheckmark(_ item: SurgeryChecklistResponse.Item) async throws {
        try await withCheckedThrowingContinuation { continuation in
            completeCheckmark(item) { result in
                continuation.resume(with: result)
            }
        }
    }

    func deleteCheckmark(_ item: SurgeryChecklistResponse.Item) async throws {
        try await withCheckedThrowingContinuation { continuation in
            deleteCheckmark(item) { result in
                continuation.resume(with: result)
            }
        }
    }

    func updateDate(_ date: Date) async throws {
        try await withCheckedThrowingContinuation { continuation in
            updateDate(date) { result in
                continuation.resume(with: result)
            }
        }
    }
}

/// Default implementation of `AnyActionsRepository`. Uses `AnyHumaNetworking` to make calls to API.
final class SurgeryChecklistRepository: AnySurgeryChecklistRepository {
    private let networking: AnyNetworking
    private let repository: AnySurgeryUserUpdateRepository

    public init(networking: AnyNetworking, repository: AnySurgeryUserUpdateRepository) {
        self.networking = networking
        self.repository = repository
    }
    
    public func getWidgetData(
        widgetType: String,
        widgetID: String,
        completion: @escaping RepositoryCompletion<SurgeryChecklistResponse>
    ) {
        networking.getSurgeryChecklist(widgetType: widgetType, widgetID: widgetID) { response in
            switch response.result {
            case .success(let result):
                completion(.success(result))
            case .failure(let error):
                completion(.failure(.fetchFailed(reason: error)))
            }
        }
    }
    
    func completeCheckmark(
        _ item: SurgeryChecklistResponse.Item,
        completion: @escaping RepositoryCompletion<Void>
    ) {
        networking.markCompleted(item) { response in
            switch response.result {
            case .success(let result):
                completion(.success(result))
            case .failure(let error):
                completion(.failure(.fetchFailed(reason: error)))
            }
        }
    }
    
    func deleteCheckmark(
        _ item: SurgeryChecklistResponse.Item,
        completion: @escaping RepositoryCompletion<Void>
    ) {
        networking.markUncompleted(item) { response in
            switch response.result {
            case .success(let result):
                completion(.success(result))
            case .failure(let error):
                completion(.failure(.fetchFailed(reason: error)))
            }
        }
    }

    func updateDate(
       _ date: Date,
       completion: @escaping RepositoryCompletion<Void>
    ) {
        repository.updateSurgeryDate(date, completion: completion)
    }
}
