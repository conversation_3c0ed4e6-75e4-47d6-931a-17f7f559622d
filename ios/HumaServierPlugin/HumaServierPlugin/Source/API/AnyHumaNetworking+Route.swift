//
//  AnyHumaNetworking+Route.swift
//  HumaSurgeryChecklistWidget
//
//  Created by <PERSON> on 26.11.2024.
//

import HumaFoundation

public typealias VoidCompletion = HumaFoundation.VoidCompletion

extension AnyNetworking {
    func getSurgeryChecklist(widgetType: String, widgetID: String, completion: @escaping RequestCompletion<SurgeryChecklistResponse>) {
        request(
            Endpoint.getWidgetData(widgetType: widgetType, widgetID: widgetID),
            completion: completion
        )
    }

    func markCompleted(_ item: SurgeryChecklistResponse.Item, completion: @escaping RequestCompletion<Void>) {
        request(
            Endpoint.markCompleted(id: item.id, payload: item.completePayload),
            completion: completion
        )
    }

    func markUncompleted(_ item: SurgeryChecklistResponse.Item, completion: @escaping RequestCompletion<Void>) {
        request(
            Endpoint.markUncompleted(id: item.id),
            completion: completion
        )
    }
}

extension Endpoint {
    static func getWidgetData(widgetType: String, widgetID: String) -> Endpoint {
        .init(
            path: "/api/sdk/v1/user/\(Endpoint.variableUserIDMacro)/widget/\(widgetType)/\(widgetID)",
            method: .get
        )
    }

    static func markCompleted(id: String, payload: SurgeryChecklistCompletePayload) -> Endpoint {
        .init(
            path: "/api/extensions/v1/user/\(Endpoint.variableUserIDMacro)/key-action/\(id)",
            method: .post,
            body: .json(payload, encoder: .huma)
        )
    }

    static func markUncompleted(id: String) -> Endpoint {
        .init(
            path: "/api/extensions/v1/user/\(Endpoint.variableUserIDMacro)/key-action/\(id)",
            method: .delete
        )
    }
}

// MARK: - Payload

struct SurgeryChecklistCompletePayload: Encodable, Equatable {
    let model: String
    let userId: String
    let startDateTime: Date
    let endDateTime: Date
    let completeDateTime: Date
}

extension SurgeryChecklistResponse.Item {
    var completePayload: SurgeryChecklistCompletePayload {
        .init(
            model: model,
            userId: userId,
            startDateTime: startDateTime,
            endDateTime: endDateTime,
            completeDateTime: Date()
        )
    }
}
