//
//  SurgeryChecklistViewModel+Preview.swift
//  HumaSurgeryChecklistWidget
//
//  Created by <PERSON> on 17.12.2024.
//

final class PreviewSurgeryChecklistViewModel: AnySurgeryChecklistViewModel {

    let model: SurgeryChecklistModel
    var config: SurgeryChecklistConfig
    var content: ContentState<SurgeryChecklistModel>

    init(config: SurgeryChecklistConfig, model: SurgeryChecklistModel) {
        self.model = model
        self.config = config
        self.content = .results(model)
    }

    func loadData() async {

    }

    func handleOnCheck(_ phase: SurgeryChecklistItemModel) {

    }

    func handleOnExpand(_ phase: SurgeryChecklistItemModel) {

    }

    func handleOnEditDate() {

    }

    func handleOnShowAll() {

    }

    func handleOnBack() {

    }
}

extension PreviewSurgeryChecklistViewModel {
    static var preview: PreviewSurgeryChecklistViewModel {
        .init(config: .preview0, model: .preview)
    }
    static var preview1: PreviewSurgeryChecklistViewModel {
        .init(config: .preview1, model: .preview)
    }
    static var preview2: PreviewSurgeryChecklistViewModel {
        .init(config: .preview2, model: .preview)
    }
    static var preview3: PreviewSurgeryChecklistViewModel {
        .init(config: .preview3, model: .preview)
    }
    static var preview4: PreviewSurgeryChecklistViewModel {
        .init(config: .preview4, model: .preview)
    }
    static var preview5: PreviewSurgeryChecklistViewModel {
        .init(config: .preview5, model: .preview)
    }
    static var preview6: PreviewSurgeryChecklistViewModel {
        .init(config: .preview6, model: .preview)
    }
    static var preview7: PreviewSurgeryChecklistViewModel {
        .init(config: .preview7, model: .preview)
    }

    static var previewToday: PreviewSurgeryChecklistViewModel {
        .init(config: .preview0, model: .previewToday)
    }
    static var previewToday1: PreviewSurgeryChecklistViewModel {
        .init(config: .preview1, model: .previewToday)
    }
    static var previewToday2: PreviewSurgeryChecklistViewModel {
        .init(config: .preview2, model: .previewToday)
    }
    static var previewToday3: PreviewSurgeryChecklistViewModel {
        .init(config: .preview3, model: .previewToday)
    }
    static var previewToday4: PreviewSurgeryChecklistViewModel {
        .init(config: .preview4, model: .previewToday)
    }

    static var previewPastDate: PreviewSurgeryChecklistViewModel {
        .init(config: .preview0, model: .previewPastDate)
    }
    static var previewPastDate1: PreviewSurgeryChecklistViewModel {
        .init(config: .preview1, model: .previewPastDate)
    }
    static var previewPastDate2: PreviewSurgeryChecklistViewModel {
        .init(config: .preview2, model: .previewPastDate)
    }
    static var previewPastDate3: PreviewSurgeryChecklistViewModel {
        .init(config: .preview3, model: .previewPastDate)
    }
    static var previewPastDate4: PreviewSurgeryChecklistViewModel {
        .init(config: .preview4, model: .previewPastDate)
    }

    static var previewNoDate: PreviewSurgeryChecklistViewModel {
        .init(config: .preview0, model: .previewNoDate)
    }
    static var previewNoDate1: PreviewSurgeryChecklistViewModel {
        .init(config: .preview1, model: .previewNoDate)
    }
    static var previewNoDate2: PreviewSurgeryChecklistViewModel {
        .init(config: .preview5, model: .previewNoDate)
    }
}
