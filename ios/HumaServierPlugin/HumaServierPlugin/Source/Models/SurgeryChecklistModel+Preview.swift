//
//  SurgeryChecklistModel+Preview.swift
//  HumaSurgeryChecklistWidget
//
//  Created by <PERSON> on 17.12.2024.
//

import Foundation

// MARK: - Config

extension SurgeryChecklistConfig {
    static var preview0: Self {
        .init(
            title: "Surgical checklist",
            description: "Put a description here to let user know more about screen purpose and put short instructions.",
            showProgress: true,
            showOffset: true,
            showDate: true,
            editDate: true,
            groups: [.previewPhase1, .previewPhase2]
        )
    }

    static var preview1: Self {
        .init(
            title: "Total knee replacement: comprehensive surgery preparation checklist",
            description: "Put a description here to let user know more about screen purpose and put short instructions.",
            showProgress: true,
            showOffset: true,
            showDate: true,
            editDate: true,
            groups: [.previewPhase1, .previewPhase2]
        )
    }

    static var preview2: Self {
        .init(
            title: "Surgical checklist",
            description: "Put a description here to let user know more about screen purpose and put short instructions.",
            showProgress: true,
            showOffset: true,
            showDate: false,
            editDate: true,
            groups: [.previewPhase1, .previewPhase2]
        )
    }

    static var preview3: Self {
        .init(
            title: "Surgical checklist",
            description: "Put a description here to let user know more about screen purpose and put short instructions.",
            showProgress: false,
            showOffset: true,
            showDate: true,
            editDate: true,
            groups: [.previewPhase1, .previewPhase2]
        )
    }

    static var preview4: Self {
        .init(
            title: "Surgical checklist",
            description: "Put a description here to let user know more about screen purpose and put short instructions.",
            showProgress: false,
            showOffset: true,
            showDate: false,
            editDate: true,
            groups: [.previewPhase1, .previewPhase2]
        )
    }

    static var preview5: Self {
        .init(
            title: "Surgical checklist",
            description: "Put a description here to let user know more about screen purpose and put short instructions.",
            showProgress: true,
            showOffset: true,
            showDate: true,
            editDate: false,
            groups: [.previewPhase1, .previewPhase2]
        )
    }

    static var preview6: Self {
        .init(
            title: "Surgical checklist",
            description: "Put a description here to let user know more about screen purpose and put short instructions.",
            showProgress: true,
            showOffset: false,
            showDate: true,
            editDate: true,
            groups: [.previewPhase1, .previewPhase2]
        )
    }

    static var preview7: Self {
        .init(
            title: "Surgical checklist",
            description: "Put a description here to let user know more about screen purpose and put short instructions.",
            showProgress: false,
            showOffset: false,
            showDate: false,
            editDate: false,
            groups: [.previewPhase1, .previewPhase2]
        )
    }
}

// MARK: - Model

extension SurgeryChecklistModel {
    static var preview: Self {
        .init(
            checklists: [.previewPhase1, .previewPhase2],
            surgeryDate: Date().addingDays(20)
        )
    }


    static var previewToday: Self {
        .init(
            checklists: [.previewPhase1, .previewPhase2],
            surgeryDate: Date()
        )
    }

    static var previewPastDate: Self {
        .init(
            checklists: [.previewPhase1, .previewPhase2],
            surgeryDate: Date().addingDays(-20)
        )
    }

    static var previewNoDate: Self {
        .init(
            checklists: [.previewPhase1, .previewPhase2],
            surgeryDate: nil
        )
    }
}

// MARK: - Cofig Phase

extension SurgeryChecklistConfig.Phase {
    static var previewPhase1: Self {
        .init(
            title: "Pre-surgery",
            offset: "2 weeks before",
            checklist: ["1", "2"]
        )
    }

    static var previewPhase2: Self {
        .init(
            title: "Post-surgery",
            offset: "1 day after",
            checklist: ["3", "4", "5"]
        )
    }
}

// MARK: - Phase

extension SurgeryChecklistPhaseModel {
    static var previewPhase1: Self {
        .init(
            title: "Pre-surgery",
            description: "2 weeks before",
            checklist: [.mockPhase1, .mockPhase2]
        )
    }

    static var previewPhase2: Self {
        .init(
            title: "Post-surgery",
            description: "1 day after",
            checklist: [.mockPhase3, .mockPhase4, .mockPhase5]
        )
    }
}

// MARK: - Item

extension SurgeryChecklistItemModel {
    static var mockPhase1: Self {
        .init(
            id: "1",
            title: "Do I have a ride home from the surgery & for follow up appointments as needed?",
            description: "Arrange transportation for your ride home and follow-up appointments, and ensure support during recovery.",
            isActive: true,
            isCompleted: true,
            isExpanded: false,
            isLoading: false
        )
    }

    static var mockPhase2: Self {
        .init(
            id: "2",
            title: "Do I have my insurance details & ID?",
            description: "Ensure you have your insurance details and ID ready before the surgery, as they may be needed for registration and billing.",
            isActive: true,
            isCompleted: false,
            isExpanded: false,
            isLoading: false
        )
    }

    static var mockPhase3: Self {
        .init(
            id: "3",
            title: "What care might I need after surgery? In-home care? Rehabilitative therapy.",
            description: "After surgery, you may need in-home care and rehabilitative therapy. Consult your provider for a personalized plan.",
            isActive: false,
            isCompleted: false,
            isExpanded: false,
            isLoading: false
        )
    }

    static var mockPhase4: Self {
        .init(
            id: "4",
            title: "How easy will it be for me to get to the kitchen?",
            description: "Find out how to keep essentials within easy reach and what adjustments might make meal prep easier.",
            isActive: false,
            isCompleted: false,
            isExpanded: false,
            isLoading: false
        )
    }

    static var mockPhase5: Self {
        .init(
            id: "5",
            title: "Will I need to arrange for childcare?",
            description: "Consider arranging childcare if you’ll need assistance with your children during your recovery. Plan ahead to ensure support.",
            isActive: false,
            isCompleted: false,
            isExpanded: false,
            isLoading: false
        )
    }
}

