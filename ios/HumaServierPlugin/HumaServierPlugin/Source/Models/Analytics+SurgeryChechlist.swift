//
//  Analytics+SurgeryChechlist.swift
//  HumaSurgeryChecklistWidget
//
//  Created by <PERSON> on 29.01.2025.
//  Copyright © 2025 Huma Therapeutics Ltd. All rights reserved.
//

import HumaFoundation

extension HumaAnalytics.Event {
    static var surgeryChecklistDateAdded: HumaAnalytics.Event {
        SurgeryChecklistEvent(name: .dateAdded)
    }

    static var surgeryChecklistDateUpdated: HumaAnalytics.Event {
        SurgeryChecklistEvent(name: .dateUpdate)
    }

    static var surgeryChecklistDetailsOpen: HumaAnalytics.Event {
        SurgeryChecklistEvent(name: .detailsOpen)
    }

    static func surgeryChecklistItemCompleted(_ id: String) -> HumaAnalytics.Event {
        SurgeryChecklistEvent(name: .completeItem, itemID: id)
    }

    static func surgeryChecklistItemIncompleted(_ id: String) -> HumaAnalytics.Event {
        SurgeryChecklistEvent(name: .incompleteItem, itemID: id)
    }

    static func surgeryChecklistItemExpanded(_ id: String) -> HumaAnalytics.Event {
        SurgeryChecklistEvent(name: .expandItem, itemID: id)
    }

    static func surgeryChecklistItemCollapsed(_ id: String) -> HumaAnalytics.Event {
        SurgeryChecklistEvent(name: .collapseItem, itemID: id)
    }
}

/// Implementation of `Analytics.KeyedEvent` which is used when opening Learn content.
private class SurgeryChecklistEvent: HumaAnalytics.KeyedEvent<SurgeryChecklistEvent.Keys> {
    enum Name: String {
        case dateAdded = "Surgery Date Added"
        case dateUpdate = "Surgery Date Updated"
        case detailsOpen = "Surgery Checklist Details Open"
        case completeItem = "Mark Surgery Checklist Item as Completed"
        case incompleteItem = "Mark Surgery Checklist Item as Incomplete"
        case collapseItem = "Surgery Checklist Item Expanded"
        case expandItem = "Surgery Checklist Item Collapsed"
    }

    enum Keys: String, CustomStringConvertible, CaseIterable {
        case itemID = "item_id"
    }

    init(name: Name, itemID: String? = nil) {
        super.init(name: name.rawValue, properties: [.itemID: itemID])
    }
}
