//
//  SurgeryChecklistConfig.swift
//  HumaSurgeryChecklistWidget
//
//  Created by <PERSON> on 26.11.2024.
//

import Foundation

struct SurgeryChecklistConfig: Codable, Hashable, Equatable {
    struct Phase: Codable, Hashable, Equatable {
        let title: String
        /// ISO duration formatted string, examples:
        /// `-P1M` translated into `1 month before`
        /// `P2D` translated into `2 days after`
        let offset: String
        let checklist: [String]
    }
    
    let title: String
    let description: String?
    let showProgress: Bool
    let showOffset: Bool
    let showDate: Bool
    let editDate: Bool
    let groups: [Phase]

    var totalCount: Int { groups.flatMap(\.checklist).count }
}
