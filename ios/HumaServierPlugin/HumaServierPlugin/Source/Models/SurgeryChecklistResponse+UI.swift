//
//  SurgeryChecklistResponse+UI.swift
//  HumaSurgeryChecklistWidget
//
//  Created by <PERSON> on 16.12.2024.
//

// MARK: - Conversion

private typealias PreviusStatusCheck = (String) -> Bool

extension SurgeryChecklistResponse {

    /// Previus model is passed to retain expanded state
    func uiModel(previus previusModel: SurgeryChecklistModel?) -> SurgeryChecklistModel {

        func previusStatus(_ transform: @escaping (SurgeryChecklistItemModel?) -> Bool) -> PreviusStatusCheck {
            { id in
                let source = previusModel?.checklists.flatMap(\.checklist) ?? []
                return transform(source.first(where: { $0.id == id }))
            }
        }
        let previusExpand = previusStatus { $0?.isExpanded ?? false }
        let previusLoading = previusStatus { $0?.isLoading ?? false }

        func isActive(_ id: String) -> Bool {
            checklists.first(where: { $0.checklist.contains { $0.id == id } })?.isActive ?? true
        }

        return .init(
            checklists: checklists.map {
                $0.uiModel(
                    isActive: $0.isActive,
                    isExpanded: previusExpand,
                    isLoading: previusLoading
                )
            },
            surgeryDate: surgeryDate
        )
    }
}

private extension SurgeryChecklistResponse.Phase {
    func uiModel(
        isActive: Bool,
        isExpanded: PreviusStatusCheck,
        isLoading: PreviusStatusCheck
    ) -> SurgeryChecklistPhaseModel {
         .init(
            title: title,
            description: subtitle,
            checklist: checklist.map {
                $0.uiModel(
                    isActive: isActive,
                    isExpanded: isExpanded,
                    isLoading: isLoading
                )
            }
        )
    }
}

private extension SurgeryChecklistResponse.Item {
    func uiModel(
        isActive: Bool,
        isExpanded: PreviusStatusCheck,
        isLoading: PreviusStatusCheck
    ) -> SurgeryChecklistItemModel {
        .init(
            id: constantID, /// Not actual `id` to have consistent animation. Regular `id` changes.
            title: title,
            description: description,
            isActive: isActive,
            isCompleted: completed,
            isExpanded: isExpanded(constantID),
            isLoading: isLoading(constantID)
        )
    }
}
