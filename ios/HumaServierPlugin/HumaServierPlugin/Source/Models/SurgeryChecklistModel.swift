//
//  SurgeryChecklistViewConfig.swift
//  HumaSurgeryChecklistWidget
//
//  Created by <PERSON> on 02.12.2024.
//

import Foundation
import UIKit

/// Model of surgery checklist used for UI.
struct SurgeryChecklistModel {
    var checklists: [SurgeryChecklistPhaseModel]
    var surgeryDate: Date?
}

extension SurgeryChecklistModel {
    var progress: Int {
        checklists.flatMap(\.checklist).filter(\.isCompleted).count
    }

    var totalCount: Int {
        checklists.flatMap(\.checklist).count
    }

    var datePlaceholder: Bool {
        surgeryDate.isNil
    }
}

/// Model of surgery checklist phase used for UI.
struct SurgeryChecklistPhaseModel: Identifiable, Equatable {
    let id: String
    let title: String
    let description: String?

    var checklist: [SurgeryChecklistItemModel]

    init(id: String? = nil, title: String, description: String?, checklist: [SurgeryChecklistItemModel]) {
        self.id = id ?? UUID().uuidString
        self.title = title
        self.description = description
        self.checklist = checklist
    }
}

/// Model of surgery checklist expandable  item used for UI.
struct SurgeryChecklistItemModel: Identifiable, Hashable, Equatable {
    let id: String
    let title: String
    let description: String?

    let isActive: Bool
    var isCompleted = true
    var isLoading = false
    var isExpanded = false

    init(
        id: String? = nil,
        title: String,
        description: String?,
        isActive: Bool,
        isCompleted: Bool,
        isExpanded: Bool,
        isLoading: Bool
    ) {
        self.id = id ?? UUID().uuidString
        self.title = title
        self.description = description
        self.isCompleted = isCompleted
        self.isActive = isActive
        self.isLoading = isExpanded
        self.isExpanded = isExpanded
    }
}
