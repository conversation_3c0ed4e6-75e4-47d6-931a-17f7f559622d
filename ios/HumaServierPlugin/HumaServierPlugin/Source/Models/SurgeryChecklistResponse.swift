//
//  SurgeryChecklistResponse.swift
//  HumaSurgeryChecklistWidget
//
//  Created by <PERSON> on 26.11.2024.
//

import HumaFoundation

struct SurgeryChecklistResponse: Codable, Equatable {
    struct Phase: Codable, Equatable {
        let title: String
        let subtitle: String?
        let checklist: [Item]
        let isActive: Bool

        init(from decoder: any Decoder) throws {
            let container = try decoder.container(keyedBy: SurgeryChecklistResponse.Phase.CodingKeys.self)
            let subtitle: String? = try container.decodeIfPresent(forKey: .subtitle)

            self.title = try container.decode(forKey: .title)
            self.subtitle = subtitle.nonEmptyOrNil
            self.checklist = try container.decode(forKey: .checklist)
            self.isActive = try container.decode(forKey: .isActive)
        }
    }

    struct Item: Codable, Identifiable, Equatable, Hashable {
        let id: String
        let title: String
        let description: String?
        let completeDateTime: Date?
        let parentID: String

        /// Next 4 fields are unused for UI, we need to parse them only for completing key action.
        let model: String
        let userId: String
        let startDateTime: Date
        let endDateTime: Date

        /// Calculated base on `completeDateTime` is `nil` or not.
        var completed: Bool { completeDateTime != nil }
        /// It is need only for consitent UI animations as regular `id` field changes after completion of item.
        var constantID: String { [title, parentID, startDateTime.iso8601].joined(separator: "-") }

        enum CodingKeys: String, CodingKey {
            case id
            case title
            case description
            case model
            case userId
            case startDateTime
            case endDateTime
            case completeDateTime
            case parentID = "parentId"
        }

        init(from decoder: any Decoder) throws {
            let container  = try decoder.container(keyedBy: CodingKeys.self)
            let description: String? = try container.decodeIfPresent(forKey: .description)

            self.id = try container.decode(forKey: .id)
            self.title = try container.decode(forKey: .title)
            self.description = description?.nonEmptyOrNil
            self.completeDateTime = try container.decodeIfPresent(forKey: .completeDateTime)
            self.model = try container.decode(forKey: .model)
            self.userId = try container.decode(forKey: .userId)
            self.startDateTime = try container.decode(forKey: .startDateTime)
            self.endDateTime = try container.decode(forKey: .endDateTime)
            self.parentID = try container.decode(forKey: .parentID)
        }
    }

    enum CodingKeys: String, CodingKey {
        case surgeryDate
        case checklists
    }

    let checklists: [Phase]
    let surgeryDate: Date?

    var progress: Int {
        checklists.flatMap(\.checklist).filter(\.completed).count
    }

    init(from decoder: any Decoder) throws {
        let container = try decoder.container(keyedBy: CodingKeys.self)
        let dateString = try? container.decodeIfPresent(String.self, forKey: .surgeryDate)

        self.surgeryDate = dateString.flatMap { DateFormatter.shortDate.date(from: $0) }
        self.checklists = try container.decode(forKey: .checklists)
    }

    func encode(to encoder: any Encoder) throws {
        var container = encoder.container(keyedBy: CodingKeys.self)
        let dateString = surgeryDate.map { DateFormatter.shortDate.string(from: $0) }

        try container.encodeIfPresent(dateString, forKey: .surgeryDate)
        try container.encode(checklists, forKey: .checklists)
    }
}

// MARK: - Extensions

private extension DateFormatter {
    static var shortDate: DateFormatter {
        let formatter = DateFormatter()
        formatter.calendar = Calendar(identifier: .gregorian)
        formatter.dateFormat = "yyyy-MM-dd"
        formatter.timeZone = TimeZone(secondsFromGMT: 0)
        return formatter
    }
}

