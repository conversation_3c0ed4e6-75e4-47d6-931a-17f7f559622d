//
//  HumaSurgeryChecklistWidget.swift
//  HumaSurgeryChecklistWidget
//
//  Created by <PERSON> on 22.11.2024.
//  Copyright © 2024 Huma Therapeutics Ltd. All rights reserved.
//

import UIKit
import SwiftUI
import HumaFoundation
import Huma<PERSON>idgetKit

typealias WidgetConfigInfo = WidgetConfig.Info

public final class SurgeryChecklistWidget: AnyWidget {

    // MARK: - Public Properties
    public static var discoveryData: WidgetDiscoveryData? {
        .idPrefix("com.huma.widget.surgery_checklist")
    }

    public let info: WidgetConfig.Info
    public var renderMode: WidgetRenderMode { .listHostingController(cardView) }

    // MARK: - Events

    public var onStartFlow: TriggeredEvent<AnyCoordinator>? = TriggeredEvent<AnyCoordinator>()
    @Triggered
    public var onUpdate: Event<Int>
    @Triggered
    public var onResize: Event<VoidCompletion>

    // MARK: - Private Properties

    private let resolver: Resolver
    private let navigator: AnyNavigator
    private var widgetConfig: SurgeryChecklistConfig

    private weak var viewModel: (any AnySurgeryChecklistViewModel)?
    private lazy var cardView = makeCardView()

    // MARK: - Init

    public init(context: WidgetInitContext) throws {
        self.widgetConfig = try context.config.decodeBody().orThrow()
        self.resolver = context.resolver
        self.info = context.config.info
        self.navigator = context.navigator
    }

    public func refreshWidget(force: Bool) {
        Task { await viewModel?.loadData() }
    }

    public func handleConfigDidChange(config: WidgetConfig) {
        guard let newConfig: SurgeryChecklistConfig = try? config.decodeBody(),
              newConfig != widgetConfig else {
            return
        }
        widgetConfig = newConfig
        cardView = makeCardView()
    }
}

private extension SurgeryChecklistWidget {
    func makeCardView() -> UIViewController {
        let model = SurgeryChecklistViewModel(
            info: info,
            config: widgetConfig,
            repository: SurgeryChecklistRepository(
                networking: resolver.resolve(),
                repository: resolver.resolve()
            ),
            onboardingManager: resolver.resolve(),
            analytics: resolver.resolve()
        )
        model.onChange.addObserver(self) {
            $0.$onResize.trigger({})
        }
        model.onShowAll.addObserver(self) {
            $0.handleShowAll($1)
        }
        model.onEditDate.addObserver(self) {
            $0.showDatePicker(previusDate: $1.previusDate, submitter: $1.submitter)
        }
        viewModel = model
        let view = SurgeryChecklistCardView(viewModel: model)
        let controller = UIHostingController(rootView: view)
        controller.view.backgroundColor = .clear
        controller.navigationItem.largeTitleDisplayMode = .never
        if #available(iOS 16.0, *) {
            controller.sizingOptions = [.intrinsicContentSize]
        }
        return controller
    }

    func handleShowAll(_ model: SurgeryChecklistModel) {
        let coordinator = SurgeryChecklistDetailCoordinator(
            info: info,
            widgetConfig: widgetConfig,
            initalModel: model,
            resolver: resolver,
            navigator: navigator
        )
        coordinator.onEditDate.addObserver(self) {
            $0.showDatePicker(previusDate: $1.previusDate, submitter: $1.submitter)
        }
        onStartFlow?.trigger(coordinator)
    }

    func showDatePicker(previusDate: Date?, submitter: AnyDatePickerSubmitter) {
        let controller = DatePickerSheetController(
            heading: "Select surgery date:",
            selectedDate: previusDate,
            minimumDate: Date().addingYears(-1),
            maximumDate: Date().addingYears(2)
        )
        controller.modalPresentationStyle = .overFullScreen
        controller.submitter = submitter
        navigator.showModal(controller, animated: false)
    }
}

// MARK: - Detail Coorinator

final class SurgeryChecklistDetailCoordinator: BaseCoordinator {

    let onEditDate = TriggeredEvent<SurgeryChecklistViewModel.DateUpdateHandler>()

    // MARK: - Private Properties

    private let info: WidgetConfig.Info
    private let widgetConfig: SurgeryChecklistConfig
    private let initalModel: SurgeryChecklistModel
    private let resolver: Resolver

    // MARK: - Life Cycle

    /// Initialization.
    /// - Parameters:
    ///    - navigator: Controls navigation between view controllers.
    ///    - resolver: Contains all needed dependencies.
    init(
        info: WidgetConfig.Info,
        widgetConfig: SurgeryChecklistConfig,
        initalModel: SurgeryChecklistModel,
        resolver: Resolver,
        navigator: AnyNavigator
    ) {
        self.info = info
        self.widgetConfig = widgetConfig
        self.initalModel = initalModel
        self.resolver = resolver
        super.init(navigator: navigator)
    }

    // MARK: - Public Methods

    /// Starts a coordinator.
    /// - Parameters:
    ///    - animated: Whether the coordinator should be started with animation.
    /// - Returns: Completable object that lifecycle connected to the coordinator.
    override func start(animated: Bool) -> Completable? {
        let model = SurgeryChecklistViewModel(
            info: info,
            config: widgetConfig,
            content: .results(initalModel),
            repository: SurgeryChecklistRepository(
                networking: resolver.resolve(),
                repository: resolver.resolve()
            ),
            onboardingManager: resolver.resolve(),
            analytics: resolver.resolve()
        )
        model.onGoBack.addObserver(self) {
            $0.navigator.pop()
        }
        onEditDate.subscribe(to: model.onEditDate)

        let view = SurgeryChecklistDetailView(viewModel: model)
        let controller = UIHostingController(rootView: view)
        controller.hidesBottomBarWhenPushed = true
        navigator.push(controller)
        return controller
    }
}
