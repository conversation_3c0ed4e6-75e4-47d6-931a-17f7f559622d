from datetime import datetime, timedelta, timezone
from pathlib import Path
from unittest.mock import patch

from flask import url_for
from freezegun import freeze_time
from parameterized import parameterized

from huma_plugins.components.billing.component import BillingComponent
from huma_plugins.components.billing.dtos.billing_models import BillingSubmissionDTO
from huma_plugins.components.billing.dtos.billing_submission_schedule_event import (
    BillingSubmissionScheduleEvent,
)
from huma_plugins.components.billing.helpers.module_result_helpers import PrimitiveSources
from huma_plugins.components.billing.models import BillingSubmission, BillingAlert
from huma_plugins.components.billing.repository.billing_repository import (
    BillingProfileHistoryLogRepository,
)
from huma_plugins.components.billing.router.billing_response_objects import (
    CPTCode,
    CPTRecordStatusType,
    CPTReportResponseObject,
)
from huma_plugins.components.billing.tests.IntegrationTests.repository.mongodb.mongo_billing_submission_repository_tests import (
    BaseBillingSubmissionRepoTest,
)
from huma_plugins.components.billing_export.component import BillingExportComponent
from huma_plugins.components.export.dtos.export_models import ExportParameters
from huma_plugins.components.export.use_case.export_request_objects import (
    ExportRequestObject,
)
from huma_plugins.components.extended_authorization.component import (
    ExtendedAuthorizationComponent,
)
from huma_plugins.components.extended_module_result.component import (
    ExtendedModuleResultComponent,
)
from huma_plugins.components.online_offline_call.component import (
    OnlineOfflineCallComponent,
)
from huma_plugins.tests.plugin_test_case import PluginsTestCase
from huma_plugins.tests.shared.common import sample_high_frequency_heart_rate
from huma_plugins.tests.test_helpers import set_submission_calculation_type
from sdk.auth.component import AuthComponent
from sdk.authorization.models import User
from sdk.authorization.models.user_move_history import UserMoveHistory
from sdk.authorization.router.user_profile_request import MoveUserDetails
from sdk.calendar.component import CalendarComponent
from sdk.calendar.dtos.calendar_event import CalendarEventDTO
from sdk.calendar.tasks import prepare_events_and_execute, prepare_events_for_next_day
from sdk.celery.app import celery_app
from sdk.common.utils import inject
from sdk.common.utils.common_functions_utils import find
from sdk.common.utils.validators import utc_str_field_to_val, model_to_dict
from sdk.deployment.component import DeploymentComponent
from sdk.deployment.models.deployment import Deployment
from sdk.deployment.router.deployment_requests import AddUserNotesV2RequestObject
from sdk.module_result.component import ModuleResultComponent
from sdk.module_result.dtos.primitives import PrimitiveDTO
from sdk.module_result.modules import HighFrequencyHeartRateModule, WeightModule, RespiratoryRateModule
from sdk.module_result.tests.IntegrationTests.test_samples import sample_weight, sample_respiratory_rate
from sdk.organization.component import OrganizationComponent
from sdk.storage.component import StorageComponentV1
from sdk.versioning.component import VersionComponent

VALID_CLINICIAN_ID = "64e76adedf047d493ba356c3"
VALID_DEPLOYMENT_ID = "5d386cc6ff885918d96edb2c"
RTM_DEPLOYMENT_ID = "5d386cc6ff885918d96edb2b"
USER_ID = "5e8f0c74b50aa9656c34789b"
RTM_USER_ID = "5e8f0c74b50aa9656c347891"

STATUS = CPTReportResponseObject.STATUS


class BillingTestCase(PluginsTestCase):
    components = [
        AuthComponent(),
        ExtendedAuthorizationComponent(),
        BillingComponent(),
        CalendarComponent(),
        DeploymentComponent(),
        OrganizationComponent(),
        OnlineOfflineCallComponent(),
        ModuleResultComponent(
            additional_modules=[WeightModule(), HighFrequencyHeartRateModule(), RespiratoryRateModule()]
        ),
        VersionComponent(server_version="1.0.0", api_version="1.0.0"),
    ]
    fixtures = [
        Path(__file__).parent.joinpath("fixtures/billing_dump.json"),
    ]


class BillingSubmissionTestCase(BillingTestCase, BaseBillingSubmissionRepoTest):
    def setUp(self):
        super(BillingTestCase, self).setUp()
        super(BaseBillingSubmissionRepoTest, self).setUp()
        self.cpt_mapping_number = 2
        self.base_url = f"/api/extensions/v1/billing/user/{USER_ID}/report"
        self.collection = "billingremotesubmission"

    @parameterized.expand(
        [
            (
                "2023-01-25T10:00:00.000Z",
                CPTRecordStatusType.INCOMPLETE,
                CPTRecordStatusType.INCOMPLETE,
            ),
            (
                "2023-03-25T10:00:00.000Z",
                CPTRecordStatusType.INCOMPLETE,
                CPTRecordStatusType.INCOMPLETE,
            ),
        ]
    )
    def test_submission_report_calendar_calculation_incomplete_periods(
        self, request_time, mapping_1_status, mapping_2_status
    ):
        self._set_calendar_calculation_type()
        self.create_record(start_datetime=datetime(2023, 1, 3))

        with freeze_time(request_time):
            headers = self.get_headers_for_token(VALID_CLINICIAN_ID)
            rsp = self.flask_client.get(self.base_url, headers=headers)

        self.assertEqual(200, rsp.status_code)
        self.assertEqual(CPTCode.RPM.CPT_MAPPING_1.value, rsp.json[0].get("cptCode"))
        self.assertEqual(mapping_1_status, rsp.json[0].get(STATUS))
        self.assertEqual(CPTCode.RPM.CPT_MAPPING_2.value, rsp.json[1].get("cptCode"))
        self.assertEqual(mapping_2_status, rsp.json[1].get(STATUS))

    @parameterized.expand(
        [
            (
                "2023-01-25T10:00:00.000Z",
                CPTRecordStatusType.COMPLETED,
                CPTRecordStatusType.COMPLETED,
            ),
            (
                "2023-02-25T10:00:00.000Z",
                CPTRecordStatusType.COMPLETED,
                CPTRecordStatusType.COMPLETED,
            ),
        ]
    )
    def test_submission_report_calendar_calculation_completed_periods(
        self, request_time, mapping_1_status, mapping_2_status
    ):
        self._set_calendar_calculation_type()

        with freeze_time(request_time):
            for i in range(16):
                frmt = "%Y-%m-%dT%H:%M:%S.000Z"
                dt = datetime.strptime(request_time, frmt) - timedelta(days=24 - i)
                self.create_record(start_datetime=dt)

            headers = self.get_headers_for_token(VALID_CLINICIAN_ID)
            rsp = self.flask_client.get(self.base_url, headers=headers)

        self.assertEqual(200, rsp.status_code)
        self.assertEqual(CPTCode.RPM.CPT_MAPPING_1.value, rsp.json[0].get("cptCode"))
        self.assertEqual(mapping_1_status, rsp.json[0].get(STATUS))
        self.assertEqual(CPTCode.RPM.CPT_MAPPING_2.value, rsp.json[1].get("cptCode"))
        self.assertEqual(mapping_2_status, rsp.json[1].get(STATUS))

    @freeze_time("2023-01-05T10:00:00.000Z")
    def test_submission_report_calendar_calculation__period_just_started(self):
        self._set_calendar_calculation_type()
        self.create_record(start_datetime=datetime(2023, 1, 3))

        headers = self.get_headers_for_token(VALID_CLINICIAN_ID)
        rsp = self.flask_client.get(self.base_url, headers=headers)

        self.assertEqual(200, rsp.status_code)
        self.assertEqual(CPTCode.RPM.CPT_MAPPING_1.value, rsp.json[0].get("cptCode"))
        self.assertEqual(CPTRecordStatusType.IN_PROGRESS, rsp.json[0].get(STATUS))
        self.assertEqual(CPTCode.RPM.CPT_MAPPING_2.value, rsp.json[1].get("cptCode"))
        self.assertEqual(CPTRecordStatusType.IN_PROGRESS, rsp.json[1].get(STATUS))

    @freeze_time("2022-12-31T10:00:00.000Z")
    def test_submission_report_calendar_calculation__december_with_next_common_year(
        self,
    ):
        self._set_calendar_calculation_type()
        self.create_record(start_datetime=datetime(2022, 12, 15))

        headers = self.get_headers_for_token(VALID_CLINICIAN_ID)
        rsp = self.flask_client.get(self.base_url, headers=headers)

        self.assertEqual(200, rsp.status_code)
        self.assertEqual(CPTCode.RPM.CPT_MAPPING_1.value, rsp.json[0].get("cptCode"))
        self.assertEqual(CPTRecordStatusType.PENDING, rsp.json[0].get(STATUS))
        self.assertEqual(0, rsp.json[0].get("daysHaveSubmission"))
        self.assertEqual(CPTCode.RPM.CPT_MAPPING_2.value, rsp.json[1].get("cptCode"))
        self.assertEqual(CPTRecordStatusType.PENDING, rsp.json[1].get(STATUS))
        self.assertEqual(0, rsp.json[1].get("daysHaveSubmission"))

    @freeze_time("2023-12-31T10:00:00.000Z")
    def test_submission_report_calendar_calculation__december_with_next_leap_year(self):
        self._set_calendar_calculation_type()
        self.create_record(start_datetime=datetime(2023, 12, 15))

        headers = self.get_headers_for_token(VALID_CLINICIAN_ID)
        rsp = self.flask_client.get(self.base_url, headers=headers)

        self.assertEqual(200, rsp.status_code)
        self.assertEqual(CPTCode.RPM.CPT_MAPPING_1.value, rsp.json[0].get("cptCode"))
        self.assertEqual(CPTRecordStatusType.INCOMPLETE, rsp.json[0].get(STATUS))
        self.assertEqual(1, rsp.json[0].get("daysHaveSubmission"))
        self.assertEqual(CPTCode.RPM.CPT_MAPPING_2.value, rsp.json[1].get("cptCode"))
        self.assertEqual(CPTRecordStatusType.INCOMPLETE, rsp.json[1].get(STATUS))
        self.assertEqual(1, rsp.json[1].get("daysHaveSubmission"))

    @freeze_time("2023-01-29T10:00:00.000Z")
    def test_submission_report_calendar_calculation__january_common_year(self):
        self._set_calendar_calculation_type()
        # 1 submission on the 31st of December
        with freeze_time("2022-12-31T10:00:00.000Z"):
            self.create_record(start_datetime=datetime(2022, 12, 31))

        # 15 submissions in January
        with freeze_time("2023-01-29T10:00:00.000Z"):
            for d in range(1, 16):
                self.create_record(start_datetime=datetime(2023, 1, d))

        headers = self.get_headers_for_token(VALID_CLINICIAN_ID)
        rsp = self.flask_client.get(self.base_url, headers=headers)

        self.assertEqual(200, rsp.status_code)
        self.assertEqual(CPTCode.RPM.CPT_MAPPING_1.value, rsp.json[0].get("cptCode"))
        self.assertEqual(CPTRecordStatusType.COMPLETED, rsp.json[0].get(STATUS))
        self.assertEqual(16, rsp.json[0].get("daysHaveSubmission"))
        self.assertEqual(CPTCode.RPM.CPT_MAPPING_2.value, rsp.json[1].get("cptCode"))
        self.assertEqual(CPTRecordStatusType.COMPLETED, rsp.json[1].get(STATUS))
        self.assertEqual(16, rsp.json[1].get("daysHaveSubmission"))

    @freeze_time("2024-01-29T10:00:00.000Z")
    def test_submission_report_calendar_calculation__january_leap_year(self):
        self._set_calendar_calculation_type()
        # 1 submission on the 31st of December
        with freeze_time("2023-12-31T10:00:00.000Z"):
            self.create_record(start_datetime=datetime(2023, 12, 31))

        # 15 submissions in January
        for d in range(1, 16):
            self.create_record(start_datetime=datetime(2024, 1, d))

        headers = self.get_headers_for_token(VALID_CLINICIAN_ID)
        rsp = self.flask_client.get(self.base_url, headers=headers)

        self.assertEqual(200, rsp.status_code)
        self.assertEqual(CPTCode.RPM.CPT_MAPPING_1.value, rsp.json[0].get("cptCode"))
        self.assertEqual(CPTRecordStatusType.IN_PROGRESS, rsp.json[0].get(STATUS))
        self.assertEqual(15, rsp.json[0].get("daysHaveSubmission"))
        self.assertEqual(CPTCode.RPM.CPT_MAPPING_2.value, rsp.json[1].get("cptCode"))
        self.assertEqual(CPTRecordStatusType.IN_PROGRESS, rsp.json[1].get(STATUS))
        self.assertEqual(15, rsp.json[1].get("daysHaveSubmission"))

    @freeze_time("2023-02-28T10:00:00.000Z")
    def test_submission_report_calendar_calculation__february(self):
        self._set_calendar_calculation_type()
        # 1 submission on the 30th of January
        with freeze_time("2023-01-30T10:00:00.000Z"):
            self.create_record(start_datetime=datetime(2023, 1, 30))

        # 1 submission on the 31st of January
        with freeze_time("2023-01-31T10:00:00.000Z"):
            self.create_record(start_datetime=datetime(2023, 1, 31))

        # 14 submissions in February
        for d in range(1, 15):
            self.create_record(start_datetime=datetime(2023, 2, d))

        headers = self.get_headers_for_token(VALID_CLINICIAN_ID)
        rsp = self.flask_client.get(self.base_url, headers=headers)

        self.assertEqual(200, rsp.status_code)
        self.assertEqual(CPTCode.RPM.CPT_MAPPING_1.value, rsp.json[0].get("cptCode"))
        self.assertEqual(CPTRecordStatusType.COMPLETED, rsp.json[0].get(STATUS))
        self.assertEqual(16, rsp.json[0].get("daysHaveSubmission"))
        self.assertEqual(CPTCode.RPM.CPT_MAPPING_2.value, rsp.json[1].get("cptCode"))
        self.assertEqual(CPTRecordStatusType.COMPLETED, rsp.json[1].get(STATUS))
        self.assertEqual(16, rsp.json[1].get("daysHaveSubmission"))

    @freeze_time("2023-01-24T10:00:00.000Z")
    def test_submission_report_calendar_calculation__period_midway(self):
        self._set_calendar_calculation_type()
        for i in range(10):
            dt = datetime(2023, 1, 3) + timedelta(days=i)
            self.create_record(start_datetime=dt)

        headers = self.get_headers_for_token(VALID_CLINICIAN_ID)
        rsp = self.flask_client.get(self.base_url, headers=headers)

        self.assertEqual(200, rsp.status_code)
        self.assertEqual(CPTCode.RPM.CPT_MAPPING_1.value, rsp.json[0].get("cptCode"))
        self.assertEqual(CPTRecordStatusType.IN_PROGRESS, rsp.json[0].get(STATUS))
        self.assertEqual(CPTCode.RPM.CPT_MAPPING_2.value, rsp.json[1].get("cptCode"))
        self.assertEqual(CPTRecordStatusType.IN_PROGRESS, rsp.json[1].get(STATUS))

    @freeze_time("2023-02-25T10:00:00.000Z")
    def test_submission_report_calendar_calculation__ZERO_submissions(self):
        self._set_calendar_calculation_type()
        headers = self.get_headers_for_token(VALID_CLINICIAN_ID)
        rsp = self.flask_client.get(self.base_url, headers=headers)

        self.assertEqual(200, rsp.status_code)
        self.assertEqual(CPTCode.RPM.CPT_MAPPING_1.value, rsp.json[0].get("cptCode"))
        self.assertEqual(CPTCode.RPM.CPT_MAPPING_2.value, rsp.json[1].get("cptCode"))
        # 0 submissions done, 5 days left -> INCOMPLETE
        self.assertEqual(CPTRecordStatusType.INCOMPLETE, rsp.json[0].get(STATUS))
        self.assertEqual(CPTRecordStatusType.INCOMPLETE, rsp.json[1].get(STATUS))

    @freeze_time("2023-03-02T10:00:00.000Z")
    def test_submission_report_calendar_calculation__fresh_period_after_idle(self):
        self._set_calendar_calculation_type()
        self.create_record(start_datetime=datetime(2023, 1, 3))

        headers = self.get_headers_for_token(VALID_CLINICIAN_ID)
        rsp = self.flask_client.get(self.base_url, headers=headers)

        self.assertEqual(200, rsp.status_code)
        self.assertEqual(CPTCode.RPM.CPT_MAPPING_1.value, rsp.json[0].get("cptCode"))
        self.assertEqual(CPTRecordStatusType.PENDING, rsp.json[0].get(STATUS))
        self.assertEqual(CPTCode.RPM.CPT_MAPPING_2.value, rsp.json[1].get("cptCode"))
        self.assertEqual(CPTRecordStatusType.PENDING, rsp.json[1].get(STATUS))

    @freeze_time("2023-03-25T10:00:00.000Z")
    def test_submission_report_calendar_calculation__billable_idle_billable(self):
        self._set_calendar_calculation_type()
        for i in range(16):
            self.create_record(start_datetime=datetime(2023, 1, 3) + timedelta(days=i))

        # February is IDLE
        for i in range(16):
            self.create_record(start_datetime=datetime(2023, 3, 2) + timedelta(days=i))

        headers = self.get_headers_for_token(VALID_CLINICIAN_ID)
        rsp = self.flask_client.get(self.base_url, headers=headers)

        self.assertEqual(200, rsp.status_code)
        # 99453/75 is missing after First Completed Period
        self.assertEqual(CPTCode.RPM.CPT_MAPPING_2.value, rsp.json[0].get("cptCode"))
        self.assertEqual(CPTRecordStatusType.COMPLETED, rsp.json[0].get(STATUS))

    @freeze_time("2021-01-05T10:00:00.000Z")
    def test_success_get_submission_report(self):
        self.create_record(start_datetime=datetime.now(timezone.utc) - timedelta(days=10))

        rsp = self.flask_client.get(
            self.base_url,
            headers=self.get_headers_for_token(VALID_CLINICIAN_ID),
        )

        self.assertEqual(200, rsp.status_code)
        self.assertEqual(CPTCode.RPM.CPT_MAPPING_1.value, rsp.json[0].get("cptCode"))
        self.assertEqual(
            CPTRecordStatusType.IN_PROGRESS,
            rsp.json[0].get(CPTReportResponseObject.STATUS),
        )

    @freeze_time("2021-01-05T10:00:00.000Z")
    def test_report_WHEN_user_has_NON_billable_periods_previously(self):
        self.create_record(start_datetime=datetime.now(timezone.utc) - timedelta(days=40))

        rsp = self.flask_client.get(
            self.base_url,
            headers=self.get_headers_for_token(VALID_CLINICIAN_ID),
        )

        self.assertEqual(200, rsp.status_code)
        self.assertEqual(CPTCode.RPM.CPT_MAPPING_1.value, rsp.json[0].get("cptCode"))
        self.assertEqual(CPTCode.RPM.CPT_MAPPING_2.value, rsp.json[1].get("cptCode"))
        self.assertEqual(
            CPTRecordStatusType.PENDING,
            rsp.json[0].get(CPTReportResponseObject.STATUS),
            rsp.json[0].get(CPTReportResponseObject.STATUS),
        )

    @freeze_time("2021-01-05T10:00:00.000Z")
    def test_report_WHEN_user_has_billable_periods_previously(self):
        for i in range(16):
            self.create_record(start_datetime=datetime.now(timezone.utc) - timedelta(days=40 - i))

        rsp = self.flask_client.get(
            self.base_url,
            headers=self.get_headers_for_token(VALID_CLINICIAN_ID),
        )

        self.assertEqual(200, rsp.status_code)
        self.assertEqual(CPTCode.RPM.CPT_MAPPING_2.value, rsp.json[0].get("cptCode"))
        self.assertEqual(
            CPTRecordStatusType.PENDING,
            rsp.json[0].get(CPTReportResponseObject.STATUS),
        )

    @freeze_time("2021-01-05T10:00:00.000Z")
    def test_submit_two_reports_overlapping_utc_midnight(self):
        # user timezone is New_York
        midnight = datetime.now(timezone.utc).replace(hour=0, minute=0, second=0, microsecond=0)
        two_hours_before = midnight - timedelta(hours=2)
        two_hours_after = midnight + timedelta(hours=2)

        body = self._sample_module_result()
        body["startDateTime"] = utc_str_field_to_val(two_hours_before)
        self._submit_module_result("Weight", body)

        body["startDateTime"] = utc_str_field_to_val(two_hours_after)
        self._submit_module_result("Weight", body)

        get_rsp = self.flask_client.get(
            self.base_url,
            headers=self.get_headers_for_token(VALID_CLINICIAN_ID),
        )

        billing_alert = BillingAlert.objects.get(user_id=USER_ID)
        submission_dates = billing_alert.submissionDates

        self.assertEqual(["2021-01-04"], submission_dates)

        self.assertEqual(200, get_rsp.status_code)
        self.assertEqual(1, get_rsp.json[0].get("daysHaveSubmission"))

    @freeze_time("2021-01-05T10:00:00.000Z")
    def test_submit_two_reports_overlapping_local_midnight(self):
        # user timezone is New_York
        ny_midnight_utc = datetime.now(timezone.utc).replace(hour=5, minute=0, second=0, microsecond=0)
        two_hours_before = ny_midnight_utc - timedelta(hours=2)
        two_hours_after = ny_midnight_utc + timedelta(hours=2)

        body = self._sample_module_result()
        body["startDateTime"] = utc_str_field_to_val(two_hours_before)
        self._submit_module_result("Weight", body)

        body["startDateTime"] = utc_str_field_to_val(two_hours_after)
        self._submit_module_result("Weight", body)

        get_rsp = self.flask_client.get(
            self.base_url,
            headers=self.get_headers_for_token(VALID_CLINICIAN_ID),
        )

        self.assertEqual(200, get_rsp.status_code)
        self.assertEqual(2, get_rsp.json[0].get("daysHaveSubmission"))

    def test_confirm_high_freq_heart_rate_is_whitelisted(self):
        report_before = self._request_report()
        self.assertIsNone(report_before[0].get("daysHaveSubmission"))

        data = sample_high_frequency_heart_rate()
        module_id = HighFrequencyHeartRateModule.moduleId
        self._submit_module_result(module_id, data)

        report_after = self._request_report()
        self.assertEqual(1, report_after[0].get("daysHaveSubmission"))

    @freeze_time("2023-06-25T10:00:00.000Z")
    def test_calendar_calculation(self):
        self._set_calendar_calculation_type()

        # Confirm that there are no submissions in this period
        report = self._request_report()
        self.assertEqual(0, report[0].get("daysHaveSubmission"))
        self.assertEqual(0, report[1].get("daysHaveSubmission"))

        first_submission_day = 7
        self._submit_multiple_module_results(month=6, day_from=first_submission_day)
        report = self._request_report()

        # CPT 99453/75
        self.assertEqual(16, report[0].get("daysHaveSubmission"))
        self.assertEqual(CPTRecordStatusType.COMPLETED, report[0].get("status"))
        # CPT 99454/76
        self.assertEqual(16, report[1].get("daysHaveSubmission"))
        self.assertEqual(CPTRecordStatusType.COMPLETED, report[1].get("status"))

        date_next_period = utc_str_field_to_val(datetime(2023, 7, 3))
        data = self._sample_module_result()
        data["startDateTime"] = date_next_period
        with freeze_time(date_next_period):
            self._submit_module_result("Weight", data)
            report = self._request_report()

        # CPT 99453/75 is missing after First Completed Period
        find(lambda x: x.get("cptCode") == CPTCode.RPM.CPT_MAPPING_1.value, report)
        # CPT 99454/76 started new period
        self.assertEqual(CPTRecordStatusType.IN_PROGRESS, report[0].get("status"))
        self.assertEqual(1, report[0].get("daysHaveSubmission"))

    @patch("sdk.common.push_notifications.push_notifications_utils.NotificationService")
    def test_user_reminded_to_make_submissions_3_times(self, notification_service):
        celery_app.conf.task_always_eager = True
        CalendarEventDTO.register(BillingSubmissionScheduleEvent.__name__, BillingSubmissionScheduleEvent)

        start_date = datetime(2023, 7, 1, 12, 0)
        with freeze_time(start_date):
            body = self._sample_module_result()
            body["startDateTime"] = utc_str_field_to_val(start_date)
            self._submit_module_result(WeightModule.moduleId, body)
            prepare_events_and_execute.apply()
            notification_service().push_for_user.assert_not_called()

        expected_push_notification_call_days = [2, 4, 6]
        for day in range(1, 10):
            with freeze_time(start_date.replace(hour=3) + timedelta(days=day)):
                prepare_events_for_next_day.apply()

            with freeze_time(start_date.replace(hour=14) + timedelta(days=day)):
                prepare_events_and_execute.apply()

            if day in expected_push_notification_call_days:
                notification_service().push_for_user.assert_called_once()
                notification_service.reset_mock()
                continue

            notification_service().push_for_user.assert_not_called()

    @patch("sdk.common.push_notifications.push_notifications_utils.NotificationService")
    def test_user_reminded_with_cycle_reset_and_tz_change(self, notification_service):
        celery_app.conf.task_always_eager = True
        CalendarEventDTO.register(BillingSubmissionScheduleEvent.__name__, BillingSubmissionScheduleEvent)

        def prepare_day(day_offset):
            """Run prepare_events_for_next_day at 3 AM"""
            current_time = start_date + timedelta(days=day_offset)
            with freeze_time(current_time.replace(hour=3)):
                prepare_events_for_next_day.apply()

        def execute_day(day_offset):
            """Run prepare_events_and_execute at 2 PM"""
            current_time = start_date + timedelta(days=day_offset)
            with freeze_time(current_time.replace(hour=14)):
                prepare_events_and_execute.apply()

        def init_new_cycle(day_offset):
            """Init new Submission cycle by submitting Weight module"""
            with freeze_time(start_date + timedelta(days=day_offset)):
                current_time = start_date + timedelta(days=day_offset)
                body = self._sample_module_result()
                body["startDateTime"] = utc_str_field_to_val(current_time)
                self._submit_module_result(WeightModule.moduleId, body)

        # Initial submission
        start_date = datetime(2023, 7, 1, 12, 0)
        with freeze_time(start_date):
            init_new_cycle(0)
            prepare_events_and_execute.apply()
            notification_service().push_for_user.assert_not_called()

        # Day 1: No notification
        prepare_day(1)
        execute_day(1)
        notification_service().push_for_user.assert_not_called()

        # Day 2: Expect notification
        prepare_day(2)
        execute_day(2)
        notification_service().push_for_user.assert_called_once()
        notification_service.reset_mock()

        # Day 3: No notification, new submission
        prepare_day(3)
        execute_day(3)
        notification_service().push_for_user.assert_not_called()
        init_new_cycle(3)

        # Day 4: No notification
        prepare_day(4)
        execute_day(4)
        notification_service().push_for_user.assert_not_called()

        # Day 5: Expect notification adjusted to new timezone
        self._update_user_tz("Europe/Kyiv")
        prepare_day(5)
        with freeze_time((start_date + timedelta(days=5)).replace(hour=7)):
            prepare_events_and_execute.apply()
            notification_service().push_for_user.assert_called_once()

    def _update_user_tz(self, tz: str, user_id: str = USER_ID):
        user = User.objects.filter(mongoId=user_id).first()
        user.timezone = tz
        user.save()

    def _set_calendar_calculation_type(self, use_calendar_calculation=True):
        set_submission_calculation_type(VALID_DEPLOYMENT_ID, use_calendar_calculation)

    def _submit_module_result(self, module_id, data, user_id=USER_ID):
        url = url_for(f"submit_module.create_module_result_{module_id}", user_id=user_id)
        rsp = self.flask_client.post(
            url,
            json=[data],
            headers=self.get_headers_for_token(user_id),
        )
        self.assertEqual(201, rsp.status_code)
        return rsp.json

    def _submit_multiple_module_results(self, month, day_from=2, day_to=23, module_id="Weight", user_id=USER_ID):
        data = self._sample_module_result()
        for day in range(day_from, day_to):
            data["startDateTime"] = utc_str_field_to_val(datetime(2023, month, day))
            self._submit_module_result(module_id, data, user_id)

    def _request_report(self, user_id=USER_ID):
        url = url_for("billing_route.report_billing_records_for_cpt", user_id=user_id)
        rsp = self.flask_client.get(
            url,
            headers=self.get_headers_for_token(VALID_CLINICIAN_ID),
        )
        self.assertEqual(200, rsp.status_code)
        return rsp.json

    @staticmethod
    def _sample_module_result():
        return {
            "deploymentId": VALID_DEPLOYMENT_ID,
            "deviceName": "iOS",
            "value": 100,
            "type": "Weight",
            "source": "HumaDeviceKit",
        }


class MovePatientBillingDataTestCase(PluginsTestCase):
    DEPLOYMENT_ID_2 = "5d386cc6ff885918d96edb2b"
    DEPLOYMENT_ID_3 = "65c611d4ed32374bf83b5341"
    GENERAL_REPORT_KEY = "GeneralBilling_2023-04-01_2023-07-01"
    MONTHLY_REPORT_KEY = "MonthlyBilling_2023-04-01_2023-07-01"
    LOG_REPORT_KEY = "ManualLogBilling_2023-04-01_2023-07-01"
    AUTOLOG_REPORT_KEY = "AutomatedLogBilling_2023-04-01_2023-07-01"
    REPORT_START_DATE = "2023-04-01T00:00:00.000Z"
    REPORT_END_DATE = "2023-07-01T00:00:00.000Z"

    components = [
        AuthComponent(),
        ExtendedAuthorizationComponent(),
        BillingComponent(),
        DeploymentComponent(),
        OrganizationComponent(),
        OnlineOfflineCallComponent(),
        VersionComponent(server_version="1.0.0", api_version="1.0.0"),
        ExtendedModuleResultComponent(
            additional_modules=[WeightModule()],
        ),
        BillingExportComponent(),
        StorageComponentV1(),
    ]
    fixtures = [
        Path(__file__).parent.joinpath("fixtures/move_patient_dump.json"),
    ]

    def test_move_patient__both_deployments_have_billing(self):
        self.onboard_user(USER_ID)
        # User has submission, manual log and time tracking in deployment A

        rsp = self.move_patient()
        self.assertEqual(200, rsp.status_code)
        self.trigger_onboard_patient()

        user_report = self.generate_user_report_on("2023-05-30")
        self.assertEqual(200, user_report.status_code)
        report_98976 = find(lambda c: c["cptCode"] == "98976", user_report.json)
        self.assertEqual(1, report_98976["daysHaveSubmission"])

        export_after_move = self.generate_report(self.DEPLOYMENT_ID_2)
        user_monthly = export_after_move.json[USER_ID][self.MONTHLY_REPORT_KEY]
        user_general = export_after_move.json[USER_ID][self.GENERAL_REPORT_KEY]
        self.assertEqual(3, len(user_monthly))
        self.assertEqual(VALID_DEPLOYMENT_ID, user_monthly[0]["deploymentId"])
        self.assertEqual(self.DEPLOYMENT_ID_2, user_monthly[2]["deploymentId"])
        self.assertEqual(17, user_general[0]["report"]["numberOfSubmissions"])
        self.assertEqual(30, user_general[0]["report"]["monitoringTimeMins"])

        rsp = self.fetch_billing_profiles(USER_ID, self.DEPLOYMENT_ID_2)
        self.assertEqual(200, rsp.status_code)
        self.assertEqual(1, len(rsp.json["users"]))
        profiles_rsp = self.fetch_billing_awaiting_profiles(self.DEPLOYMENT_ID_2)
        self.assertEqual(1, len(profiles_rsp.json["users"]))

    def test_move_patient__only_source_deployment_has_billing(self):
        self.onboard_user(USER_ID)
        # User has submission, manual log and time tracking in deployment A
        # disable Billing for deployment B
        self.disable_billing_for_deployment(self.DEPLOYMENT_ID_2)

        rsp = self.move_patient()
        self.assertEqual(200, rsp.status_code)
        self.trigger_onboard_patient()

        user_report = self.generate_user_report_on("2023-05-30")
        self.assertEqual(400, user_report.status_code)
        export_target = self.generate_report(self.DEPLOYMENT_ID_2)
        self.assertEmpty(export_target.json)
        export_source = self.generate_report(VALID_DEPLOYMENT_ID)
        monthly_billing = export_source.json[USER_ID].get(self.MONTHLY_REPORT_KEY)
        general_billing = export_source.json[USER_ID].get(self.GENERAL_REPORT_KEY)
        self.assertIsNone(monthly_billing)
        self.assertIsNone(general_billing)

    def test_move_patient__export_billing_only_for_last_user_deployment(self):
        self.onboard_user(USER_ID)
        # User has submission, manual log and time tracking in deployment A
        export_deployment_a = self.generate_report(VALID_DEPLOYMENT_ID)
        monthly_billing = export_deployment_a.json[USER_ID].get(self.MONTHLY_REPORT_KEY)
        general_billing = export_deployment_a.json[USER_ID].get(self.GENERAL_REPORT_KEY)
        autolog = export_deployment_a.json[USER_ID].get(self.AUTOLOG_REPORT_KEY)
        manual_billing = export_deployment_a.json[USER_ID].get(self.LOG_REPORT_KEY)
        self.assertEqual(len(monthly_billing), 3)
        self.assertEqual(len(general_billing), 1)
        self.assertEqual(len(autolog), 3)
        self.assertEqual(len(manual_billing), 1)

        # Move user from deployment A to deployment B
        rsp = self.move_patient()
        self.assertEqual(200, rsp.status_code)
        self.trigger_onboard_patient()

        # Generate report for deployment A again
        export_source_a = self.generate_report(VALID_DEPLOYMENT_ID)
        monthly_billing = export_source_a.json[USER_ID].get(self.MONTHLY_REPORT_KEY)
        general_billing = export_source_a.json[USER_ID].get(self.GENERAL_REPORT_KEY)
        autolog = export_source_a.json[USER_ID].get(self.AUTOLOG_REPORT_KEY)
        manual_billing = export_source_a.json[USER_ID].get(self.LOG_REPORT_KEY)
        self.assertIsNone(monthly_billing)
        self.assertIsNone(general_billing)
        self.assertIsNone(autolog)
        self.assertIsNone(manual_billing)

        # Generate report for deployment B
        export_target = self.generate_report(self.DEPLOYMENT_ID_2)
        monthly_billing = export_target.json[USER_ID].get(self.MONTHLY_REPORT_KEY)
        general_billing = export_target.json[USER_ID].get(self.GENERAL_REPORT_KEY)
        autolog = export_target.json[USER_ID].get(self.AUTOLOG_REPORT_KEY)
        manual_billing = export_target.json[USER_ID].get(self.LOG_REPORT_KEY)
        self.assertEqual(len(monthly_billing), 3)
        self.assertEqual(len(general_billing), 1)
        self.assertEqual(len(autolog), 3)
        self.assertEqual(len(manual_billing), 1)

    def test_move_patient__only_target_deployment_has_billing(self):
        self.disable_billing_for_deployment(VALID_DEPLOYMENT_ID)
        self.remove_billing_profile_data(USER_ID)
        self.onboard_user(USER_ID)
        self.clear_components_data(USER_ID)

        rsp = self.move_patient()
        self.assertEqual(200, rsp.status_code)
        self.trigger_onboard_patient()

        awaiting_list = self.fetch_billing_awaiting_profiles(self.DEPLOYMENT_ID_2)
        self.assertEqual(1, len(awaiting_list.json["users"]))

    def test_move_patient__different_submission_calculation_types_in_move_history(self):
        self.onboard_user(USER_ID)
        # Given User has been moved before from DEPLOYMENT_ID to DEPLOYMENT_ID_2
        # DEPLOYMENT_ID has 30-day Calculation
        # DEPLOYMENT_ID_2 has no billing enabled
        self.disable_billing_for_deployment(self.DEPLOYMENT_ID_2)

        rsp = self.move_patient()
        self.assertEqual(200, rsp.status_code)

        # When User is moved to a DEPLOYMENT_ID_3 with Calendar Calculation
        self._set_calendar_calculation_type(self.DEPLOYMENT_ID_3, use_calendar_calculation=True)

        rsp = self.move_patient(source=self.DEPLOYMENT_ID_2, target=self.DEPLOYMENT_ID_3)
        self.assertEqual(400, rsp.status_code, rsp.text)

    def test_move_patient__deployments_with_different_submission_calculation_type(self):
        self._set_calendar_calculation_type()
        self.onboard_user(USER_ID)

        rsp = self.move_patient()
        self.assertEqual(400, rsp.status_code)

    def onboard_user(self, user_id):
        user = User.objects.get(mongoId=user_id)
        user.finishedOnboarding = True
        user.boardingStatus = user.boardingStatus or {}
        user.boardingStatus["status"] = 0
        user.boardingStatus["updateDateTime"] = "2021-02-09T14:08:05.997Z"
        user.save()

    @freeze_time("2023-05-25")
    def move_patient(self, source=VALID_DEPLOYMENT_ID, target=DEPLOYMENT_ID_2):
        self.resolve_flags(USER_ID, source)
        details = {
            MoveUserDetails.REASON: "Completed Treatment",
            MoveUserDetails.SITUATION: "Nothing Special",
            MoveUserDetails.BACKGROUND: "Was Healthy",
            MoveUserDetails.ASSESSMENT: "Should be fine",
            MoveUserDetails.MESSAGE: "You were moved to the new deployment",
            MoveUserDetails.RECOMMENDATION: "Stay Healthy",
        }
        url = url_for(
            "user_v1.move_user",
            user_id=USER_ID,
            target_deployment_id=target,
        )
        return self.flask_client.post(
            url,
            json={"details": details},
            headers=self.get_headers_for_token(VALID_CLINICIAN_ID),
        )

    def generate_report(self, deployment_id, from_=REPORT_START_DATE, to=REPORT_END_DATE):
        url = url_for("rpm_export_deployment_route.export_deployment", deployment_id=deployment_id)
        payload = {
            **self._export_payload(from_, to),
            ExportParameters.FORMAT: ExportParameters.DataFormatOption.JSON.value,
        }

        rsp = self.flask_client.post(
            url,
            headers=self.get_headers_for_token(VALID_CLINICIAN_ID),
            json=payload,
        )
        self.assertEqual(200, rsp.status_code)
        return rsp

    def generate_user_report_on(self, now_dt: str):
        url = url_for("billing_route.report_billing_records_for_cpt", user_id=USER_ID)
        with freeze_time(now_dt):
            rsp = self.flask_client.get(
                url,
                headers=self.get_headers_for_token(VALID_CLINICIAN_ID),
            )
        return rsp

    def fetch_billing_awaiting_profiles(self, deployment_id: str):
        url = url_for("user_v1.retrieve_user_profiles_v1")
        body = {
            "deploymentId": deployment_id,
            "filters": {
                "componentsData": {"billing.status": 0},
                "boardingStatus.status": 0,
            },
        }
        rsp = self.flask_client.post(
            url,
            headers=self.get_headers_for_token(VALID_CLINICIAN_ID),
            json=body,
        )
        self.assertEqual(200, rsp.status_code)
        return rsp

    @staticmethod
    def _sample_module_result(deployment_id=VALID_DEPLOYMENT_ID, start_dt="2023-04-10T00:00:00.000Z"):
        return [
            {
                "deploymentId": deployment_id,
                "startDateTime": start_dt,
                "deviceName": "iOS",
                "value": 100,
                "type": "Weight",
                "source": "HumaDeviceKit",
            }
        ]

    @staticmethod
    def _export_payload(from_, to_):
        return {
            ExportParameters.FROM_DATE: from_,
            ExportParameters.TO_DATE: to_,
            ExportParameters.SINGLE_FILE_RESPONSE: True,
            ExportRequestObject.REQUESTER_ID: VALID_CLINICIAN_ID,
        }

    def clear_components_data(self, user_id: str):
        user = User.objects.get(mongoId=user_id)
        user.finishedOnboarding = True
        if user.componentsData and user.componentsData["billing"]:
            del user.componentsData["billing"]["status"]
        user.save()

    def disable_billing_for_deployment(self, deployment_id):
        deployment = Deployment.objects.get(mongoId=deployment_id)
        deployment.features["customAppConfig"] = None
        deployment.save()

    def remove_billing_profile_data(self, user_id):
        billing_profile_history_repo = inject.instance(BillingProfileHistoryLogRepository)
        billing_profile_history_repo.delete_user_data(user_id=user_id)

    def resolve_flags(self, user_id: str, deployment_id: str):
        headers = self.get_headers_for_token(VALID_CLINICIAN_ID)
        request_object = AddUserNotesV2RequestObject(note="note")
        url = url_for("notes_v2.add_user_notes", user_id=user_id, deployment_id=deployment_id)
        rsp = self.flask_client.post(url, json=request_object.to_dict(), headers=headers)
        self.assertEqual(201, rsp.status_code, rsp.text)

    def fetch_billing_profiles(self, user_id: str, deployment_id: str):
        url = url_for("billing_route.retrieve_billing_alerts")
        body = {
            "skip": 0,
            "limit": 20,
            "search": user_id,
            "deploymentId": deployment_id,
        }
        rsp = self.flask_client.post(
            url,
            json=body,
            headers=self.get_headers_for_token(VALID_CLINICIAN_ID),
        )
        return rsp

    def trigger_onboard_patient(self):
        self._ack_move()
        url = url_for("user_v1.retrieve_deployment_config", user_id=USER_ID)
        rsp = self.flask_client.get(
            url,
            headers=self.get_headers_for_token(USER_ID),
        )
        self.assertEqual(200, rsp.status_code)

    def _ack_move(self):
        history = UserMoveHistory.objects.filter(userId=USER_ID, targetResourceId=self.DEPLOYMENT_ID_2).first()
        history.isAcknowledged = True
        history.save()

    def _set_calendar_calculation_type(self, deployment_id=VALID_DEPLOYMENT_ID, use_calendar_calculation=True):
        set_submission_calculation_type(deployment_id, use_calendar_calculation)


class BillingSubmissionCreationTestCase(BillingTestCase):
    def test_created_billing_submission_added_to_alert(self):
        alert = BillingAlert.objects.filter(mongoId=USER_ID).first()
        self.assertEqual(0, len(alert.submissionDates))

        weight_1 = sample_weight()
        weight_1["source"] = "HumaDeviceKit"
        self._create_module_result([weight_1])

        submission = BillingSubmission.objects.filter(userId=USER_ID).first()
        self.assertIsNotNone(submission)

        alert.refresh_from_db()
        self.assertEqual(1, len(alert.submissionDates))

    def test_failed_billing_submission_not_added_to_alert(self):
        alert = BillingAlert.objects.filter(mongoId=USER_ID).first()
        self.assertEqual(0, len(alert.submissionDates))

        weight_1 = sample_weight()
        weight_1["source"] = PrimitiveSources.MANUAL.value  # not eligible for billing
        self._create_module_result([weight_1])

        submission = BillingSubmission.objects.filter(userId=USER_ID).first()
        self.assertIsNone(submission)

        alert.refresh_from_db()
        self.assertEqual(0, len(alert.submissionDates))

    @freeze_time("2023-05-02")
    def test_module_results_submitted_on_period_edge(self):
        self._set_calendar_calculation_type()
        # Both periods are in the same month in UTC, but different in local time
        weight_1 = sample_weight()
        weight_1["startDateTime"] = "2023-04-30T23:00:00.000Z"
        weight_1["source"] = "HumaDeviceKit"
        weight_2 = sample_weight()
        weight_2["startDateTime"] = "2023-05-01T01:00:00.000Z"
        weight_2["source"] = "HumaDeviceKit"
        body = [weight_1, weight_2]

        self._create_module_result(body)

        submissions = self._retrieve_submission()
        # Both submissions are in the same period in User's local time
        self.assertEqual(1, len(submissions))
        self.assertEqual(1, submissions[0]["todaySubmissionCount"])  # 0-based index

    def test_batch_submissions_count(self):
        self._set_calendar_calculation_type()
        # Both periods are in the same month in UTC, but different in local time
        weight = sample_weight()
        weight["startDateTime"] = "2023-04-30T12:00:00.000Z"
        weight["source"] = "HumaDeviceKit"

        self._create_module_result([weight, weight, weight])

        submissions = self._retrieve_submission()
        self.assertEqual(1, len(submissions))
        self.assertEqual(2, submissions[0]["todaySubmissionCount"])  # 0-based index

    @freeze_time("2023-05-03")
    def test_followup_submissions_created_in_RTM(self):
        module_id = RespiratoryRateModule.moduleId
        rr1 = sample_respiratory_rate()
        rr1[PrimitiveDTO.START_DATE_TIME] = utc_str_field_to_val(datetime(2023, 5, 1))
        rr1[PrimitiveDTO.DEPLOYMENT_ID] = RTM_DEPLOYMENT_ID
        rr1.pop(BillingSubmissionDTO.SOURCE, None)

        self._create_module_result([rr1], module_id, RTM_USER_ID)
        submission = BillingSubmission.objects.filter(userId=RTM_USER_ID).first()
        self.assertIsNotNone(submission)

        rr2 = sample_respiratory_rate()
        rr2[PrimitiveDTO.DEPLOYMENT_ID] = RTM_DEPLOYMENT_ID
        rr2.pop(BillingSubmissionDTO.SOURCE, None)

        self._create_module_result([rr2], module_id, RTM_USER_ID)
        submission = BillingSubmission.objects.filter(userId=RTM_USER_ID).first()
        self.assertIsNotNone(submission)

        alert = BillingAlert.objects.filter(mongoId=RTM_USER_ID).first()
        self.assertEqual(2, len(alert.submissionDates))

    def _create_module_result(self, body, module_id: str = WeightModule.moduleId, user_id=USER_ID):
        url = url_for(
            f"submit_module.create_module_result_{module_id}",
            user_id=user_id,
        )
        return self.flask_client.post(
            url,
            json=body,
            headers=self.get_headers_for_token(user_id),
        )

    def _set_calendar_calculation_type(self, use_calendar_calculation=True):
        set_submission_calculation_type(VALID_DEPLOYMENT_ID, use_calendar_calculation)

    def _retrieve_submission(self):
        return [model_to_dict(sub) for sub in BillingSubmission.objects.filter(userId=USER_ID)]
