import json
import logging
import os
import sys
from datetime import datetime, timezone
from pathlib import Path
from typing import Iterable

import marshmallow
from bson import ObjectId
from django.db.models.expressions import RawSQL
from jsonschema.validators import validate

from huma_plugins.components.cms.dtos import Status
from huma_plugins.components.cms.library import (
    CMSL<PERSON>rary,
    CMS_QUESTIONNAIRE_ID,
    QUESTIONNAIRE_CONFIGURATION_KIND_CMS,
    QUESTIONNAIRE_TYPE,
    QUESTIONNAIRE_COLLECTION_NAME,
)
from huma_plugins.components.cms.models import CMSContent
from sdk.common.utils import inject
from sdk.deployment.models import Deployment

logger = logging.getLogger(__name__)

FOLDER_NAME = "questionnaires_log"

SCHEMA_PATH = Path(__file__).parent.parent.joinpath("library", "schemas", "questionnaires.json")
try:
    with open(SCHEMA_PATH, "r") as schema_file:
        QUESTIONNAIRE_SCHEMA = json.load(schema_file)
except (FileNotFoundError, json.JSONDecodeError) as e:
    logger.error(f"Error loading schema file: {e}")
    QUESTIONNAIRE_SCHEMA = {}


def move_questionnaires_from_deployment_to_cms(
    deployment_ids=[],
    blacklist_deployment_ids=[],
    only_log=False,
    only_log_errors=False,
    dry_run=False,
):
    if deployment_ids == ["ALL"]:
        deployments = Deployment.objects.filter(moduleConfigs__contains=[{"moduleId": "Questionnaire"}])
        deployment_ids = list(deployments.values_list("mongoId", flat=True))

        logger.debug(f"Found {len(deployment_ids)} deployments with questionnaire module.")
        if not ask_confirmation():
            return

    for deployment_id in deployment_ids:
        if deployment_id in blacklist_deployment_ids:
            logger.debug(f"Skipping deployment {deployment_id}.")
            continue

        deployment = Deployment.objects.get(mongoId=deployment_id)
        if not deployment:
            logger.warning(f"Deployment: {deployment_id} not found")
            continue

        config_bodies, invalid_config_bodies, invalid_config_bodies_errors = [], [], []

        update_module_config_to_cms(
            deployment,
            deployment_id,
            invalid_config_bodies_errors,
            invalid_config_bodies,
            only_log,
            config_bodies,
            dry_run,
        )

        if config_bodies or invalid_config_bodies:
            file_path = os.path.join(FOLDER_NAME, f"{deployment_id}.json")
            invalid_file_path = os.path.join(FOLDER_NAME, f"{deployment_id}_invalid.json")
            os.makedirs(os.path.dirname(file_path), exist_ok=True)
            if config_bodies and not only_log_errors:
                with open(file_path, "w") as file:
                    json.dump(config_bodies, file, indent=4)
            if invalid_config_bodies:
                with open(invalid_file_path + ".exceptions", "w") as file:
                    for error in invalid_config_bodies_errors:
                        file.write(error.message.strip())
                        file.write("\n" + "-" * 80 + "\n")
                with open(invalid_file_path, "w") as file:
                    json.dump(invalid_config_bodies, file, indent=4)


def update_module_config_to_cms(
    deployment,
    deployment_id,
    invalid_config_bodies_errors,
    invalid_config_bodies,
    only_log,
    config_bodies,
    dry_run,
):
    for module_config in deployment.moduleConfigs[:]:
        if module_config.get("moduleId") != "Questionnaire":
            continue

        module_config_id = module_config.get("id")
        logger.debug(f"Deployment: {deployment_id} has questionnaire module with id: {module_config_id}")

        q_config_body = module_config.get("configBody", {})

        q_type = q_config_body.get(QUESTIONNAIRE_TYPE)
        q_id = q_config_body.get("id")

        if q_type == QUESTIONNAIRE_CONFIGURATION_KIND_CMS:
            logger.info(f"Questionnaire: {q_id} already in CMS")
            continue

        try:
            validate(q_config_body, QUESTIONNAIRE_SCHEMA)
        except Exception as e:
            logger.error(f"Deployment:{deployment_id} ModuleConfig:{module_config_id}: Validation error: {e}")
            invalid_config_bodies_errors.append(e)
            invalid_config_bodies.append(q_config_body)
            continue

        if only_log:
            config_bodies.append(q_config_body)
            continue

        q_config_body.pop("id", None)
        # ToDo: Double check if we need to remove this field or not? @mahdib-huma

        json_data = {
            "data": q_config_body,
            "status": "DRAFT",
            "resources": ["deployment/" + deployment_id],
            "tags": ["migrated_from_deployment"],
            "schema": "questionnaires",
        }

        content = CMSContent(**json_data)
        content.mongoId = ObjectId()
        now = datetime.now(timezone.utc)
        content.createDateTime = content.updateDateTime = now
        if content.status is Status.PUBLISHED:
            content.publishDateTime = now

        try:
            content.save()
            logger.debug(f"Questionnaire added to CMS with id: {str(content.mongoId)}")
        except Exception as e:
            logger.warning(f"Questionnaire with id {q_id} not added to CMS with error: {e}")
            continue

        module_config["configBody"] = {
            CMS_QUESTIONNAIRE_ID: str(content.mongoId),
            QUESTIONNAIRE_TYPE: QUESTIONNAIRE_CONFIGURATION_KIND_CMS,
            "id": q_id,
        }

        if not dry_run:
            logger.info(f"Updating deployment: {deployment_id}")

            deployment.save()

            logger.info(f"Deployment: {deployment_id} ModuleConfig: {module_config_id} updated successfully!")


def ask_confirmation():
    while True:
        response = input("Do you want to continue? (yes/no): ").strip().lower()
        if response in ["yes", "y"]:
            print("Continuing...")
            return True
        elif response in ["no", "n"]:
            print("Exiting...")
            sys.exit(0)
        else:
            print("Invalid response. Please answer 'yes' or 'no'.")


def apply_cms_content_localization(content: dict, languages: Iterable) -> dict:
    if "localizable" not in content:
        return content

    translations = content["localizable"]
    if not translations:
        return {}

    for language in languages:
        for translation in translations:
            if translation.get("language") == language:
                return translation

    return translations[0]


def remove_cms_questionnaire_id_from_data(apps, schema_editor):
    (
        CMSContent.objects.filter(
            schema=QUESTIONNAIRE_COLLECTION_NAME,
            data__has_key=CMS_QUESTIONNAIRE_ID,
        ).update(data=RawSQL("data - %s", (CMS_QUESTIONNAIRE_ID,)))
    )


def _validate_collection_exists(collection_name: str) -> None:
    """Validates that the specified collection registered in the CMS."""
    if collection_name is None:
        return
    if collection_name == "":
        raise marshmallow.ValidationError("Collection name can't be empty")
    if not inject.instance(CMSLibrary).get_schema(collection_name):
        raise marshmallow.ValidationError(f"Collection {collection_name} not found or not registered")
