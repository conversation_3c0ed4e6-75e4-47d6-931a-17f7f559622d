import abc

from huma_plugins.components.cms.dtos import CMSContentDTO
from sdk.common.common_models.sort import SortField


class CMSRepository(abc.ABC):
    @abc.abstractmethod
    def create(self, content: CMSContentDTO) -> str:
        raise NotImplementedError

    @abc.abstractmethod
    def create_draft(self, original_id: str) -> str:
        raise NotImplementedError

    @abc.abstractmethod
    def update(self, content_id: str, content: CMSContentDTO) -> str:
        raise NotImplementedError

    @abc.abstractmethod
    def publish(self, content_id: str, author_id: str) -> str:
        raise NotImplementedError

    @abc.abstractmethod
    def bulk_update(self, ids: list[str], content: CMSContentDTO) -> list[str]:
        raise NotImplementedError

    @abc.abstractmethod
    def retrieve(self, collection: str, content_id: str, version: str | int | None = None):
        raise NotImplementedError

    @abc.abstractmethod
    def retrieve_by_ids(self, collection: str, content_ids: list[str]):
        raise NotImplementedError

    @abc.abstractmethod
    def delete(self, collection: str | None, content_id: str) -> str:
        raise NotImplementedError

    @abc.abstractmethod
    def search(
        self,
        collection: str | None,
        filters: dict | None,
        sort_fields: list[SortField] | None,
        skip: int | None,
        limit: int | None,
        search: str | None = None,
        unpublished: bool = False,
        only_published: bool = False,
    ) -> tuple[list[CMSContentDTO], int]:
        raise NotImplementedError

    @abc.abstractmethod
    def retrieve_all_tags(self, collection: str, resource: str) -> list[str]:
        raise NotImplementedError
