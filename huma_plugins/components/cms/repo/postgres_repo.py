import json
from copy import deepcopy
from datetime import datetime, timezone
from itertools import chain

import deepdiff
from bson import ObjectId, json_util
from django.db.models import F, Q

from huma_plugins.components.cms.dtos import CMSContentDTO, Status
from huma_plugins.components.cms.models import <PERSON><PERSON>ChangeLog, CMSContent
from huma_plugins.components.cms.repo import CMSRepository
from sdk.common.adapter.sql import build_sql_query
from sdk.common.common_models.sort import SortField
from sdk.common.exceptions.exceptions import InvalidRequestException, ObjectDoesNotExist
from sdk.common.utils.validators import model_to_dict


class PostgresCMSRepository(CMSRepository):
    MSG_CONTENT_NOT_FOUND = "Content not found"

    def create(self, content: CMSContentDTO) -> str:
        now = datetime.now(timezone.utc)
        content.createDateTime = content.updateDateTime = now
        if content.status is Status.PUBLISHED:
            content.publishDateTime = now
        content_entity = CMSContent(**content.to_dict(include_none=False))
        content_entity.mongoId = ObjectId()
        content_entity.save()
        return str(content_entity.mongoId)

    def create_draft(self, original_id: str) -> str:
        original = CMSContent.objects.filter(mongoId=original_id).first()
        if original.status == Status.DRAFT.value:
            raise InvalidRequestException("Content is already a draft")
        draft = deepcopy(original)
        if not original:
            raise ObjectDoesNotExist(self.MSG_CONTENT_NOT_FOUND)

        draft.status = Status.DRAFT
        draft.publishDateTime = None
        draft.id = None
        draft.mongoId = ObjectId()
        draft.save()

        original.draft = draft.mongoId
        original.save()
        return str(draft.mongoId)

    def update(self, content_id: str, content: CMSContentDTO) -> str:
        query = CMSContent.objects.filter(mongoId=content_id, schema=content.schema)
        mongo_content = query.first()
        if not mongo_content:
            raise ObjectDoesNotExist(self.MSG_CONTENT_NOT_FOUND)

        now = datetime.now(timezone.utc)
        content.updateDateTime = now
        if content.status is Status.PUBLISHED and not mongo_content.publishDateTime:
            content.publishDateTime = now

        update_dict = content.to_dict(include_none=False)
        update_dict.pop("id")
        query.update(**update_dict)
        return str(content.id)

    def publish(self, content_id: str, author_id: str) -> str:
        content = CMSContent.objects.filter(mongoId=content_id).first()
        if not content.draftData:
            return str(content.mongoId)

        old_content = model_to_dict(content)

        content.data = content.draftData
        content.draftData = None
        content.status = Status.PUBLISHED
        content.publishDateTime = datetime.now(timezone.utc)
        content.version += 1
        content.save()
        self._create_change_log(old_content, model_to_dict(content), author_id)
        return str(content.mongoId)

    def bulk_update(self, ids: list[str], content: CMSContentDTO):
        CMSContent.objects.filter(mongoId__in=ids).update(**content.to_dict(include_none=False))

    def retrieve(self, collection: str, content_id: str, version: str | int | None = None) -> CMSContentDTO:
        content_in_cms = CMSContent.objects.filter(mongoId=content_id, schema=collection).first()
        if not content_in_cms:
            raise ObjectDoesNotExist(self.MSG_CONTENT_NOT_FOUND)
        if version is not None:
            if version in ("latest", ""):
                version = None
            elif not version.isdigit():
                raise InvalidRequestException("Version must be an integer")
            else:
                version = int(version)

        version_in_cms = content_in_cms.version

        if version is None or version_in_cms == version:
            return CMSContentDTO.from_dict(model_to_dict(content_in_cms), use_validator_field=False)

        content: CMSChangeLog = CMSChangeLog.objects.filter(
            contentId=content_id, snapshot__schema=collection, snapshot__version=version
        ).first()

        if not content:
            raise ObjectDoesNotExist(self.MSG_CONTENT_NOT_FOUND)

        return CMSContentDTO.from_dict(content.snapshot, use_validator_field=False)

    def retrieve_by_ids(self, collection: str, content_ids: list[str]) -> list[CMSContentDTO]:
        result = CMSContent.objects.filter(mongoId__in=content_ids, schema=collection)
        return [CMSContentDTO.from_dict(model_to_dict(content), use_validator_field=False) for content in result]

    def delete(self, collection: str | None, content_id: str):
        content = (
            CMSContent.objects.filter(mongoId=content_id, schema=collection).first()
            if collection
            else CMSContent.objects.filter(mongoId=content_id).first()
        )
        if not content:
            raise ObjectDoesNotExist("Content not found")
        CMSChangeLog.objects.filter(contentId=content_id).delete()
        content.delete()

    def search(
        self,
        collection: str | None,
        filters: dict | None,
        sort_fields: list[SortField] | None,
        skip: int | None,
        limit: int | None,
        search: str | None = None,
        unpublished: bool = False,
        only_published: bool = False,
    ) -> tuple[list[CMSContentDTO], int]:
        data_search = {key: value for key, value in filters.items() if key.startswith("data")}
        for key in data_search:
            filters.pop(key)

        tag_filter = filters.pop("tags", [])

        query = build_sql_query(kwargs=filters)
        if tag_filter:
            tag_conditions = Q()
            for tag in tag_filter:
                tag_conditions |= Q(tags__contains=[tag])
            query &= tag_conditions

        for key, value in data_search.items():
            or_query = Q()
            if "localizable" in key:
                for i in range(20):
                    new_key = key.replace(".localizable.", f"__localizable__{i}__") + "__icontains"
                    or_query |= Q(**{new_key: value})
            else:
                or_query |= Q(**{key.replace(".", "__"): value})

            query &= or_query

        if search and ObjectId.is_valid(search):
            query &= Q(mongoId=search)
        elif search:
            search_query = Q(tags__icontains=search)
            for i in range(20):
                search_query |= Q(**{f"data__localizable__{i}__title__icontains": search})

            query &= search_query

        if collection:
            query &= Q(schema=collection)

        if only_published:
            query &= Q(status=Status.PUBLISHED.value)

        result = CMSContent.objects.filter(query)
        count = result.count()
        if sort_fields:
            result = self.apply_sort_fields(result, sort_fields, unpublished)

        if skip is not None and limit is not None:
            result = result[skip : skip + limit]
        elif skip is not None:
            result = result[skip:]
        elif limit is not None:
            result = result[:limit]

        return [CMSContentDTO.from_dict(model_to_dict(content), use_validator_field=False) for content in result], count

    def _create_change_log(self, old_content: dict, new_content: dict, author_id: str) -> str:
        diff = deepdiff.DeepDiff(
            old_content,
            new_content,
            ignore_order=True,
            exclude_paths=["version"],
            verbose_level=2,
        )
        log = CMSChangeLog(
            mongoId=ObjectId(),
            contentId=old_content["id"],
            snapshot=json.loads(json.dumps(old_content, default=json_util.default)),
            version=new_content.get("version", 1),
            authorId=author_id,
        )

        log.change = json.loads(diff.to_json())
        log.save()
        return str(log.mongoId)

    def retrieve_all_tags(self, collection: str, resource: str) -> list[str]:
        content_items = (
            CMSContent.objects.filter(schema=collection, resources__contains=resource)
            .exclude(tags__isnull=True)
            .exclude(tags=[])
        )
        all_tags = list(chain.from_iterable(item.tags for item in content_items if item.tags))
        return sorted(list(set(all_tags)))

    @staticmethod
    def apply_sort_fields(queryset, sort_fields: list[SortField], is_unpublished: bool):
        for sort in sort_fields:
            order_by = sort.field
            if sort.field.startswith(CMSContentDTO.DATA):
                prefix = CMSContentDTO.DRAFT_DATA if is_unpublished else CMSContentDTO.DATA
                parts = sort.field.split(".")
                order_by = f"{prefix}__localizable__0__{parts[-1]}"

            if sort.direction is SortField.Direction.DESC:
                queryset = queryset.order_by(F(order_by).desc(nulls_last=True))
            else:
                queryset = queryset.order_by(F(order_by).asc(nulls_last=True))

        return queryset
