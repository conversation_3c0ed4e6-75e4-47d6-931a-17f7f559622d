from huma_plugins.components.cms.dtos import CMSContentDTO
from huma_plugins.components.cms.repo import CMSRepository
from sdk.common.utils.inject import autoparams


class CMSService:
    @autoparams()
    def __init__(self, repo: CMSRepository):
        self._repo = repo

    def retrieve(self, collection: str, content_id: str) -> CMSContentDTO:
        return self._repo.retrieve(collection, content_id)

    def retrieve_by_ids(self, collection: str, content_ids: list) -> list[CMSContentDTO]:
        return self._repo.retrieve_by_ids(collection, content_ids)

    def search(
        self,
        collection: str | None,
        filters: dict | None,
        sort_fields: list | None,
        skip: int | None,
        limit: int | None,
        search: str | None = None,
        unpublished: bool = False,
        only_published: bool = False,
    ) -> tuple[list[CMSContentDTO], int]:
        return self._repo.search(collection, filters, sort_fields, skip, limit, search, unpublished, only_published)

    def delete(self, collection: str, content_id: str):
        return self._repo.delete(collection, content_id)
