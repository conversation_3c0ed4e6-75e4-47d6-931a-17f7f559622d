from huma_plugins.components.cms.dtos import CMSContentDTO
from huma_plugins.components.cms.repo import CMSRepository
from sdk.common.usecase.use_case import UseCase
from sdk.common.utils.inject import autoparams
from ._requests import ContentIdRequestObject, ContentIdVersionRequestObject


class RetrieveContentUseCase(UseCase):
    """Retrieve content item by ID"""

    request_object: ContentIdRequestObject | ContentIdVersionRequestObject

    @autoparams("repo")
    def __init__(self, repo: CMSRepository):
        self._repo = repo

    def process_request(self, request_object: ContentIdRequestObject | ContentIdVersionRequestObject) -> CMSContentDTO:
        if isinstance(request_object, ContentIdVersionRequestObject):
            return self._repo.retrieve(request_object.collection, request_object.contentId, request_object.version)
        return self._repo.retrieve(request_object.collection, request_object.contentId)
