import calendar
import logging
import sys
from copy import deepcopy
from datetime import date, datetime, timedelta

from django.db import transaction

from huma_plugins.components.billing_portal.dtos.billing_portal_models import (
    BillingRecordDTO,
    CPTGeneralExport,
    CPTMonthlyExport,
    CptChargeUnitDTO,
    CptCodeEnum,
)
from huma_plugins.components.billing_portal.models import BillingGeneralCPTCodes, BillingMonthlyCPTCodes
from huma_plugins.components.billing_portal.models.billing_record import BillingRecord
from huma_plugins.components.billing_portal.models.client import Client
from huma_plugins.components.billing_portal.models.cpt_charge_unit import CptChargeUnit, CptCode
from huma_plugins.components.billing_portal.models.invoice import Invoice
from huma_plugins.components.billing_portal.models.invoice_entry import InvoiceEntry
from huma_plugins.components.billing_portal.models.sales_force_file import SalesForceFile
from huma_plugins.components.billing_portal.repository.billing_portal_repository import BillingPortalRepository
from huma_plugins.components.billing_portal.router.billing_portal_requests import GenerateReportsRequestObject
from huma_plugins.components.billing_portal.router.billing_portal_response_objects import GenerateReportsResponseObject
from huma_plugins.components.billing_portal.use_case.utils.salesforce import (
    SALES_FORCE_EMPTY_DICT,
    SALES_FORCE_ICD_10,
    SALES_FORCE_INSURANCE,
    SALES_FORCE_MRN,
    SALES_FORCE_PROVIDER,
    read_sales_force_file,
)
from sdk.authorization.repository import AuthorizationRepository
from sdk.common.usecase.use_case import UseCase
from sdk.common.utils.inject import autoparams
from sdk.common.utils.validators import model_to_dict
from sdk.deployment.dtos.consent import ConsentLogDTO
from sdk.deployment.service.deployment_service import DeploymentService
from sdk.module_result.service.observation_note import ObservationNoteService

logger = logging.getLogger(__name__)


REPORT_DATE_FORMAT = "%Y-%m-%d"

# Payment formula constants
FORMULA_PREFIX = "FORMULA"
FORMULA_20_MINUTES_MONITORING = f"{FORMULA_PREFIX}_20_MINUTES_MONITORING"
FORMULA_MONTH_READING = f"{FORMULA_PREFIX}_MONTH_READING"
FORMULA_SIGN_UP = f"{FORMULA_PREFIX}_SIGN_UP"
FORMULA_BASIC = f"{FORMULA_PREFIX}_BASIC"

# Bucket utils constants
BUCKET_NAME = "hu-he-us-respi"
BILLING_BUCKET_LOCATION = "./cpt_app/bucket"  # TODO: fix!
BILLING_FILES_LOCATION = f"{BILLING_BUCKET_LOCATION}/hu-he-us-respi"  # fix this ugly code
VITAL_FILES_LOCATION = f"{BILLING_FILES_LOCATION}/daily_aggregated_vitals"
CONSENT_FILE_PATH = f"{BILLING_FILES_LOCATION}/consents.json"


def get_last_day_of_last_month_string():
    """
    Calculates and returns the last day of the previous month as a string.

    Returns:
        datetime: The last day of the previous month.
    """
    today = date.today()
    first_day_of_current_month = today.replace(day=1)
    last_day_of_last_month = first_day_of_current_month - timedelta(days=1)
    return last_day_of_last_month


def get_time_range_of_last_month():
    today = date.today()
    first_date_time_of_current_month = datetime.combine(today.replace(day=1), datetime.min.time())
    last_date_time_of_last_month = first_date_time_of_current_month - timedelta(seconds=1)
    first_date_time_of_last_month = last_date_time_of_last_month.replace(
        day=1, hour=0, minute=0, second=0, microsecond=0
    )
    return first_date_time_of_last_month, last_date_time_of_last_month


def get_first_day_of_month(date):
    return date.replace(day=1)


def convert_to_US_format(date_str):
    date_object = datetime.strptime(date_str, "%Y-%m-%d")
    new_format = date_object.strftime(REPORT_DATE_FORMAT)
    return new_format


def is_date(string, date_format="%Y-%m-%d"):
    try:
        datetime.strptime(string, date_format)
        return True
    except ValueError:
        return False


def clean_row(row):
    new_row = []
    for item in row:
        if is_date(item):
            new_row.append(convert_to_US_format(item))
        else:
            new_row.append(item)

    return new_row


def write_row(row, output):
    row = clean_row(row)
    output.append(row)


def count_of_20_minutes(record, *args, **kwargs):
    monitoring_count = record.monitoring_minutes // 20
    max_value = kwargs.get("max_value", monitoring_count)
    return min(max_value, monitoring_count)


def count_of_month_reading(record, *args, **kwargs):
    return record.month_reading // 16


def count_of_sign_up(record, *args, **kwargs):
    report_end_date = kwargs.get("report_end_date", None)
    return 1 if record.device_connection_date.month == report_end_date.month else 0


def count_of_basic(record, *args, **kwargs):
    return 1 if count_of_20_minutes(record) and count_of_month_reading(record) else 0


FORMULAS = {
    FORMULA_BASIC: count_of_basic,
    FORMULA_20_MINUTES_MONITORING: count_of_20_minutes,
    FORMULA_MONTH_READING: count_of_month_reading,
    FORMULA_SIGN_UP: count_of_sign_up,
}


def handle_cpt(cpt_code, units):
    if cpt_code not in CptCode:
        raise Exception(f"BAD CPT code (You can't mix CPT with Formula): {cpt_code}")

    cpt_code_unit = units.filter(code=cpt_code, units__gt=0).first()
    return bool(cpt_code_unit)


def interpret_payment_formula(formula, records, invoice, report_end_date):
    for record in records:
        if not record.is_eligible:  # we shouldn't process engage!
            continue
        units = CptChargeUnit.objects.filter(billingRecord=record)

        for item in formula:
            parts = item.split(" and ")
            eligible = True
            count = 0
            if len(parts) > 1 or item in CptCode:
                for part in parts:
                    if not handle_cpt(part, units):
                        eligible = False
                        break
                count = 1
            elif item in FORMULAS:
                count = FORMULAS[item](
                    record, report_end_date=report_end_date, max_value=formula[item].get("max", sys.maxsize)
                )
                if not count:
                    eligible = False
            else:
                eligible = False
                raise Exception(f"BAD Forumula {item}, the choices are {FORMULAS.keys()}")

            if eligible:
                InvoiceEntry.objects.create(
                    patient=record.patient,
                    service_description=formula[item]["description"],
                    unites=count,
                    rates=formula[item]["rate"],
                    parent_invoice=invoice,
                )


def generate_internal_invoices(selected_date):
    clients = Client.objects.all()
    for client in clients:
        payment_formula = client.paymentFormula
        if selected_date:
            records = BillingRecord.objects.filter(report_end_date=selected_date, deployment__client=client)
        else:
            records = BillingRecord.objects.none()

        report_end_date = datetime.strptime(selected_date, "%Y-%m-%d").date()
        with transaction.atomic():
            invoice, created = Invoice.objects.get_or_create(
                invoice_start_date=report_end_date.replace(day=1),
                invoice_end_date=report_end_date,
                client=client,
            )
            if not created:
                continue
            interpret_payment_formula(payment_formula, records, invoice, report_end_date)


def is_valid_99454_3(count_of_submissions_days):
    return count_of_submissions_days >= 16


def clean_fitbit_connection_date(fitbit_connection_date):
    if not fitbit_connection_date:
        fitbit_connection_date = None
    else:
        if isinstance(fitbit_connection_date, str):
            fitbit_connection_date = datetime.strptime(fitbit_connection_date, REPORT_DATE_FORMAT)
        if isinstance(fitbit_connection_date, datetime):
            fitbit_connection_date = fitbit_connection_date.date()
    return fitbit_connection_date


def clean_int_numbers(num):
    return 0 if not num else int(num)


class GenerateReportsUseCase(UseCase):
    @autoparams()
    def __init__(self, auth_repo: AuthorizationRepository, billing_portal_repo: BillingPortalRepository):
        self.auth_repo = auth_repo
        self.billing_portal_repo = billing_portal_repo

    def process_request(self, request_object: GenerateReportsRequestObject):
        file = self.get_last_sales_force_file()
        month_year = request_object.monthYear
        message = ""

        if month_year:
            report_end_date = self.get_last_day_of_month_string(month_year)
            if file.uploadedAt.date() < report_end_date:
                message = "The SalesForce file needs to be updated!"
                raise Exception(message)
            else:
                success, message = self.generate_month_reports(report_end_date)

        return GenerateReportsResponseObject(message=message)

    def get_last_sales_force_file(self):
        return SalesForceFile.objects.all().order_by("-uploadedAt").first()

    def get_last_day_of_month_string(self, month_year):
        year, month = map(int, month_year.split("-"))
        last_day = calendar.monthrange(year, month)[1]
        last_day_date = date(year, month, last_day)
        return last_day_date

    def generate_month_reports(self, report_end_date):
        if not report_end_date:
            report_end_date = get_last_day_of_last_month_string()

        if BillingRecord.objects.filter(report_end_date=report_end_date).first():
            return False, "The report already exists"
        self.generate_billing_reports(report_end_date)

        generate_internal_invoices(report_end_date.strftime("%Y-%m-%d"))  # TODO: temporary

        return True, "Success"  # TODO: use the actual reports to see if it was successful or not!

    def generate_billing_reports(self, report_end_date):
        if not report_end_date:
            report_end_date = get_last_day_of_last_month_string()

        if BillingRecord.objects.filter(report_end_date=report_end_date).first():
            return False, "The report already exists"

        notes_service = ObservationNoteService()
        deployment_service = DeploymentService()

        users_extra_data = read_sales_force_file()

        general_report = [
            CPTGeneralExport.from_dict(model_to_dict(b))
            for b in BillingGeneralCPTCodes.objects.filter(reportDate=report_end_date)
        ]

        for record in general_report:
            user_id = record.userId
            if not users_extra_data.get(user_id, None):
                users_extra_data[user_id] = deepcopy(SALES_FORCE_EMPTY_DICT)
            users_extra_data[user_id]["fitbit_connection_date"] = record.billingFirstReading
            users_extra_data[user_id]["month_reading"] = record.numberOfSubmissionDays

        monthly_report: list[CPTMonthlyExport] = [
            CPTMonthlyExport.from_dict(model_to_dict(b))
            for b in BillingMonthlyCPTCodes.objects.filter(reportEndDate=report_end_date)
        ]

        with transaction.atomic():
            processed_patients = set()
            for record in monthly_report:
                deployment_id = record.deploymentId
                user_id = record.userId
                if user_id in processed_patients:
                    logger.warning(f"Double records for user {user_id} in deployment {deployment_id}, skipping")
                    continue
                processed_patients.add(user_id)

                # TODO: investigate the NOOOO on salesforce
                patient_data = users_extra_data.get(user_id, {})
                patient = self.auth_repo.retrieve_simple_user_profile(user_id=user_id)
                if not patient:
                    logger.warning(f"Patient with user_id {user_id} not found in the system, skipping")
                    continue
                elif not patient.componentsData or not patient.componentsData.get("billing"):
                    logger.warning(f"Patient {user_id} does not have billing data, skipping")
                elif "mrn" not in patient.componentsData["billing"]:
                    logger.warning(f"Patient {user_id} does not have MRN, setting")
                    mrn = patient_data.get(SALES_FORCE_MRN, "")
                    self.billing_portal_repo.set_user_mrn(user_id=user_id, mrn=mrn)

                monitoring_time = clean_int_numbers(record.monitoringTimeMins)
                number_of_calls = clean_int_numbers(record.numberOfCalls)
                billing_record: BillingRecordDTO = BillingRecordDTO(
                    patientId=user_id,
                    provider=patient_data.get(SALES_FORCE_PROVIDER, ""),
                    monitoring_minutes=monitoring_time,
                    number_of_calls=number_of_calls,
                    month_reading=clean_int_numbers(patient_data.get("month_reading", 0)),
                    diagnosis_type=patient_data.get(SALES_FORCE_ICD_10, ""),
                    primary_insurance=patient_data.get(SALES_FORCE_INSURANCE, ""),
                    report_end_date=report_end_date,
                    deploymentId=deployment_id,
                    device_connection_date=clean_fitbit_connection_date(patient_data.get("fitbit_connection_date", "")),
                )
                billing_record.id = self.billing_portal_repo.create_billing_record(billing_record)

                cpt_99457_units = record.cpt99457_units
                cpt_99457_8_date_of_service = record.cpt99457_8_DateOfService

                cpt_99457_unit_obj = CptChargeUnitDTO(
                    code=CptCodeEnum.CPT_99457,
                    units=cpt_99457_units,
                    date_of_service=cpt_99457_8_date_of_service,
                    billingRecordId=billing_record.id,
                    extra_details={
                        "monitoring_time": monitoring_time,
                        "count_of_interactive_calls": number_of_calls,
                    },
                )
                self.billing_portal_repo.create_cpt_charge_unit(cpt_charge_unit=cpt_99457_unit_obj)

                cpt_99458_unit_obj = CptChargeUnitDTO(
                    code=CptCodeEnum.CPT_99458,
                    units=clean_int_numbers(record.cpt99458_units),
                    date_of_service=cpt_99457_8_date_of_service,
                    billingRecordId=billing_record.id,
                )

                self.billing_portal_repo.create_cpt_charge_unit(cpt_charge_unit=cpt_99458_unit_obj)

                iterate_items = [""]
                if not record.cpt99454_CountOfSubmissionDays:
                    iterate_items = ["_0", "_1"]

                for iterate_item in iterate_items:
                    if record.cpt99454(iterate_item).billingStartDate:
                        cpt_99454_unit_obj = CptChargeUnitDTO(
                            code=CptCodeEnum.CPT_99454,
                            units=(
                                1
                                if is_valid_99454_3(
                                    clean_int_numbers(record.cpt99454(iterate_item).countOfSubmissionDays)
                                )
                                else 0
                            ),
                            date_of_service=record.cpt99454(iterate_item).billingEndDate,
                            billingRecordId=billing_record.id,
                            extra_details={
                                "count_of_submission_days": clean_int_numbers(
                                    record.cpt99454(iterate_item).countOfSubmissionDays
                                ),
                                "total_data_submission": clean_int_numbers(
                                    record.cpt99454(iterate_item).countOfSubmissions
                                ),
                                "99454_start_date": record.cpt99454(iterate_item).billingStartDate,
                            },
                        )  # TODO: fix it and replace start_date with 99454 date of service

                        self.billing_portal_repo.create_cpt_charge_unit(cpt_charge_unit=cpt_99454_unit_obj)

                        if record.cpt99453_BillingStartDate == record.cpt99454(iterate_item).billingStartDate:
                            cpt_99453_unit_obj = CptChargeUnitDTO(
                                code=CptCodeEnum.CPT_99453,
                                units=1,
                                date_of_service=cpt_99454_unit_obj.date_of_service,
                                billingRecordId=billing_record.id,
                            )

                            self.billing_portal_repo.create_cpt_charge_unit(cpt_charge_unit=cpt_99453_unit_obj)

        notes_start_date, notes_end_date = get_time_range_of_last_month()

        for huma_id in processed_patients:
            patient_notes, count = notes_service.retrieve_user_notes(
                deployment_id=deployment_id,
                user_id=huma_id,
                start_date=notes_start_date,
                end_date=notes_end_date,
                skip=0,
                limit=1000,
            )

            billing_record = self.billing_portal_repo.retrieve_patient_billing_record(
                user_id=huma_id,
                report_end_date=report_end_date,
            )
            if not billing_record:
                print(f"No billing record found for patient {huma_id} and report end date {report_end_date}")
                continue

            consents = deployment_service.retrieve_consent_logs(huma_id)
            consent: ConsentLogDTO = None if not consents else consents[0]

            billing_record.consent_date = consent.createDateTime if consent else None
            billing_record.consent_id = consent.id if consent else None

            billing_record.device_type = "Fitbit"
            billing_record.device_model = "Inspire 3"  # todo: is this always correct?
            self.billing_portal_repo.update_billing_record(billing_record)

        logger.info(f"Billing report generated for {report_end_date}")
        return None
        # TODO: Add vitals and notes later on generating the medical record of the patient
