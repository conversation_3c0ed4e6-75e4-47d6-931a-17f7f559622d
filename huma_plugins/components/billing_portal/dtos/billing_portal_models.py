from datetime import datetime
from enum import Enum

from sdk.authorization.dtos.user import UserDTO
from sdk.common.utils.convertible import convertibleclass, default_field
from sdk.common.utils.huconvertible import HuConvertible
from sdk.deployment.dtos.deployment import DeploymentDTO


@convertibleclass
class ClientDTO:
    ID = "id"
    NAME = "name"
    PAYMENT_FORMULA = "paymentFormula"

    id: str = default_field()
    name: str = default_field()
    paymentFormula: dict = default_field()


@convertibleclass
class BillingRecordDTO:
    id: str = default_field()
    deployment: DeploymentDTO = default_field()
    deploymentId: str = default_field()
    patient: UserDTO = default_field()
    patientId: str = default_field()
    provider: str = default_field()
    diagnosis_type: str = default_field()
    device_connection_date: str = default_field()
    primary_insurance: str = default_field()
    report_end_date: str = default_field()
    is_eligible: bool = default_field()
    monitoring_minutes: int = default_field(default=0)
    number_of_calls: int = default_field(default=0)
    month_reading: int = default_field(default=0)
    consent_date: datetime = default_field()
    consent_id: str = default_field()
    device_type: str = default_field()
    device_model: str = default_field()


class CptCodeEnum(Enum):
    CPT_99453 = "99453"
    CPT_99454 = "99454"
    CPT_99457 = "99457"
    CPT_99458 = "99458"

    @classmethod
    def choices(cls):
        return [(item.value, item.name) for item in cls]


@convertibleclass
class CptChargeUnitDTO:
    id: str = default_field()
    code: CptCodeEnum = default_field()
    units: int = default_field()
    date_of_service: str = default_field()
    extra_details: dict = default_field()
    billingRecord: BillingRecordDTO = default_field()
    billingRecordId: str = default_field()


class CPTGeneralExport(HuConvertible):
    userId: str = default_field()
    deploymentId: str = default_field()
    reportDate: datetime = default_field()
    billingFirstReading: datetime = default_field()
    billingProviderName: str = default_field()
    billingDiagnosisDescription: str = default_field()
    billingDiagnosisICD10Code: str = default_field()
    billingDiagnosisOrder: int = default_field()
    primaryInsuranceCarrier: str = default_field()
    primaryInsuranceCarrierPayerId: str = default_field()
    secondaryInsuranceCarrier: str = default_field()
    secondaryInsuranceCarrierPayerId: str = default_field()
    cpt98975: int = default_field()
    cpt98976: int = default_field()
    cpt98980: int = default_field()
    cpt98981: int = default_field()
    cpt99453: int = default_field()
    cpt99454: int = default_field()
    cpt99457: int = default_field()
    cpt99458: int = default_field()
    monitoringTimeMinutes: int = default_field()
    numberOfCalls: int = default_field()
    numberOfSubmissionDays: int = default_field()
    numberOfSubmissions: int = default_field()

    @classmethod
    def from_export_dict(cls, export_dict: dict):
        """export_dict structure is as follows:
        {
        "billing": {
          "billingFirstReading": "",
          "billingProviderName": "someName",
          "diagnosis": {
            "description": "diagnosis description",
            "icd10Code": "ICD",
            "order": 1
          },
          "primaryInsuranceCarrier": "",
          "primaryInsuranceCarrierPayerId": "",
          "secondaryInsuranceCarrier": "",
          "secondaryInsuranceCarrierPayerId": ""
        },
        "deploymentId": "5d386cc6ff885918d96edb2c",
        "moduleId": "billing",
        "report": {
          "cpt98975": 0,
          "cpt98976": 0,
          "cpt98980": 0,
          "cpt98981": 0,
          "cpt99453": 0,
          "cpt99454": 0,
          "cpt99457": 0,
          "cpt99458": 0,
          "monitoringTimeMins": 0,
          "numberOfCalls": 0,
          "numberOfSubmissionDays": 0,
          "numberOfSubmissions": 0
        },
        "user": {
          "biologicalSex": "",
          "dateOfBirth": "1988-02-20",
          "email": "<EMAIL>",
          "familyName": "test",
          "gender": "",
          "givenName": "test",
          "language": "",
          "phoneNumber": "+380999999999",
          "primaryAddress": "",
          "timezone": "UTC"
        },
        "userId": "5e8f0c74b50aa9656c34789b"
        }
        """
        billing = export_dict.get("billing", {})
        report = export_dict.get("report", {})

        return cls(
            userId=export_dict.get("userId"),
            deploymentId=export_dict.get("deploymentId"),
            reportDate=export_dict.get("reportDate"),
            billingFirstReading=billing.get("billingFirstReading"),
            billingProviderName=billing.get("billingProviderName"),
            billingDiagnosisDescription=billing.get("diagnosis", {}).get("description"),
            billingDiagnosisICD10Code=billing.get("diagnosis", {}).get("icd10Code"),
            billingDiagnosisOrder=billing.get("diagnosis", {}).get("order"),
            primaryInsuranceCarrier=billing.get("primaryInsuranceCarrier"),
            primaryInsuranceCarrierPayerId=billing.get("primaryInsuranceCarrierPayerId"),
            secondaryInsuranceCarrier=billing.get("secondaryInsuranceCarrier"),
            secondaryInsuranceCarrierPayerId=billing.get("secondaryInsuranceCarrierPayerId"),
            cpt98975=report.get("cpt98975", 0),
            cpt98976=report.get("cpt98976", 0),
            cpt98980=report.get("cpt98980", 0),
            cpt98981=report.get("cpt98981", 0),
            cpt99453=report.get("cpt99453", 0),
            cpt99454=report.get("cpt99454", 0),
            cpt99457=report.get("cpt99457", 0),
            cpt99458=report.get("cpt99458", 0),
            monitoringTimeMinutes=report.get("monitoringTimeMins", 0),
            numberOfCalls=report.get("numberOfCalls", 0),
            numberOfSubmissionDays=report.get("numberOfSubmissionDays", 0),
            numberOfSubmissions=report.get("numberOfSubmissions", 0),
        )


class CPT99454(HuConvertible):
    billingStartDate: str = default_field()
    billingEndDate: str = default_field()
    countOfSubmissions: int = default_field()
    countOfSubmissionDays: int = default_field()
    earliestBillingDate: str = default_field()


class CPTMonthlyExport(HuConvertible):
    """
      {
      "userId": "5e8f0c74b50aa9656c34789b",
      "deploymentId": "5d386cc6ff885918d96edb2c",
      "moduleId": "billing",
      "reportStartDate": "2022-06-01",
      "reportEndDate": "2022-06-30",
      "user": {
        "givenName": "test",
        "familyName": "test",
        "dateOfBirth": "1988-02-20"
      },
      "billing": {
        "cpt99453_4": {
          "billingProviderName": "",
          "billingProviderId": "",
          "billingProviderAbbreviation": ""
        },
        "cpt99457_8": {
          "billingProviderName": "",
          "billingProviderId": "",
          "billingProviderAbbreviation": ""
        },
        "icd10Code": "ICD",
        "description": "diagnosis description",
        "billingFirstReading": "2022-05-10",
        "primaryInsuranceCarrier": "",
        "secondaryInsuranceCarrier": "",
        "primaryInsuranceCarrierPayerId": "",
        "secondaryInsuranceCarrierPayerId": ""
      },
      "cptCodesDetails": {
        "cpt99453": {
          "billingStartDate": "2022-05-10",
          "billingEndDate": "2022-06-08",
          "countOfSubmissions": 21,
          "countOfSubmissionDays": 21,
          "earliestBillingDate": "2022-05-25"
        },
        "cpt99454": {
          "billingStartDate_0": "2022-05-10",
          "billingEndDate_0": "2022-06-08",
          "countOfSubmissions_0": 21,
          "countOfSubmissionDays_0": 21,
          "earliestBillingDate_0": "2022-05-25",
          "billingStartDate_1": "",
          "billingEndDate_1": "",
          "countOfSubmissions_1": "",
          "countOfSubmissionDays_1": "",
          "earliestBillingDate_1": ""
        },
        "cpt99457": {
          "billingStartDate": "2022-06-01",
          "billingEndDate": "2022-06-30",
          "totalBillable": 0
        },
        "cpt99458": {
          "billingStartDate": "2022-06-01",
          "billingEndDate": "2022-06-30",
          "totalBillable": 0
        },
        "monitoringTimeMins": 0,
        "numberOfCalls": 0
      }
    }
    """

    userId: str = default_field()
    deploymentId: str = default_field()
    moduleId: str = default_field()
    reportStartDate: str = default_field()
    reportEndDate: str = default_field()
    givenName: str = default_field()
    familyName: str = default_field()
    dateOfBirth: str = default_field()
    monitoringTimeMins: int = default_field()
    numberOfCalls: int = default_field()
    cpt99457_units: int = default_field()
    cpt99458_units: int = default_field()
    cpt99457_8_DateOfService: str = default_field()

    cpt99453_4_CountOfSubmissionDays: int = default_field()

    cpt99453_BillingStartDate: str = default_field()
    cpt99453_BillingEndDate: str = default_field()
    cpt99453_CountOfSubmissions: int = default_field()
    cpt99453_EarliestBillingDate: str = default_field()
    cpt99454_BillingStartDate: str = default_field()
    cpt99454_BillingEndDate: str = default_field()
    cpt99454_CountOfSubmissions: int = default_field()
    cpt99454_CountOfSubmissionDays: int = default_field()
    cpt99454_EarliestBillingDate: str = default_field()

    cpt99454_BillingStartDate_0: str = default_field()
    cpt99454_BillingEndDate_0: str = default_field()
    cpt99454_CountOfSubmission_0: int = default_field()
    cpt99454_CountOfSubmissionDays_0: int = default_field()
    cpt99454_EarliestBillingDate_0: str = default_field()

    cpt99454_BillingStartDate_1: str = default_field()
    cpt99454_BillingEndDate_1: str = default_field()
    cpt99454_CountOfSubmission_1: int = default_field()
    cpt99454_CountOfSubmissionDays_1: int = default_field()
    cpt99454_EarliestBillingDate_1: str = default_field()

    def cpt99454(self, iteration: str = "") -> CPT99454:
        return CPT99454.from_dict(
            {
                "billingStartDate": getattr(self, f"cpt99454_BillingStartDate{iteration}"),
                "billingEndDate": getattr(self, f"cpt99454_BillingEndDate{iteration}"),
                "countOfSubmissions": getattr(self, f"cpt99454_CountOfSubmission{iteration}"),
                "countOfSubmissionDays": getattr(self, f"cpt99454_CountOfSubmissionDays{iteration}"),
                "earliestBillingDate": getattr(self, f"cpt99454_EarliestBillingDate{iteration}"),
            }
        )

    @classmethod
    def from_export_dict(cls, export_dict: dict):
        """export_dict structure is as follows:"""
        user = export_dict.get("user", {})
        cpt_codes_details = export_dict.get("cptCodesDetails", {})
        cpt99453 = cpt_codes_details.get("cpt99453", {})
        cpt99454 = cpt_codes_details.get("cpt99454", {})
        cpt99457 = cpt_codes_details.get("cpt99457", {})
        cpt99458 = cpt_codes_details.get("cpt99458", {})

        return cls(
            userId=export_dict.get("userId"),
            deploymentId=export_dict.get("deploymentId"),
            moduleId=export_dict.get("moduleId"),
            reportStartDate=export_dict.get("reportStartDate"),
            reportEndDate=export_dict.get("reportEndDate"),
            givenName=user.get("givenName"),
            familyName=user.get("familyName"),
            dateOfBirth=user.get("dateOfBirth"),
            monitoringTimeMins=cpt_codes_details.get("monitoringTimeMins", 0),
            numberOfCalls=cpt_codes_details.get("numberOfCalls", 0),
            cpt99457_units=cpt99457.get("totalBillable", 0),
            cpt99458_units=cpt99458.get("totalBillable", 0),
            cpt99457_8_DateOfService=cpt99458.get("billingEndDate"),
            cpt99453_4_CountOfSubmissionDays=cpt99453.get("countOfSubmissionDays", 0),
            cpt99453_BillingStartDate=cpt99453.get("billingStartDate"),
            cpt99453_BillingEndDate=cpt99453.get("billingEndDate"),
            cpt99453_CountOfSubmissions=cpt99453.get("countOfSubmissions", 0),
            cpt99453_EarliestBillingDate=cpt99453.get("earliestBillingDate"),
            cpt99454_BillingStartDate=cpt99454.get("billingStartDate"),
            cpt99454_BillingEndDate=cpt99454.get("billingEndDate"),
            cpt99454_CountOfSubmissions=cpt99454.get("countOfSubmissions", 0),
            cpt99454_CountOfSubmissionDays=cpt99454.get("countOfSubmissionDays", 0),
            cpt99454_EarliestBillingDate=cpt99454.get("earliestBillingDate"),
            cpt99454_BillingStartDate_0=cpt99454.get("billingStartDate_0"),
            cpt99454_BillingEndDate_0=cpt99454.get("billingEndDate_0"),
            cpt99454_CountOfSubmission_0=cpt99454.get("countOfSubmissions_0", 0),
            cpt99454_CountOfSubmissionDays_0=cpt99454.get("countOfSubmissionDays_0", 0),
            cpt99454_EarliestBillingDate_0=cpt99454.get("earliestBillingDate_0"),
            cpt99454_BillingStartDate_1=cpt99454.get("billingStartDate_1"),
            cpt99454_BillingEndDate_1=cpt99454.get("billingEndDate_1"),
            cpt99454_CountOfSubmission_1=cpt99454.get("countOfSubmissions_1", 0),
            cpt99454_CountOfSubmissionDays_1=cpt99454.get("countOfSubmissionDays_1", 0),
            cpt99454_EarliestBillingDate_1=cpt99454.get("earliestBillingDate_1"),
        )
