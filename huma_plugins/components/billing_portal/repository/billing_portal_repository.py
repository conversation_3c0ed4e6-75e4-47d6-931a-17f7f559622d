from abc import ABC, abstractmethod
from datetime import datetime

from huma_plugins.components.billing_portal.dtos.billing_portal_models import (
    BillingRecordDTO,
    CPTGeneralExport,
    CPTMonthlyExport,
    ClientDTO,
    CptChargeUnitDTO,
)
from huma_plugins.components.billing_portal.repository.models import LatestDeployment


class BillingPortalRepository(ABC):
    """This is the base repository class for keeping track of users' CPT codes."""

    @abstractmethod
    def add_user_general_cpt_code(self, billing: CPTGeneralExport):
        raise NotImplementedError

    @abstractmethod
    def get_user_general_cpt_code(self, user_id: str, report_date: datetime.date):
        raise NotImplementedError

    @abstractmethod
    def get_latest_deployments_for_general_cpt_code(self) -> list[LatestDeployment]:
        """
        Queries the latest deploymentIds in the BillingGeneralCPTCodes table, for the current month.
        If a deploymentId has multiple reportDates, it will return the one with the latest reportDate.
        """
        raise NotImplementedError

    @abstractmethod
    def add_user_monthly_cpt_code(self, billing: CPTMonthlyExport):
        raise NotImplementedError

    @abstractmethod
    def create_client(self, client: ClientDTO) -> str:
        raise NotImplementedError

    @abstractmethod
    def update_client(self, client: ClientDTO) -> str:
        raise NotImplementedError

    @abstractmethod
    def retrieve_clients(self) -> list[ClientDTO]:
        raise NotImplementedError

    @abstractmethod
    def set_user_mrn(self, user_id: str, mrn: str):
        raise NotImplementedError

    @abstractmethod
    def create_billing_record(self, record: BillingRecordDTO) -> str:
        raise NotImplementedError

    @abstractmethod
    def retrieve_patient_billing_record(self, user_id: str, report_end_date: datetime.date) -> BillingRecordDTO | None:
        raise NotImplementedError

    @abstractmethod
    def update_billing_record(self, record: BillingRecordDTO) -> str:
        raise NotImplementedError

    @abstractmethod
    def create_cpt_charge_unit(self, cpt_charge_unit: CptChargeUnitDTO) -> str:
        raise NotImplementedError

    @abstractmethod
    def add_deployment_to_client(self, client_id: str, deployment_id: str) -> str:
        raise NotImplementedError

    @abstractmethod
    def update_deployment_client(self, client_id: str, deployment_id: str):
        raise NotImplementedError

    @abstractmethod
    def delete_deployment_client(self, client_id, deployment_id):
        raise NotImplementedError
