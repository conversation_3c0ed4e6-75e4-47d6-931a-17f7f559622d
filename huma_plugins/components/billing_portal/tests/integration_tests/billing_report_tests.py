import json
from io import Bytes<PERSON>
from pathlib import Path
from unittest.mock import MagicMock, patch

import i18n
from flask import url_for
from freezegun import freeze_time

from huma_plugins.components.billing_portal.component import BillingPortalComponent
from huma_plugins.components.billing_portal.models import Client, Deployment, SalesForceFile
from huma_plugins.components.billing_portal.router.billing_portal_requests import AddClientRequestObject
from huma_plugins.components.export.tests.IntegrationTests import enabled_modules
from huma_plugins.components.extended_module_result.component import ExtendedModuleResultComponent
from huma_plugins.tests.plugin_test_case import ExtensionServerConfig, PluginsTestCase
from huma_plugins.tests.shared import PLUGIN_CONFIG_PATH
from sdk.auth.component import AuthComponent
from sdk.authorization.component import AuthorizationComponent
from sdk.deployment.component import DeploymentComponent
from sdk.organization.component import OrganizationComponent
from sdk.storage.component import StorageComponentV1
from sdk.versioning.component import VersionComponent

ORGANIZATION_ID = "5fde855f12db509a2785da06"
DEPLOYMENT_ID = "5d386cc6ff885918d96edb2c"
DUMMY_DEPLOYMENT_ID = "5d386cc6ff885918d96eeeee"
ORG_ADMIN_ID = "5e8f0c74b50aa9656c34789c"
USER_ID = "5e8f0c74b50aa9656c34789d"
CLIENT_ID = "5fde855f12db509a2785da12"


class BillingPortalIntegrationTest(PluginsTestCase):
    config_file_path = PLUGIN_CONFIG_PATH
    db_migration_path = ""
    config_class = ExtensionServerConfig
    fixtures = [Path(__file__).parent.joinpath("fixtures").joinpath("organization_dump.json")]

    components = [
        AuthComponent(),
        VersionComponent(server_version="1.0.0", api_version="1.0.0"),
        AuthorizationComponent(),
        ExtendedModuleResultComponent(
            additional_modules=[m() for m in enabled_modules],
        ),
        StorageComponentV1(),
        DeploymentComponent(),
        OrganizationComponent(),
        BillingPortalComponent(),
    ]

    def setUp(self):
        super().setUp()
        self.client = self.flask_client
        self.user_headers = self.get_headers_for_token(USER_ID)
        self.user_headers["User-Agent"] = "test-agent"
        self.org_admin_headers = self.get_headers_for_token(ORG_ADMIN_ID)
        self.org_admin_headers["User-Agent"] = "web-agent"

    def tearDown(self):
        Client.objects.all().delete()
        SalesForceFile.objects.all().delete()
        Deployment.objects.all().delete()
        super().tearDown()

    @classmethod
    def tearDownClass(cls):
        i18n.translations.container = {}
        i18n.load_path.clear()

    def test_get_clients(self):
        endpoint_url = url_for("billing_portal_route.get_clients")
        response = self.client.get(
            endpoint_url,
            headers={**self.org_admin_headers, "X-ORG-ID": ORGANIZATION_ID},
        )
        self.assertEqual(200, response.status_code)

    def test_add_client(self):
        endpoint_url = url_for("billing_portal_route.add_client")
        payload = {
            AddClientRequestObject.NAME: "Test Client",
            AddClientRequestObject.PAYMENT_FORMULA: "EMBEDED_FURMULA_99454_20minutes",
        }

        response = self.client.post(
            endpoint_url,
            json=payload,
            headers={**self.org_admin_headers, "X-ORG-ID": ORGANIZATION_ID},
        )

        self.assertEqual(201, response.status_code)
        self.assertIn("id", response.json)

        client_id = response.json["id"]
        client = Client.objects.get(mongoId=client_id)
        self.assertEqual(client.name, payload[AddClientRequestObject.NAME])
        payment_formula = client.paymentFormula

        self.assertEqual(payment_formula["formula"], payload[AddClientRequestObject.PAYMENT_FORMULA])
        self.assertEqual(payment_formula["rate"], 0)

    def test_add_client_with_json_payment_formula(self):
        endpoint_url = url_for("billing_portal_route.add_client")
        payment_formula = {
            "formula": "EMBEDED_FURMULA_99454_20minutes",
            "rate": 100,
        }

        payload = {
            AddClientRequestObject.NAME: "Test Client",
            AddClientRequestObject.PAYMENT_FORMULA: json.dumps(payment_formula),
        }
        response = self.client.post(
            endpoint_url,
            json=payload,
            headers={**self.org_admin_headers, "X-ORG-ID": ORGANIZATION_ID},
        )
        self.assertEqual(201, response.status_code)
        self.assertIn("id", response.json)
        client_id = response.json["id"]
        client = Client.objects.get(mongoId=client_id)
        self.assertEqual(client.name, payload[AddClientRequestObject.NAME])
        self.assertEqual(payment_formula["formula"], client.paymentFormula["formula"])

    @patch("huma_plugins.components.billing_portal.dtos.billing_portal_models.CPTMonthlyExport.from_dict")
    @patch("huma_plugins.components.billing_portal.models.BillingMonthlyCPTCodes.objects.filter")
    @patch("huma_plugins.components.billing_portal.models.BillingGeneralCPTCodes.objects.filter")
    @patch("huma_plugins.components.billing_portal.models.Deployment.objects.all")
    @patch("huma_plugins.components.billing_portal.use_case.utils.salesforce.download_file_id")
    def test_generate_reports(
        self,
        mock_download_file_id,
        mock_deployment_all,
        mock_general_filter,
        mock_monthly_filter,
        mock_from_dict,
    ):
        mock_download_file_id.return_value = MagicMock(content=open(Path(__file__).parent / "test_file.csv", "rb"))

        # Create a mock CPTMonthlyExport object
        mock_monthly_export = MagicMock()
        mock_monthly_export.userId = USER_ID
        mock_monthly_export.deploymentId = DEPLOYMENT_ID
        mock_monthly_export.moduleId = "billing"
        mock_monthly_export.reportStartDate = "2023-05-01"
        mock_monthly_export.reportEndDate = "2023-05-31"
        mock_monthly_export.givenName = "Test"
        mock_monthly_export.familyName = "User"
        mock_monthly_export.dateOfBirth = "1990-01-01"
        mock_monthly_export.monitoringTimeMins = 30
        mock_monthly_export.numberOfCalls = 2
        mock_monthly_export.cpt99457_units = 1
        mock_monthly_export.cpt99458_units = 1
        mock_monthly_export.cpt99457_8_DateOfService = "2023-05-15"
        mock_monthly_export.cpt99454_BillingStartDate = "2023-05-01"
        mock_monthly_export.cpt99454_BillingEndDate = "2023-05-31"
        mock_monthly_export.cpt99454_CountOfSubmissions = 20
        mock_monthly_export.cpt99454_CountOfSubmissionDays = 15
        mock_monthly_export.cpt99453_BillingStartDate = "2023-05-01"

        # Create a mock CPTGeneralExport object
        mock_general_export = MagicMock()
        mock_general_export.userId = USER_ID
        mock_general_export.billingFirstReading = "2023-05-01"
        mock_general_export.numberOfSubmissionDays = 20

        # Set up the cpt99454 method to return a mock object with the required attributes
        cpt99454_result = MagicMock()
        cpt99454_result.billingStartDate = "2023-05-01"
        cpt99454_result.billingEndDate = "2023-05-31"
        cpt99454_result.countOfSubmissions = 20
        cpt99454_result.countOfSubmissionDays = 15
        mock_monthly_export.cpt99454.return_value = cpt99454_result

        # Set up the mock from_dict to return appropriate mock objects based on input
        def from_dict_side_effect(x):
            if hasattr(x, "get") and x.get("reportDate"):
                return mock_general_export
            return mock_monthly_export

        mock_from_dict.side_effect = from_dict_side_effect

        # Mock the BillingMonthlyCPTCodes.objects.filter to return a list with a mock object
        # This ensures that the monthly_report list is not empty and the for loop is executed
        mock_billing_monthly = MagicMock()
        mock_monthly_filter.return_value = [mock_billing_monthly]

        # Mock the BillingGeneralCPTCodes.objects.filter to return a list with a mock object
        # This ensures that the general_report list is not empty
        mock_billing_general = MagicMock()
        mock_general_filter.return_value = [mock_billing_general]

        mock_deployment = MagicMock()
        mock_deployment.name = "Test Deployment"
        mock_deployment_filter = MagicMock()
        mock_deployment_filter.filter.return_value.first.return_value = mock_deployment
        mock_deployment_all.return_value = mock_deployment_filter

        with freeze_time("2023-06-13T10:00:00.000Z"):
            self._upload_sales_force_file()
            endpoint_url = url_for("billing_portal_route.generate_reports")
            params = {"monthYear": "2023-05"}
            response = self.client.get(
                endpoint_url,
                query_string=params,
                headers={**self.get_headers_for_token(ORG_ADMIN_ID), "X-ORG-ID": ORGANIZATION_ID},
            )
            self.assertEqual(200, response.status_code)

            mock_monthly_filter.assert_called_once()
            mock_general_filter.assert_called_once()

            mock_from_dict.assert_called()

    def test_get_sales_force_file_form(self):
        endpoint_url = url_for("billing_portal_route.get_sales_force_file_form")
        response = self.client.get(
            endpoint_url,
            headers={**self.org_admin_headers, "X-ORG-ID": ORGANIZATION_ID},
        )
        self.assertEqual(200, response.status_code)

    @freeze_time("2023-06-13T10:00:00.000Z")
    def test_add_sales_force_file(self):
        endpoint_url = url_for("billing_portal_route.add_sales_force_file")

        # Test with no file
        response_no_file = self.client.post(
            endpoint_url,
            headers={**self.get_headers_for_token(ORG_ADMIN_ID), "X-ORG-ID": ORGANIZATION_ID},
        )
        self.assertEqual(200, response_no_file.status_code)

        # Test with an empty file name
        response_empty_filename = self.client.post(
            endpoint_url,
            data={"file": (BytesIO(b""), "")},
            headers={**self.get_headers_for_token(ORG_ADMIN_ID), "X-ORG-ID": ORGANIZATION_ID},
            content_type="multipart/form-data",
        )
        self.assertEqual(200, response_empty_filename.status_code)

        response = self._upload_sales_force_file()
        self.assertEqual(200, response.status_code)
        self.assertEqual(1, SalesForceFile.objects.count())

    def _upload_sales_force_file(self, file_path=Path(__file__).parent / "test_file.csv"):
        endpoint_url = url_for("billing_portal_route.add_sales_force_file")
        with (
            open(Path(__file__).parent / file_path, "rb") as f,
            patch(
                "huma_plugins.components.billing_portal.use_case.add_sales_force_file_use_case.UploadFileUseCaseV1.execute",
            ) as mock_upload_file,
        ):
            mock_upload_file.return_value = MagicMock(id="mock_file_id")
            response = self.client.post(
                endpoint_url,
                data={"file": (f, file_path), "versionNumber": "1.0", "uploadedAt": "2023-06-13T10:00:00.000Z"},
                headers={**self.get_headers_for_token(ORG_ADMIN_ID), "X-ORG-ID": ORGANIZATION_ID},
                content_type="multipart/form-data",
            )
        self.assertEqual(200, response.status_code)
        return response

    @staticmethod
    def _create_sample_client(
        client_id=CLIENT_ID, name="Test Client", payment_formula="EMBEDED_FURMULA_99454_20minutes"
    ):
        client = Client(
            mongoId=client_id,
            name=name,
            paymentFormula={
                "formula": payment_formula,
                "rate": 0,
            },
        )
        client.save()
        return client

    def test_add_client_deployment(self):
        self._create_sample_client()
        endpoint_url = url_for("billing_portal_route.add_deployment_client")
        payload = {
            "clientId": CLIENT_ID,
            "deploymentId": DUMMY_DEPLOYMENT_ID,
        }
        response = self.client.post(
            endpoint_url,
            json=payload,
            headers={**self.org_admin_headers, "X-ORG-ID": ORGANIZATION_ID},
        )
        self.assertEqual(201, response.status_code)
        self.assertIn("id", response.json)
        deployment_id = response.json["id"]
        client_deployment = Deployment.objects.get(mongoId=deployment_id, client__mongoId=payload["clientId"])
        self.assertIsNotNone(client_deployment)

    def test_update_client_deployment(self):
        self._create_sample_client()
        endpoint_url = url_for("billing_portal_route.update_deployment_client")

        # There is a client deployment to update, with deployment_id as DEPLOYMENT_ID
        payload = {
            "clientId": CLIENT_ID,
            "deploymentId": DEPLOYMENT_ID,
        }
        response = self.client.put(
            endpoint_url,
            json=payload,
            headers={**self.org_admin_headers, "X-ORG-ID": ORGANIZATION_ID},
        )
        self.assertEqual(200, response.status_code)
