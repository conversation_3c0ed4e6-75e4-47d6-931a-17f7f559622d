{"deployment": [{"_id": {"$oid": "5d386cc6ff885918d96edb2c"}, "name": "Simple deployment", "status": "DRAFT", "color": "0x007AFF", "moduleConfigs": [], "learn": {"id": {"$oid": "5e8eeae1b707216625ca4202"}, "updateDateTime": {"$date": {"$numberLong": "1586433217042"}}, "createDateTime": {"$date": {"$numberLong": "1586433217042"}}}, "updateDateTime": {"$date": {"$numberLong": "1586433217042"}}, "createDateTime": {"$date": {"$numberLong": "1586433217042"}}}], "organization": [{"_id": {"$oid": "5fde855f12db509a2785da06"}, "name": "Simple organization", "enrollmentTarget": 3000, "studyCompletionTarget": 2800, "status": "DEPLOYED", "deploymentIds": ["5d386cc6ff885918d96edb2c"], "targetConsented": 0, "eulaUrl": "https://some_url.com/eulaUrl", "termAndConditionUrl": "https://example.com/termAndConditionUrl", "privacyPolicyUrl": "https://example.com/privacyPolicyUrl"}], "huma_auth_user": [{"_id": {"$oid": "5e8f0c74b50aa9656c34789c"}, "status": 1, "emailVerified": true, "email": "<EMAIL>", "phoneNumber": "+380999999999", "displayName": "test", "userAttributes": {"familyName": "test", "givenName": "test", "dob": "1988-02-20", "gender": "MALE"}, "createDateTime": {"$date": {"$numberInt": "1586422340"}}, "updateDateTime": {"$date": {"$numberInt": "1586422340"}}}, {"_id": {"$oid": "5e8f0c74b50aa9656c34789d"}, "status": 1, "emailVerified": true, "email": "<EMAIL>", "phoneNumber": "+380999999990", "displayName": "test", "userAttributes": {"familyName": "test", "givenName": "test", "dob": "1988-02-20", "gender": "MALE"}, "createDateTime": {"$date": {"$numberInt": "1586422340"}}, "updateDateTime": {"$date": {"$numberInt": "1586422340"}}}], "user": [{"_id": {"$oid": "5e8f0c74b50aa9656c34789c"}, "givenName": "test", "familyName": "test", "email": "<EMAIL>", "phoneNumber": "+380999999999", "roles": [{"roleId": "OrganizationOwner", "resource": "organization/5fde855f12db509a2785da06", "userType": "Admin", "isActive": true}], "timezone": "UTC"}, {"_id": {"$oid": "5e8f0c74b50aa9656c34789d"}, "givenName": "test", "familyName": "test", "email": "<EMAIL>", "phoneNumber": "+380999999990", "roles": [{"roleId": "User", "resource": "deployment/5d386cc6ff885918d96edb2c", "userType": "User", "isActive": true}], "timezone": "Asia/Bangkok", "lastLoginDateTime": "2022-01-07T14:59:49.275750Z", "componentsData": {"billing": {"status": 1, "billingProviderName": "someName", "diagnosis": {"order": 1, "icd10Code": "ICD", "description": "diagnosis description"}}}}], "billing_client_deployment": [{"_id": {"$oid": "5d386cc6ff885918d96edb2c"}, "name": "Simple deployment", "client_id": "6c386cc6ff885918d96eff2d"}]}