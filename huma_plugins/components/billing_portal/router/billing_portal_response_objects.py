import io

from huma_plugins.components.billing_portal.dtos.billing_portal_models import ClientDTO
from sdk import convertibleclass
from sdk.common.usecase.response_object import Response
from sdk.common.utils.convertible import default_field


@convertibleclass
class GetClientsResponseObject(Response):
    clients: list[ClientDTO] = default_field()


@convertibleclass
class DownloadClientsResponseObject(Response):
    downloadResponse: io.BytesIO = default_field()
    filename: str = default_field()


@convertibleclass
class AddClientsResponseObject(Response):
    form: dict = default_field()
    message: str = default_field()


@convertibleclass
class GenerateReportsResponseObject(Response):
    message: str = default_field()


@convertibleclass
class AddSalesForceFileResponseObject(Response):
    id: int = default_field()
    message: str = default_field()
