from io import Bytes<PERSON>

from flask import g, render_template

from huma_plugins.components.billing_portal.router.billing_portal_requests import (
    AddClientDeploymentRequestObject,
    AddClientRequestObject,
    AddSalesForceFileRequestObject,
    GenerateReportsRequestObject,
    UpdateClientDeploymentRequestObject,
)
from huma_plugins.components.billing_portal.tasks import generate_billing_cpt_codes_report
from huma_plugins.components.billing_portal.use_case import (
    AddClientUseCase,
    GenerateReportsUseCase,
    GetClientsFormUseCase,
    GetClientsUseCase,
)
from huma_plugins.components.billing_portal.use_case.add_sales_force_file_use_case import AddSalesForceFileUseCase
from huma_plugins.components.billing_portal.use_case.client_deployment_use_case import (
    AddClientDeploymentUseCase,
    DeleteClientDeploymentUseCase,
    UpdateClientDeploymentUseCase,
)
from huma_plugins.components.billing_portal.use_case.get_sales_force_file_form_use_case import (
    GetSalesForceFileFormUseCase,
)
from sdk.common.usecase.request_object import RequestObject
from sdk.common.usecase.response_object import OkResponse, ResultIdResponseObject
from sdk.security import Access, ProtectedBlueprint

api = ProtectedBlueprint(
    "billing_portal_route", __name__, url_prefix="/api/v1/billing-portal", template_folder="../templates"
)


@api.get("/")
@api.requires(Access.ORG.EDIT)
def get_clients():
    """
    Get clients for the Billing Portal.
    Responsible for rendering the client's page.
    """
    request_object = RequestObject()
    response = GetClientsUseCase().execute(request_object)

    context = {
        "clients": response.clients,
    }
    return render_template("clients.html", **context)


@api.post("/deployment-client/")
@api.requires(Access.ORG.EDIT)
@api.input(AddClientDeploymentRequestObject.Schema, location="json", arg_name="request_object")
@api.output(ResultIdResponseObject.Schema, 201)
def add_deployment_client(request_object: AddClientDeploymentRequestObject):
    """
    Add a deployment to a client for the Billing Portal.
    """
    return AddClientDeploymentUseCase().execute(request_object=request_object)


@api.put("/deployment-client/")
@api.requires(Access.ORG.EDIT)
@api.input(UpdateClientDeploymentRequestObject.Schema, location="json", arg_name="request_object")
@api.output(OkResponse.Schema, 200)
def update_deployment_client(request_object: UpdateClientDeploymentRequestObject):
    """
    Update a deployment for a client for the Billing Portal.
    """
    UpdateClientDeploymentUseCase().execute(request_object=request_object)

    return OkResponse()


@api.delete("/deployment-client/")
@api.requires(Access.ORG.EDIT)
@api.input(UpdateClientDeploymentRequestObject.Schema, location="json", arg_name="request_object")
@api.output(OkResponse.Schema, 200)
def delete_deployment_client(request_object: UpdateClientDeploymentRequestObject):
    """
    Delete a deployment for a client for the Billing Portal.
    """
    DeleteClientDeploymentUseCase().execute(request_object=request_object)

    return OkResponse()


@api.get("/clients/")
@api.requires(Access.ORG.EDIT)
def get_clients_form():
    """
    Display the form for adding clients for the Billing Portal.
    """
    response = GetClientsFormUseCase().execute(RequestObject())
    context = {"form": response.form, "message": response.message}
    return render_template("upload_clients.html", **context)


@api.post("/clients")
@api.requires(Access.ORG.EDIT)
@api.input(AddClientRequestObject.Schema, location="json", arg_name="request_object")
@api.output(ResultIdResponseObject.Schema, 201)
def add_client(request_object: AddClientRequestObject):
    """
    Add a single client for the Billing Portal.
    """
    return AddClientUseCase().execute(request_object=request_object)


@api.get("/generate/billing_and_invoices/")
@api.requires(Access.ORG.EDIT)
@api.input(GenerateReportsRequestObject.Schema, location="query", arg_name="request_object")
def generate_reports(request_object: GenerateReportsRequestObject):
    """
    Generate billing and invoice reports for the Billing Portal.
    """
    response = GenerateReportsUseCase().execute(request_object)

    context = {"month_year": request_object.monthYear, "message": response.message}

    return render_template("generate_billing_reports.html", **context)


@api.get("/import/salesforce/")
@api.requires(Access.ORG.EDIT)
def get_sales_force_file_form():
    response = GetSalesForceFileFormUseCase().execute(RequestObject())
    context = {"form": response.form, "message": response.message}
    return render_template("upload_sales_force_file.html", **context)


@api.post("/import/salesforce/")
@api.requires(Access.ORG.EDIT)
def add_sales_force_file():
    from flask import request

    if "file" not in request.files:
        response = GetSalesForceFileFormUseCase().execute(RequestObject())
        context = {"form": response.form, "message": "No file part"}
        return render_template("upload_sales_force_file.html", **context)

    file = request.files["file"]
    file_name = file.filename
    file = BytesIO(file.read())

    if file_name == "":
        response = GetSalesForceFileFormUseCase().execute(RequestObject())
        context = {"form": response.form, "message": "No selected file"}
        return render_template("upload_sales_force_file.html", **context)

    request_object = AddSalesForceFileRequestObject.from_dict(
        {"fileName": file_name, "fileData": file, "userId": g.auth_user.id}
    )
    response = AddSalesForceFileUseCase().execute(request_object)

    form_response = GetSalesForceFileFormUseCase().execute(RequestObject())
    context = {"form": form_response.form, "message": response.message}
    return render_template("upload_sales_force_file.html", **context)


@api.post("/call-export-task")
@api.requires(Access.ORG.EDIT)
def call_export_task():
    """
    Call the export task for the Billing Portal.
    This endpoint is used to trigger the export of billing data.
    """

    generate_billing_cpt_codes_report.delay()
    return {"message": "Export task has been triggered."}, 202
