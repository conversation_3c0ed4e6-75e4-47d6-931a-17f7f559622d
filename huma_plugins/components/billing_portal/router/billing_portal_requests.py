import io
from dataclasses import field
from datetime import datetime

from sdk.common.usecase.request_object import RequestObject
from sdk.common.utils.convertible import convertibleclass, default_field, meta, required_field
from sdk.common.utils.fields import id_field
from sdk.common.utils.validators import default_datetime_meta


@convertibleclass
class GenerateReportsRequestObject(RequestObject):
    MONTH_YEAR = "monthYear"

    monthYear: str = default_field()


@convertibleclass
class AddClientRequestObject(RequestObject):
    """Request object for adding a single client."""

    NAME = "name"
    PAYMENT_FORMULA = "paymentFormula"
    RATE = "rate"

    name: str = required_field()
    paymentFormula: str = required_field()
    rate: int = field(default=0)


@convertibleclass
class AddClientDeploymentRequestObject(RequestObject):
    """Request object for adding a deployment to a client."""

    CLIENT_ID = "clientId"
    DEPLOYMENT_ID = "deploymentId"

    clientId: str = id_field()
    deploymentId: str = id_field()


@convertibleclass
class UpdateClientDeploymentRequestObject(AddClientDeploymentRequestObject):
    pass


@convertibleclass
class UpdateClientRequestObject(AddClientRequestObject):
    ID = "id"

    id: str = required_field()


@convertibleclass
class AddSalesForceFileRequestObject(RequestObject):
    FILE_NAME = "fileName"
    FILE_DATA = "fileData"

    fileName: str = required_field()
    fileData: io.BytesIO = required_field(metadata=meta(type=io.BytesIO))
    uploadedAt: datetime = field(default_factory=datetime.utcnow, metadata=default_datetime_meta())
    userId: str = id_field(metadata=meta(dump_only=True, view_arg="user_id"))
