from django.db import models

from sdk.authorization.models import User
from .client import Client


class RedoxLog(models.Model):
    class Meta:
        db_table = "billing_redox_log"
        app_label = "billing_portal"
        indexes = [
            models.Index(fields=["client", "patient"], name="idx_redox_log_client_patient"),
            models.Index(fields=["client", "patient", "date_time_of_service"], name="idx_rdx_log_client_patient_dt"),
            models.Index(fields=["log_id"], name="idx_redox_log_log_id"),
        ]

    log_id = models.CharField(max_length=255, null=False)
    timestamp = models.DateTimeField()
    data_model = models.CharField(max_length=255, null=False)
    log_type = models.CharField(max_length=255, null=False)
    status = models.CharField(max_length=255, null=False)
    first_name = models.CharField(max_length=255, null=False)
    last_name = models.Char<PERSON>ield(max_length=255, null=False)
    patient = models.ForeignKey(User, on_delete=models.CASCADE, null=True)
    date_time_of_service = models.DateTimeField()
    error = models.TextField(null=True)
    client = models.ForeignKey(Client, on_delete=models.CASCADE)

    def __str__(self):
        return f"{self.log_id} {self.timestamp} - {self.status} - {self.first_name} - {self.patient}"
