from django.db import models
from django.db.models import CASCADE

from .billing_record import BillingRecord


class CptCode(models.TextChoices):
    CPT_99453 = "99453", "99453"
    CPT_99454 = "99454", "99454"
    CPT_99457 = "99457", "99457"
    CPT_99458 = "99458", "99458"


class CptChargeUnit(models.Model):
    class Meta:
        db_table = "billing_cpt_charge_unit"
        app_label = "billing_portal"
        indexes = [
            models.Index(fields=["date_of_service", "billingRecord"], name="idx_cpt_unit_date_billing"),
            models.Index(fields=["code", "billingRecord"], name="idx_cpt_unit_code_billing"),
            models.Index(fields=["mongoId"], name="idx_cpt_unit_mongo_id"),
        ]
        constraints = []

    id = models.AutoField(primary_key=True)
    mongoId = models.CharField(max_length=24, unique=True)
    code = models.CharField(
        max_length=20,
        choices=CptCode.choices,
    )
    units = models.IntegerField(null=False, default=1)
    date_of_service = models.DateField(null=False)
    extra_details = models.JSONField(null=True)
    billingRecord = models.ForeignKey(BillingRecord, null=False, on_delete=CASCADE)
    dependant_unit = models.ForeignKey("self", null=True, on_delete=CASCADE)

    def __str__(self):
        return f"{self.units}@{self.code} - {self.date_of_service} - {self.billingRecord} - {self.extra_details}"
