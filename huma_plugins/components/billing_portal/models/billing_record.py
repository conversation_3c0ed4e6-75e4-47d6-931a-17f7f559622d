from django.db import models
from django.db.models import CASCADE

from sdk.authorization.models import User
from .deployment import Deployment


class BillingRecord(models.Model):
    id = models.AutoField(primary_key=True)
    mongoId = models.CharField(max_length=24, unique=True, default=None)
    deployment = models.ForeignKey(Deployment, on_delete=CASCADE)
    patient = models.ForeignKey(User, on_delete=CASCADE)
    provider = models.CharField(max_length=255, null=False)
    diagnosis_type = models.CharField(max_length=255, null=False)
    device_connection_date = models.DateField(null=True)
    primary_insurance = models.Char<PERSON>ield(max_length=255)
    report_end_date = models.DateField(null=True)
    is_eligible = models.BooleanField(null=False)
    monitoring_minutes = models.IntegerField(null=False, default=0)
    number_of_calls = models.IntegerField(null=False, default=0)
    month_reading = models.IntegerField(null=False, default=0)
    consent_date = models.DateField(null=True)
    consent_id = models.CharField(max_length=255, null=True)
    device_type = models.CharField(max_length=255, null=True)
    device_model = models.CharField(max_length=255, null=True)

    class Meta:
        db_table = "billing_record"
        app_label = "billing_portal"
        indexes = [
            models.Index(fields=["deployment"]),
            models.Index(fields=["mongoId"], name="idx_billing_record_mongo_id"),
            models.Index(fields=["patient", "report_end_date"], name="idx_billing_rec_patient_date"),
        ]
        constraints = [
            models.UniqueConstraint(
                fields=["patient", "deployment", "report_end_date"], name="unique_patient_client_combo"
            )
        ]

    def __str__(self):
        return f"{self.patient}@{self.deployment.client} - {self.report_end_date}"

    def _is_eligible(self):
        eligible = not self._is_engage() and self._device_connection_before_report()
        return eligible

    def _is_engage(self):
        return self.primary_insurance.lower() == "engage"

    def _device_connection_before_report(self):
        return self.device_connection_date and self.device_connection_date <= self.report_end_date

    def save(self, *args, **kwargs):
        self.is_eligible = bool(self._is_eligible())
        super().save(*args, **kwargs)
