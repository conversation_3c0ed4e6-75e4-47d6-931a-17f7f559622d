from django.db import models

from .billing_record import BillingRecord


class MedicalRecordNotes(models.Model):
    # todo: remove and replace with Observation Notes
    class Meta:
        db_table = "billing_medical_record_notes"
        app_label = "billing_portal"

    billing_record = models.ForeignKey(BillingRecord, on_delete=models.CASCADE)
    date_time = models.DateTimeField()
    note = models.TextField()
    clinician_given_name = models.CharField(max_length=255)
    clinician_family_name = models.CharField(max_length=255)
