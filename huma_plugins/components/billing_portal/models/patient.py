from django.db import models


class Patient(models.Model):
    # todo: remove and replace with User model
    class Meta:
        db_table = "billing_portal_patient"
        app_label = "billing_portal"

    huma_id = models.CharField(max_length=255, primary_key=True)
    first_name = models.Char<PERSON>ield(max_length=255)
    last_name = models.CharField(max_length=255)
    date_of_birth = models.DateField(null=False)
    mrn = models.CharField(max_length=255, null=True)

    def get_patient_full_name(self):
        return f"{self.first_name} {self.last_name}"

    def __str__(self):
        return self.get_patient_full_name()
