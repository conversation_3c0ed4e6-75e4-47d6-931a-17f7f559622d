from django.db import models

from sdk.authorization.models import User
from .invoice import Invoice


class InvoiceEntry(models.Model):
    class Meta:
        db_table = "billing_invoice_entry"
        app_label = "billing_portal"
        indexes = [
            models.Index(fields=["patient", "parent_invoice"], name="idx_invoice_patient_parent"),
        ]

    patient = models.ForeignKey(User, on_delete=models.CASCADE)
    service_description = models.CharField(max_length=255, null=False)
    unites = models.IntegerField(null=False)
    rates = models.IntegerField(null=False)
    parent_invoice = models.ForeignKey(Invoice, on_delete=models.CASCADE)
