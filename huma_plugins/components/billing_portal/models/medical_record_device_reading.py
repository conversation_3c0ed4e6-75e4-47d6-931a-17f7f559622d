from django.db import models

from sdk.authorization.models import User


class MedicalRecordDeviceReading(models.Model):
    class Meta:
        db_table = "billing_medical_record_device_reading"
        app_label = "billing_portal"
        indexes = [
            models.Index(fields=["patient", "start_date_time"], name="idx_med_rec_patient_start_dt"),
        ]

    patient = models.ForeignKey(User, on_delete=models.CASCADE)
    module = models.CharField(max_length=255)  # called "type" in the report
    average = models.FloatField()
    min = models.FloatField()
    max = models.FloatField()
    source = models.CharField(max_length=255)
    start_date_time = models.DateTimeField()
