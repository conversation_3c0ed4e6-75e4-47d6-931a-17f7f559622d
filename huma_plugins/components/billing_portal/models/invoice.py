from django.db import models
from django.db.models import ForeignKey

from .client import Client


class Invoice(models.Model):
    class Meta:
        db_table = "billing_invoice"
        app_label = "billing_portal"
        indexes = [
            models.Index(fields=["client", "invoice_end_date"], name="idx_invoice_client_end_date"),
        ]
        constraints = [
            models.UniqueConstraint(
                fields=["client", "invoice_start_date", "invoice_end_date"], name="unique_client_date_combo"
            )
        ]

    invoice_start_date = models.DateField()
    invoice_end_date = models.DateField()
    client = ForeignKey(Client, on_delete=models.CASCADE)
    extra_details = models.TextField(null=True)

    def __str__(self):
        return f"{self.client.name} - {self.invoice_end_date}"
