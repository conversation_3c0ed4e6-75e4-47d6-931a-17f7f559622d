from huma_plugins.components.revere.router.policies import get_read_revere_tests_policy
from sdk.authorization.router.policies import (
    get_user_route_read_policy,
    get_user_route_write_policy,
    is_self_request,
)
from sdk.security.access import BaseAccess
from sdk.security.utils import CheckParams, CheckResult


class RevereAccess(BaseAccess):
    BASE = "BASE"
    READ = "READ"
    WRITE = "WRITE"

    def check(self, params: CheckParams) -> CheckResult:
        match self:
            case RevereAccess.BASE:
                policy = get_user_route_read_policy()
            case RevereAccess.READ:
                get_read_revere_tests_policy()
                return CheckResult(True)
            case RevereAccess.WRITE:
                if not is_self_request():
                    return CheckResult(False)
                policy = get_user_route_write_policy()
            case _:
                return CheckResult(False)

        for role, _ in params.roles:
            if role.has([policy]):
                return CheckResult(True)
        return CheckResult(False)
