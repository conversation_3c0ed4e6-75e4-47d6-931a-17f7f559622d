import logging

from flask import request, g

from huma_plugins.components.revere.dtos.revere import RevereTestDTO
from sdk.authorization.dtos.authorized_user import AuthorizedUser
from sdk.authorization.router.policies import (
    is_self_request,
    get_user_route_write_policy,
)
from sdk.common.exceptions.exceptions import PermissionDenied
from sdk.common.utils.flask_request_utils import get_json_body

logger = logging.getLogger(__file__)


def get_read_revere_tests_policy():
    """To not allow owner retrieve list of not finished tests."""
    authz_user: AuthorizedUser = g.authz_user
    body = get_json_body(request)
    status = body.get(RevereTestDTO.STATUS)
    if is_self_request() and status != RevereTestDTO.Status.FINISHED.value:
        msg = "%s #[%s] can not retrieve list of not finished test."
        logger.warning(msg % (authz_user.get_role().id, authz_user.id))
        raise PermissionDenied


def get_write_revere_tests_policy():
    """To not allow owner retrieve list of not finished tests."""
    if not is_self_request():
        raise PermissionDenied
    return get_user_route_write_policy()
