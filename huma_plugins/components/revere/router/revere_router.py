from datetime import datetime

from flasgger import swag_from
from flask import Response, g

from huma_plugins.components.revere.dtos.revere import RevereTestDTO
from huma_plugins.components.revere.models import RevereTest
from huma_plugins.components.revere.module import RevereTestModule
from huma_plugins.components.revere.router import StartRevereTestResponse
from huma_plugins.components.revere.router.revere_request_objects import (
    ExportRevereResultsRequestObject,
    ProcessAudioResultRequestBody,
    ProcessAudioResultRequestObject,
)
from huma_plugins.components.revere.security import RevereAccess
from huma_plugins.components.revere.service.revere_service import RevereTestService
from huma_plugins.components.revere.use_cases.revere_use_case import (
    UploadAudioResultUseCaseUseCase,
)
from sdk.authorization.dtos.authorized_user import AuthorizedUser
from sdk.common.constants import SWAGGER_DIR
from sdk.security import ProtectedBlueprint

api = ProtectedBlueprint("revere", __name__, url_prefix="/api/extensions/v1/user")


@api.post("/<user_id>/revere-test/start/", requires=RevereAccess.BASE & RevereAccess.WRITE)
@api.output(StartRevereTestResponse.Schema, 201)
@swag_from(f"{SWAGGER_DIR}/start_test.yml")
def start_test(user_id):
    service = RevereTestService()
    deployment_id = str(AuthorizedUser(g.user).deployment_id())
    test_id, word_lists = service.create_test(
        user_id,
        deployment_id,
        RevereTestModule.moduleId,
    )
    return StartRevereTestResponse(id=test_id, wordLists=word_lists)


@api.post("/<user_id>/revere-test/<test_id>/words/<word_list_id>/audio/")
@api.requires(RevereAccess.BASE & RevereAccess.WRITE)
@api.input(ProcessAudioResultRequestBody.Schema)
@api.output({}, 204)
@swag_from(f"{SWAGGER_DIR}/process_audio.yml")
def upload_audio_result(user_id, test_id, word_list_id, json_data: ProcessAudioResultRequestBody):
    body = {
        **json_data.dump(),
        ProcessAudioResultRequestObject.USER_ID: user_id,
        ProcessAudioResultRequestObject.SUBMITTER_ID: user_id,
        ProcessAudioResultRequestObject.MODULE_ID: RevereTest.__name__,
        ProcessAudioResultRequestObject.TEST_ID: test_id,
        ProcessAudioResultRequestObject.ID: word_list_id,
    }
    req_obj = ProcessAudioResultRequestObject.from_dict(body)
    UploadAudioResultUseCaseUseCase().execute(req_obj)


@api.post(
    "/<user_id>/revere-test/<test_id>/finish/",
    requires=RevereAccess.BASE & RevereAccess.WRITE,
)
@api.output({}, 204)
@swag_from(f"{SWAGGER_DIR}/finish_test.yml")
def finish_test(user_id, test_id):
    service = RevereTestService()
    service.finish_test(user_id, test_id)


@api.get("/<user_id>/revere-test/all/", requires=RevereAccess.BASE & RevereAccess.READ)
@api.output(RevereTestDTO.Schema(many=True))
@swag_from(f"{SWAGGER_DIR}/retrieve_user_tests.yml")
def retrieve_all_user_tests(user_id):
    service = RevereTestService()
    return service.retrieve_user_tests(user_id)


@api.get("/<user_id>/revere-test/", requires=RevereAccess.BASE)
@api.output(RevereTestDTO.Schema(many=True))
@swag_from(f"{SWAGGER_DIR}/retrieve_users_finished_tests.yml")
def retrieve_finished_user_tests(user_id):
    service = RevereTestService()
    return service.retrieve_user_tests(user_id, RevereTestDTO.Status.FINISHED.value)


@api.get("/<user_id>/revere-test/<test_id>/", requires=RevereAccess.BASE)
@api.doc(
    responses={
        200: {
            "description": "CSV file with test results",
            "content": {"text/csv": {"schema": {}}},
        }
    }
)
@swag_from(f"{SWAGGER_DIR}/export_test_result.yml")
def export_test_result(user_id, test_id):
    service = RevereTestService()
    csv_data = service.export_test_result(user_id, test_id)

    return Response(
        csv_data,
        mimetype="text/csv",
        headers={"Content-disposition": f"attachment; filename={test_id}.csv"},
    )


@api.post("/<user_id>/revere-test/export/", requires=RevereAccess.BASE & RevereAccess.WRITE)
@api.input(ExportRevereResultsRequestObject.Schema)
@api.doc(
    responses={
        200: {
            "description": "Archive with data",
            "content": {"application/zip": {"schema": {}}},
        }
    }
)
def export_revere(user_id, json_data: ExportRevereResultsRequestObject):
    service = RevereTestService()
    data = service.export_tests_zip(user_id=user_id, status=json_data.status.value)

    return Response(
        data,
        mimetype="application/zip",
        headers={"Content-disposition": f"attachment; filename={user_id}_{datetime.now()}.zip"},
    )
