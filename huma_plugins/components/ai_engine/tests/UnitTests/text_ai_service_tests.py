import unittest
from unittest.mock import MagicMock

from huma_plugins.components.ai_engine.adapters.ai_platform import AIPlatform
from huma_plugins.components.ai_engine.exceptions import GenAIEngineNotConfiguredException
from huma_plugins.components.ai_engine.models.common import Action, Context
from huma_plugins.components.ai_engine.repository.ai_log_repository import AILogRepository
from huma_plugins.components.ai_engine.service.data_extraction_service import DataExtractionService
from huma_plugins.components.ai_engine.service.text_ai_service import TextGenAIService
from sdk.authorization.dtos.user import UserDTO
from sdk.common.exceptions.exceptions import InternalServerErrorException
from sdk.common.utils import inject
from sdk.deployment.dtos.deployment import DeploymentDTO
from sdk.phoenix.config.server_config import PhoenixServerConfig

SAMPLE_USER_ID = "600a8476a961574fb38157d5"
SAMPLE_DEPLOYMENT_ID = "600a8476a961574fb38157d6"


class MockAIPlatform(AIPlatform):
    model_id = "test_model"

    @property
    def name(self) -> str:
        return "MockAIPlatform"

    def generate_text(self, prompts: list[str]) -> tuple[str, dict]:
        return "Generated text", {"response": "AI response"}


class MockDataExtractionService:
    get_user_profile = MagicMock(return_value={"name": "Test User"})
    get_staff_profile = MagicMock(return_value={"name": "Dr. Test"})
    get_user_health_data = MagicMock(return_value=(["health_data"], ["seen_data"]))
    get_user_medications = MagicMock(return_value=["medication1"])


class TextGenAIServiceTestCase(unittest.TestCase):
    ai_platform_mock = MockAIPlatform()
    server_config = MagicMock()
    ai_log_repo_mock = MagicMock()
    data_extraction_service_mock = MockDataExtractionService()

    @classmethod
    def setUpClass(cls):
        def bind(binder):
            binder.bind(AIPlatform, cls.ai_platform_mock)
            binder.bind(DataExtractionService, cls.data_extraction_service_mock)
            binder.bind(AILogRepository, cls.ai_log_repo_mock)
            binder.bind(PhoenixServerConfig, cls.server_config)

        inject.clear_and_configure(bind)

    def setUp(self) -> None:
        self.service = TextGenAIService()
        self.server_config.server.aiEngine.aiPlatformTimeout = 30

        self.user_dto = UserDTO(id=SAMPLE_USER_ID)
        self.submitter_dto = UserDTO(id="submitter_id")
        self.deployment_dto = DeploymentDTO(id=SAMPLE_DEPLOYMENT_ID)

    def tearDown(self):
        self.data_extraction_service_mock.get_user_profile.reset_mock()

    def test_init_without_ai_platform_raises_exception(self):
        def bind(binder):
            binder.bind(AIPlatform, None)
            binder.bind(DataExtractionService, MockDataExtractionService())
            binder.bind(AILogRepository, MagicMock())
            binder.bind(PhoenixServerConfig, self.server_config)

        inject.clear_and_configure(bind)

        with self.assertRaises(GenAIEngineNotConfiguredException):
            TextGenAIService()

    def test_generate_message_success(self):
        prompt, text = self.service.generate_text(
            submitter=self.submitter_dto,
            user=self.user_dto,
            deployment=self.deployment_dto,
            action=Action.GENERATE_MESSAGE,
            contexts=[Context.DEMOGRAPHICS],
            input_text="Test message",
        )

        self.assertEqual(text, "Generated text")
        self.assertIn("PATIENT'S DEMOGRAPHICS", prompt)
        self.assertIn("CLINICIAN'S NAME", prompt)
        self.assertIn("MESSAGE INTENT", prompt)

        # Verify logging
        self.ai_log_repo_mock.add_generated_text_log.assert_called_once()

    def test_generate_note_success(self):
        prompt, text = self.service.generate_text(
            submitter=self.submitter_dto,
            user=self.user_dto,
            deployment=self.deployment_dto,
            action=Action.GENERATE_NOTE,
            contexts=[Context.DEMOGRAPHICS, Context.UNRESOLVED_FLAGS],
            input_text="",
        )

        self.assertEqual(text, "Generated text")
        self.assertIn("PATIENT'S DEMOGRAPHICS", prompt)
        self.assertIn("RECENT HEALTH DATA", prompt)
        self.assertIn("HEALTH DATA", prompt)
        self.assertIn("PATIENT'S MEDICATIONS", prompt)
        self.assertIn("CURRENT DATE", prompt)

    def test_generate_message_no_data_available(self):
        self.data_extraction_service_mock.get_user_profile.return_value = None

        prompt, text = self.service.generate_text(
            submitter=self.submitter_dto,
            user=self.user_dto,
            deployment=self.deployment_dto,
            action=Action.GENERATE_MESSAGE,
            contexts=[],
            input_text="",
        )

        self.assertEqual(text, self.service.NO_DATA_MESSAGE)
        self.assertEqual(prompt, "")

    def test_ai_platform_timeout_raises_exception(self):
        from concurrent.futures import TimeoutError

        self.ai_platform_mock.generate_text.side_effect = TimeoutError()

        with self.assertRaises(InternalServerErrorException) as cm:
            self.service.generate_text(
                submitter=self.submitter_dto,
                user=self.user_dto,
                deployment=self.deployment_dto,
                action=Action.GENERATE_MESSAGE,
                contexts=[Context.DEMOGRAPHICS],
                input_text="Test",
            )

        self.assertIn("AI Platform timed out", str(cm.exception))

    def test_generated_message_too_long_raises_exception(self):
        mock_platform = MagicMock()

        def bind(binder):
            binder.bind(AIPlatform, mock_platform)

        inject.get_injector().rebind(bind)

        mock_platform.generate_text.return_value = ("a" * 10000, "AI response")

        with self.assertRaises(InternalServerErrorException) as cm:
            self.service.generate_text(
                submitter=self.submitter_dto,
                user=self.user_dto,
                deployment=self.deployment_dto,
                action=Action.GENERATE_MESSAGE,
                contexts=[Context.DEMOGRAPHICS],
                input_text="Test",
            )

        self.assertIn("Generated message is too long", str(cm.exception))
