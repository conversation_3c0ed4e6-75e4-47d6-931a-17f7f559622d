from sdk.common.exceptions.exceptions import DetailedException


class GENAIErrorCodes:
    INVALID_SUBJECT = 300001
    AI_ENGINE_NOT_CONFIGURED = 300002


class GenAIInvalidSubjectException(DetailedException):
    def __init__(self, msg=None, **kwargs):
        super().__init__(
            code=GENAIErrorCodes.INVALID_SUBJECT,
            debug_message=msg or "Invalid subject",
            status_code=400,
            **kwargs,
        )


class GenAIEngineNotConfiguredException(DetailedException):
    def __init__(self, msg=None, **kwargs):
        super().__init__(
            code=GENAIErrorCodes.AI_ENGINE_NOT_CONFIGURED,
            debug_message=msg or "AI Engine is not configured",
            status_code=501,
            **kwargs,
        )
