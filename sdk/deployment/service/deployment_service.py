import logging
import typing
from copy import copy, deepcopy
from dataclasses import fields, is_dataclass
from datetime import datetime, timezone
from functools import cached_property
from typing import Optional

from bson import ObjectId
from tomlkit._utils import merge_dicts

from sdk.authorization.dtos.authorized_user import AuthorizedUser
from sdk.authorization.dtos.invitation import InvitationDTO
from sdk.authorization.dtos.role.role import RoleDTO
from sdk.common.adapter.event_bus_adapter import EventBusAdapter
from sdk.common.adapter.feature_toggle import FeatureToggleAdapter
from sdk.common.caching.utils import func_memory_cache
from sdk.common.common_models.sort import SortField
from sdk.common.exceptions.exceptions import (
    CmsArticleNotFoundException,
    InvalidRequestException,
    PermissionDenied,
)
from sdk.common.localization.utils import LOCALIZATION_DYNAMIC_KEY_PREPEND, Language
from sdk.common.utils import inject
from sdk.common.utils.common_functions_utils import find, get_all_subclasses
from sdk.common.utils.encryption_utils import encrypt
from sdk.common.utils.inject import autoparams
from sdk.common.utils.validators import read_json_file
from sdk.deployment.adapter.cms_adapter import CMSAdapter
from sdk.deployment.dtos.consent import Consent, ConsentLogDTO
from sdk.deployment.dtos.deployment import (
    DeploymentDTO,
    DeploymentRevisionDTO,
    DeploymentTemplateDTO,
    Label,
    ModuleConfig,
    OnboardingModuleConfigDTO,
    ProfileFields,
)
from sdk.deployment.dtos.deployment_info import DeploymentInfo
from sdk.deployment.dtos.econsent.econsent import EConsent
from sdk.deployment.dtos.econsent.econsent_log import EConsentLogDTO
from sdk.deployment.dtos.learn import Learn, LearnArticle, LearnArticleContentType, LearnSection, OrderUpdateObject
from sdk.deployment.dtos.status import EnableStatus, Status
from sdk.deployment.events import (
    CreateDeploymentEvent,
    DeleteDeploymentCustomRolesEvent,
    DeleteDeploymentEvent,
    ModuleConfigDeleteDisableEvent,
    PostCreateKeyActionConfigEvent,
    PostDeleteKeyActionConfigEvent,
    PostDeploymentUpdateEvent,
    PostUpdateKeyActionConfigEvent,
    PostUpdateModuleConfigEvent,
    PreCreateDeploymentEvent,
    PreCreateModuleConfigEvent,
    PreDeleteOnboardingModuleConfigEvent,
    PreDeploymentUpdateEvent,
    PreUpdateModuleConfigEvent,
    RetrieveCMSQuestionnaireEvent,
    StoreCMSQuestionnaireEvent,
    ValidateCMSContentEvent,
)
from sdk.deployment.exceptions import (
    DeploymentDoesNotExist,
    DuplicateOnboardingModuleConfigException,
    EConsentLogDoesNotExist,
    EncryptionSecretNotAvailable,
)
from sdk.deployment.repository.consent_repository import ConsentRepository
from sdk.deployment.repository.deployment_repository import DeploymentRepository
from sdk.deployment.repository.econsent_repository import EConsentRepository
from sdk.key_action.models.config import KeyActionConfig
from sdk.module_result.dtos.module_config import FilledBy, Footnote
from sdk.module_result.exceptions import InvalidModuleConfiguration
from sdk.module_result.modules import excluded_modules_ids_for_applying_default_disclaimer_config
from sdk.module_result.modules.awareness_training import AwarenessTrainingModule
from sdk.module_result.modules.az_screening_questionnaire import AZScreeningQuestionnaireModule
from sdk.module_result.modules.background_information import BackgroundInformationModule
from sdk.module_result.modules.ecg_module import ECGHealthKitModule
from sdk.module_result.modules.high_frequency_step import HighFrequencyStepModule
from sdk.module_result.modules.licensed_questionnaire_module import LicensedQuestionnaireModule
from sdk.module_result.modules.modules_manager import ModulesManager
from sdk.module_result.modules.questionnaire import QuestionnaireConstants
from sdk.module_result.modules.step import StepModule
from sdk.module_result.modules.surgery_details import SurgeryDetailsModule
from sdk.phoenix.config.dynamic_component_config import BasePhoenixDynamicConfig
from sdk.phoenix.config.server_config import PhoenixServerConfig
from sdk.storage.dtos import S3Object

logger = logging.getLogger(__name__)

FULL_DEPLOYMENT_SECTIONS = [
    DeploymentDTO.MODULE_CONFIGS,
    DeploymentDTO.LEARN,
    DeploymentDTO.KEY_ACTIONS,
    DeploymentDTO.CONSENT,
    DeploymentDTO.ECONSENT,
]

CONFIG_BODY_NAME = "name"


class DeploymentService:
    """Service to work with deployment repo."""

    @autoparams()
    def __init__(
        self,
        repo: DeploymentRepository,
        consent_repo: ConsentRepository,
        config: PhoenixServerConfig,
        econsent_repo: EConsentRepository,
        modules_manager: ModulesManager,
        event_bus: EventBusAdapter,
    ):
        self.repo = repo
        self.consent_repo = consent_repo
        self.econsent_repo = econsent_repo
        self.modules_manager = modules_manager
        self.config = config
        self._event_bus = event_bus

    def create_deployment(self, deployment: DeploymentDTO) -> str:
        self._check_SaMD(deployment)
        self._pre_create_deployment(deployment)
        deployment_id = self.repo.create_deployment(deployment=deployment)
        self._create_deployment_event(deployment_id)
        return deployment_id

    def create_full_deployment(self, full_deployment: DeploymentDTO) -> str:
        dep_id = self._handle_full_raw_deployment(deepcopy(full_deployment))

        full_deployment.id = dep_id

        self._fix_full_deployment_obj_s3_urls(deployment_id=dep_id, obj=full_deployment)

        self._handle_full_deployment_learn(full_deployment.learn, dep_id)

        for conf in full_deployment.moduleConfigs or []:
            conf_id = self.create_module_config(deployment_id=dep_id, config=conf)
            conf.id = conf_id

        for onboarding_conf in full_deployment.onboardingConfigs or []:
            self.create_or_update_onboarding_module_config(deployment_id=dep_id, config=onboarding_conf)

        self._handle_full_deployment_key_actions(
            key_actions=full_deployment.keyActions,
            deployment_id=dep_id,
            full_deployment=full_deployment,
        )

        if consent := full_deployment.consent:
            self.create_consent(deployment_id=dep_id, consent=consent)

        if econsent := full_deployment.econsent:
            self.create_econsent(deployment_id=dep_id, econsent=econsent)

        return dep_id

    def _handle_full_raw_deployment(self, deployment: DeploymentDTO):
        for k in FULL_DEPLOYMENT_SECTIONS:
            setattr(deployment, k, None)

        dep_id = self.create_deployment(deployment)

        self._fix_full_deployment_obj_s3_urls(dep_id, deployment)

        self.repo.update_deployment_icon(deployment_id=dep_id, icon=deployment.icon)

        return dep_id

    @staticmethod
    def __replace_deployment_id_in_path(path: str, deployment_id: str) -> str:
        dirs = path.split("/")
        if len(dirs) > 1:
            dirs[1] = deployment_id
            return "/".join(dirs)
        return path

    def _fix_full_deployment_obj_s3_urls(self, deployment_id: str, obj: object):
        if not obj or not is_dataclass(obj):
            return

        for k in fields(obj):
            val = getattr(obj, k.name, None)
            if isinstance(val, S3Object):
                obj_key = val.key
                if not obj_key:
                    continue
                setattr(
                    val,
                    S3Object.KEY,
                    self.__replace_deployment_id_in_path(obj_key, deployment_id),
                )
            elif isinstance(val, list):
                for item in val:
                    self._fix_full_deployment_obj_s3_urls(deployment_id, item)
            elif is_dataclass(val):
                self._fix_full_deployment_obj_s3_urls(deployment_id, val)

    def _handle_full_deployment_learn(self, learn: Learn, deployment_id: str):
        if not learn:
            return

        article_ids = []
        for sec in learn.sections or []:
            articles = deepcopy(sec.articles) or []
            sec.articles = []
            sec_id = self.create_learn_section(deployment_id, sec)
            sec.id = sec_id
            for article in articles:
                article_id = self.create_article(deployment_id, sec_id, article)
                article.id = article_id
                sec.articles.append(article)
                article_ids.append(article_id)
        return article_ids

    def _handle_full_deployment_key_actions(
        self,
        key_actions: list[KeyActionConfig],
        deployment_id: str,
        full_deployment: DeploymentDTO,
    ):
        for key_action in key_actions or []:
            if key_action.type is KeyActionConfig.Type.MODULE:
                matching_confs = list(
                    filter(
                        lambda x: x.moduleId == key_action.moduleId
                        and (
                            not key_action.moduleConfigId
                            or (key_action.moduleConfigId and x.moduleName == key_action.moduleConfigId)
                        ),
                        full_deployment.moduleConfigs,
                    )
                )
                key_action.moduleConfigId = matching_confs[0].id if matching_confs else None

            self.create_key_action(
                deployment_id=deployment_id,
                key_action=key_action,
                deployment=full_deployment,
            )

    def _create_deployment_event(self, deployment_id: str):
        self._event_bus.emit(CreateDeploymentEvent(deployment_id))

    def _pre_create_deployment(self, deployment: DeploymentDTO):
        self._event_bus.emit(PreCreateDeploymentEvent(deployment))

    def create_consent(self, deployment_id: str, consent: Consent) -> str:
        return self.consent_repo.create_consent(deployment_id=deployment_id, consent=consent)

    def create_consent_log(self, deployment_id: str, consent_log: ConsentLogDTO) -> str:
        return self.consent_repo.create_consent_log(deployment_id=deployment_id, consent_log=consent_log)

    def create_or_update_module_config(self, deployment_id: str, config: ModuleConfig) -> str:
        module_configs = self.retrieve_module_configs(deployment_id)
        module_in_db = [mc for mc in module_configs if mc.id == config.id]
        if module_in_db:
            return self.update_module_config(deployment_id, config)
        return self.create_module_config(deployment_id, config)

    def create_module_config(self, deployment_id: str, config: ModuleConfig, update_revision: bool = True) -> str:
        self.modules_manager.has(config.moduleId, raise_error=True)
        deployment = self.retrieve_deployment(deployment_id=deployment_id)
        if config.status == EnableStatus.ENABLED:
            self._set_mandatory_profile_fields(config.moduleId, deployment)
        self.set_module_fields(config, deployment.moduleConfigs)
        config.createDateTime = config.updateDateTime = datetime.now(tz=timezone.utc)
        self._event_bus.emit(PreCreateModuleConfigEvent(config), raise_error=True)

        self.store_cms_questionnaire(config, deployment_id)
        config_id = self.repo.create_module_config(
            deployment_id=deployment_id,
            config=config,
            update_revision=update_revision,
        )
        return config_id

    def update_module_config(self, deployment_id: str, config: ModuleConfig, update_revision: bool = True) -> str:
        deployment = self.retrieve_deployment(deployment_id=deployment_id)
        self.set_module_fields(config, deployment.moduleConfigs)
        self._event_bus.emit(PreUpdateModuleConfigEvent(config), raise_error=True)

        self.store_cms_questionnaire(config, deployment_id)

        config_id = self.repo.update_module_config(
            deployment_id=deployment_id,
            config=config,
            update_revision=update_revision,
        )
        self._event_bus.emit(PostUpdateModuleConfigEvent(config), raise_error=True)
        return config_id

    def store_cms_questionnaire(self, config: ModuleConfig, deployment_id: str):
        result = self._event_bus.subscribers_count_for(StoreCMSQuestionnaireEvent)

        if result != 1:
            return
        features: FeatureToggleAdapter = inject.instance(FeatureToggleAdapter)
        if features.is_enabled("cms_questionnaire") and config.moduleId == "Questionnaire":
            event = StoreCMSQuestionnaireEvent(config.configBody, deployment_id)
            config_bodies: list[dict] = self._event_bus.emit(event)
            config_body: dict = config_bodies.pop()

            config.configBody = config_body

    def set_module_fields(self, updated_config: ModuleConfig, current_module_configs: list[ModuleConfig]):
        module = self.modules_manager.find_module(module_id=updated_config.moduleId)
        module.set_fields_on_module_update(updated_config, current_module_configs)

    @staticmethod
    def _are_deployments_dicts_equal(dict1, dict2) -> bool:
        dict1 = copy(dict1)
        dict2 = copy(dict2)
        for _dict in (dict1, dict2):
            _dict.pop(DeploymentDTO.CREATE_DATE_TIME, None)
            _dict.pop(DeploymentDTO.UPDATE_DATE_TIME, None)
            _dict.pop(DeploymentDTO.VERSION, None)

        return dict1 == dict2

    def create_learn_section(self, deployment_id: str, learn_section: LearnSection) -> str:
        return self.repo.create_learn_section(deployment_id=deployment_id, learn_section=learn_section)

    def check_existing_onboarding_module_configs(self, deployment_id: str, config: OnboardingModuleConfigDTO):
        onboarding_module_configs = self.retrieve_onboarding_module_configs(deployment_id)
        for mc in onboarding_module_configs:
            if mc.onboardingId == config.onboardingId:
                raise DuplicateOnboardingModuleConfigException(config.onboardingId)

    def create_or_update_onboarding_module_config(
        self, deployment_id: str, config: OnboardingModuleConfigDTO, create_default=True
    ) -> str:
        if create_default:
            self.check_existing_onboarding_module_configs(deployment_id, config)
        module_configs = self.retrieve_onboarding_module_configs(deployment_id=deployment_id)
        module_in_db = [mc for mc in module_configs if mc.id == config.id]
        if not module_in_db:
            return self.create_onboarding_module_config(deployment_id, config)
        return self.update_onboarding_module_config(deployment_id, config)

    def create_onboarding_module_config(
        self,
        deployment_id: str,
        config: OnboardingModuleConfigDTO,
        update_revision: bool = True,
    ) -> str:
        from sdk.authorization.boarding.manager import BoardingManager

        BoardingManager.has(config.onboardingId, raise_error=True)
        return self.repo.create_onboarding_module_config(
            deployment_id=deployment_id, config=config, update_revision=update_revision
        )

    def update_onboarding_module_config(
        self,
        deployment_id: str,
        config: OnboardingModuleConfigDTO,
        update_revision: bool = True,
    ) -> str:
        self.retrieve_deployment(deployment_id=deployment_id)
        return self.repo.update_onboarding_module_config(
            deployment_id=deployment_id,
            onboarding_module_config=config,
            update_revision=update_revision,
        )

    def create_article(self, deployment_id: str, section_id: str, learn_article: LearnArticle) -> str:
        if learn_article.content.type is LearnArticleContentType.CMS:
            cms_adapter = inject.instance(CMSAdapter)
            if not cms_adapter.article_exists(learn_article.content.cmsArticleId):
                raise CmsArticleNotFoundException("Article does not exist in CMS")
        article_id = self.repo.create_learn_article(
            deployment_id=deployment_id,
            section_id=section_id,
            learn_article=learn_article,
        )
        return article_id

    def _validate_key_action(self, key_action: KeyActionConfig, deployment: DeploymentDTO):
        if key_action.type is KeyActionConfig.Type.MODULE:
            config_exists = any(mc.id == key_action.moduleConfigId for mc in deployment.moduleConfigs)
            if not config_exists:
                raise InvalidRequestException(
                    f"Deployment [{deployment.id}] does not have config [{key_action.moduleConfigId}] with type"
                    f" [{key_action.type}], title [{key_action.title}]"
                )

        elif key_action.type is KeyActionConfig.Type.LEARN:
            article_exists = False
            if deployment.learn:
                article_exists = any(article.id == key_action.learnArticleId for article in deployment.learn.articles)
            cms_article_exists = self._is_valid_cms_content("articles", key_action.learnArticleId)
            if not article_exists and not cms_article_exists:
                raise InvalidRequestException(f"Article [{key_action.learnArticleId}] does not exist")

    def _is_valid_cms_content(self, collection: str, content_id: str):
        event = ValidateCMSContentEvent(collection, content_id)
        return self._event_bus.emit(event)

    def create_key_action(
        self,
        deployment_id: str,
        key_action: KeyActionConfig,
        deployment: DeploymentDTO = None,
    ):
        deployment = deployment or self.retrieve_deployment(deployment_id)
        self._call_load_linked_configuration_for_module_config_list(deployment.moduleConfigs or [])
        self._validate_key_action(key_action, deployment)

        key_action.id = str(ObjectId())
        localizations = key_action.set_dynamic_localizations(f"{LOCALIZATION_DYNAMIC_KEY_PREPEND}#{key_action.id}")
        key_action_id = self.repo.create_key_action(deployment_id=deployment_id, key_action=key_action)
        self.update_localization(
            deployment_id=deployment_id,
            locale=deployment.language,
            localizations=localizations,
        )
        self._post_key_action_create(deployment_id, key_action_id)
        return key_action_id

    @autoparams("event_bus")
    def _call_load_linked_configuration_for_module_config_list(
        self, module_configs: None | list[ModuleConfig], event_bus: EventBusAdapter
    ):
        if module_configs is None:
            return
        # TODO: needs to set `raise_error=True` after database cleanup process applied - see HPB-32767
        event_bus.emit(RetrieveCMSQuestionnaireEvent(modules_configs=module_configs), raise_error=False)

    def retrieve_deployments(
        self,
        skip: int = 0,
        limit: int = None,
        search_criteria: str = None,
        search: str = None,
        sort_fields: list[SortField] = None,
        status: list[Status] = None,
    ):
        deployments, count = self.repo.retrieve_deployments(skip, limit, search or search_criteria, sort_fields, status)
        for deployment in deployments:
            deployment.load_linked_modules_to_deployment_configurations()
        return deployments, count

    def retrieve_deployments_by_ids(self, deployment_ids: list[str]) -> list[DeploymentDTO]:
        deployments = self.repo.retrieve_deployments_by_ids(deployment_ids=deployment_ids)
        for deployment in deployments:
            deployment.load_linked_modules_to_deployment_configurations()
        return deployments

    def retrieve_deployments_info(
        self, deployment_ids: list[str] = None, draft_ids: list[str] = None
    ) -> list[DeploymentInfo]:
        return self.repo.retrieve_deployments_info(deployment_ids, draft_ids)

    def retrieve_deployment(
        self, deployment_id: str, language: str = Language.EN, raise_error=True, exclude_fields: list[str] = None
    ) -> Optional[DeploymentDTO]:
        try:
            deployment = self.repo.retrieve_deployment(deployment_id=deployment_id, exclude_fields=exclude_fields)
            deployment.load_linked_modules_to_deployment_configurations()
            deployment.load_licensed_config_bodies()
            return deployment
        except DeploymentDoesNotExist as error:
            if not raise_error:
                return None
            raise error

    def retrieve_raw_deployment(self, deployment_id: str) -> DeploymentDTO:
        return self.repo.retrieve_deployment(deployment_id=deployment_id)

    def retrieve_deployment_with_fields(self, deployment_id: str, only_fields: list[str]) -> DeploymentDTO:
        exclude_fields = [f.name for f in fields(DeploymentDTO) if f.name not in only_fields]
        deployment = self.repo.retrieve_deployment(deployment_id=deployment_id, exclude_fields=exclude_fields)
        deployment.load_linked_modules_to_deployment_configurations()
        return deployment

    def retrieve_deployment_cms_builder(
        self, deployment_id: str, language: str, raise_error=True
    ) -> Optional[DeploymentDTO]:
        deployment = self.retrieve_deployment(deployment_id=deployment_id, language=language, raise_error=raise_error)
        deployment.generate_cms_article_content_for_learn_section()
        deployment.load_linked_cms_articles_for_modules(language)
        deployment.load_linked_cms_articles_for_key_actions(language)
        return deployment

    def retrieve_deployment_by_version_number(self, deployment_id: str, version_number: int):
        deployment = self.repo.retrieve_deployment_by_version_number(
            deployment_id=deployment_id, version_number=version_number
        )
        deployment.load_linked_modules_to_deployment_configurations()
        return deployment

    def retrieve_deployment_ids_by_tags(self, tags: list[str]) -> list[str]:
        return self.repo.retrieve_deployment_ids_by_tags(tags)

    def retrieve_deployment_config(self, authz_user: AuthorizedUser) -> DeploymentDTO:
        deployment = self.retrieve_deployment(deployment_id=authz_user.deployment_id())

        deployment.prepare_for_role(authz_user)
        return deployment

    def retrieve_deployment_config_for_key_actions_build(self, authz_user) -> DeploymentDTO:
        deployment = self.retrieve_deployment(deployment_id=authz_user.deployment_id())

        deployment.prepare_for_key_actions(authz_user)
        return deployment

    def retrieve_module_configs(self, deployment_id: str, module_id: str = None) -> list[ModuleConfig]:
        module_configs = self.repo.retrieve_module_configs(deployment_id=deployment_id)
        self._call_load_linked_configuration_for_module_config_list(module_configs)
        if module_id:
            return [mc for mc in module_configs if mc.moduleId == module_id]
        return module_configs

    def retrieve_module_configs_filled_by(
        self, deployment_ids: list[str], filled_by: list[FilledBy]
    ) -> list[ModuleConfig]:
        module_configs = self.repo.retrieve_module_configs_filled_by(deployment_ids, filled_by)
        self._call_load_linked_configuration_for_module_config_list(module_configs)
        return module_configs

    def check_export_short_code_module_config(self, short_code: str) -> bool:
        return self.repo.check_export_short_code_module_config(short_code)

    def retrieve_onboarding_module_configs(self, deployment_id: str) -> list[OnboardingModuleConfigDTO]:
        return self.repo.retrieve_onboarding_module_configs(deployment_id=deployment_id)

    def retrieve_module_config(self, module_config_id: str) -> Optional[ModuleConfig]:
        module_config = self.repo.retrieve_module_config(module_config_id=module_config_id)
        self._call_load_linked_configuration_for_module_config_list([module_config])
        return module_config

    def retrieve_module_config_with_licensed_body(self, module_config_id: str) -> Optional[ModuleConfig]:
        from sdk.module_result.modules.licensed_questionnaire_module import (
            get_licensed_questionnaire_dir,
            get_licensed_questionnaires_config_path,
            get_licensed_questionnaires_dir_list,
        )

        mc = self.repo.retrieve_module_config(module_config_id=module_config_id)
        if not mc:
            return mc

        self._call_load_linked_configuration_for_module_config_list([mc])

        licensed_questionnaires_dir_list = get_licensed_questionnaires_dir_list()
        if mc.moduleId not in licensed_questionnaires_dir_list:
            return mc
        config_path = get_licensed_questionnaires_config_path(mc.moduleId)
        try:
            config_body = read_json_file(f"{get_licensed_questionnaire_dir()}/{config_path}", "./")
        except FileNotFoundError:
            raise InvalidModuleConfiguration(f"Config Body file not found for {mc.moduleId}")
        else:
            conf_dict = mc.to_dict(include_none=False)
            config_body_copy = deepcopy(config_body)
            merge_dicts(conf_dict, config_body_copy)
            module_config = ModuleConfig.from_dict(conf_dict, use_validator_field=False)
        return module_config

    def retrieve_key_actions(
        self, deployment_id: str, trigger: KeyActionConfig.Trigger = None
    ) -> list[KeyActionConfig]:
        return self.repo.retrieve_key_actions(deployment_id=deployment_id, trigger=trigger)

    def _pre_update_deployment(self, deployment: DeploymentDTO, previous_state: DeploymentDTO):
        event = PreDeploymentUpdateEvent(deployment, previous_state)
        self._event_bus.emit(event, raise_error=True)

    def _post_update_deployment(self, deployment: DeploymentDTO, previous_state: DeploymentDTO):
        event = PostDeploymentUpdateEvent(deployment, previous_state)
        self._event_bus.emit(event, raise_error=True)

    def update_deployment(
        self,
        deployment: DeploymentDTO,
        update_revision: bool = True,
        set_update_time: bool = True,
    ) -> str:
        previous_version = self.retrieve_raw_deployment(deployment.id)
        self._check_SaMD(deployment, previous_version)
        self._check_profile_mandatory_fields(deployment, previous_version)
        self._pre_update_deployment(deployment, previous_version)

        if set_update_time:
            deployment.updateDateTime = datetime.now(tz=timezone.utc)

        status = deployment.status or previous_version.status
        if update_revision and status is Status.DEPLOYED:
            deployment.version = previous_version.version + 1

        deployment_id = self.repo.update_deployment(deployment)
        self._post_update_deployment(deployment, previous_version)
        return deployment_id

    def update_enrollment_counter(self, deployment_id) -> DeploymentDTO:
        return self.repo.update_enrollment_counter(deployment_id=deployment_id)

    def update_learn_section(self, deployment_id: str, learn_section: LearnSection) -> str:
        return self.repo.update_learn_section(deployment_id=deployment_id, learn_section=learn_section)

    def update_article(self, deployment_id: str, section_id: str, article: LearnArticle) -> str:
        article_id = self.repo.update_learn_article(
            deployment_id=deployment_id,
            section_id=section_id,
            article=article,
        )
        return article_id

    def update_key_action(self, deployment_id: str, key_action: KeyActionConfig) -> str:
        localizations = key_action.set_dynamic_localizations(f"{LOCALIZATION_DYNAMIC_KEY_PREPEND}#{key_action.id}")
        key_action_id, updated = self.repo.update_key_action(
            deployment_id=deployment_id,
            key_action_id=key_action.id,
            key_action=key_action,
        )
        self.update_localization(
            deployment_id=deployment_id,
            locale=self.retrieve_deployment_with_fields(deployment_id, [DeploymentDTO.LANGUAGE]).language,
            localizations=localizations,
        )
        if updated:
            self._post_key_action_update(key_action_id, deployment_id)
        return key_action_id

    def delete_deployment(self, deployment_id) -> None:
        self.repo.delete_deployment(deployment_id=deployment_id)
        event_bus = inject.instance(EventBusAdapter)
        event_bus.emit(DeleteDeploymentEvent(deployment_id))

    def delete_module_config(self, deployment_id: str, module_config_id: str):
        self.retrieve_deployment(deployment_id)
        self.repo.delete_module_config(deployment_id=deployment_id, module_config_id=module_config_id)
        self._post_module_config_delete(deployment_id, module_config_id)

    def delete_onboarding_module_config(self, deployment_id: str, onboarding_config_id: str) -> None:
        deployment = self.retrieve_deployment(deployment_id)
        event = PreDeleteOnboardingModuleConfigEvent(deployment, onboarding_config_id)
        self._event_bus.emit(event, raise_error=True)
        self.repo.delete_onboarding_module_config(
            deployment_id=deployment_id, onboarding_config_id=onboarding_config_id
        )

    def delete_key_action(
        self,
        deployment_id: str,
        key_action_id: str = None,
        module_config_id: str = None,
    ):
        assert (key_action_id and not module_config_id) or (module_config_id and not key_action_id)
        self.repo.delete_key_action(
            deployment_id=deployment_id,
            key_action_id=key_action_id,
            module_config_id=module_config_id,
        )
        self._post_key_action_delete(deployment_id, key_action_id, module_config_id)

    def delete_key_actions(
        self,
        deployment_id: str,
        exclude_types: tuple | None = None,
    ):
        deleted_ids = self.repo.delete_key_actions(deployment_id=deployment_id, exclude_types=exclude_types)
        for key_action_id in deleted_ids:
            self._post_key_action_delete(key_action_id=key_action_id, deployment_id=deployment_id)

    def is_consent_signed(self, user_id: str, deployment: DeploymentDTO) -> bool:
        consent = deployment.latest_consent
        if not consent:
            return True

        is_consent_enabled = self.is_onboarding_config_enable(deployment, "Consent")
        is_us_consent_enabled = self.is_onboarding_config_enable(deployment, "USConsent")
        if not is_consent_enabled and not is_us_consent_enabled:
            return True

        log_count = self.consent_repo.retrieve_log_count(
            consent_id=consent.id,
            revision=consent.revision,
            user_id=user_id,
        )
        return log_count > 0

    @staticmethod
    def is_onboarding_config_enable(deployment: DeploymentDTO, onboarding_id: str) -> bool:
        onboarding_configs = deployment.onboardingConfigs or []
        config: Optional[OnboardingModuleConfigDTO] = find(
            lambda c: c.onboardingId == onboarding_id, onboarding_configs
        )
        return config and config.is_enabled()

    def encrypt_value(self, deployment_id: str, value: str) -> str:
        # for now, ignoring deployment_id
        secret = self.config.server.deployment.encryptionSecret
        if secret:
            return encrypt(value, secret)
        else:
            raise EncryptionSecretNotAvailable

    def create_or_update_roles(self, deployment_id: str, roles: list[RoleDTO]) -> list[str]:
        deployment = self.retrieve_deployment(deployment_id)
        deployment.validate_roles(roles)
        update_role_ids = list(filter(None, [role.id for role in roles]))
        dep_roles = deployment.roles or []
        deleted_ids = [role.id for role in dep_roles if role.id not in update_role_ids]
        updated_ids = self.repo.create_or_update_roles(deployment_id=deployment_id, roles=roles)
        if deleted_ids:
            self._post_delete_roles_event(deleted_ids, deployment.id)
        return updated_ids

    def create_econsent(self, deployment_id: str, econsent: EConsent) -> str:
        return self.econsent_repo.create_econsent(deployment_id=deployment_id, econsent=econsent)

    def retrieve_consent_logs(self, user_id: str, limit: int = 1) -> list[ConsentLogDTO]:
        return self.consent_repo.retrieve_consent_logs_for_user(user_id=user_id, limit=limit)

    def is_econsent_signed(self, user_id: str, deployment: DeploymentDTO) -> bool:
        econsent = deployment.latest_econsent
        if not econsent:
            return True

        if not self.is_onboarding_config_enable(deployment, "EConsent"):
            return True

        log_count = self.econsent_repo.retrieve_log_count(
            consent_id=econsent.id, revision=econsent.revision, user_id=user_id
        )
        return log_count > 0

    def retrieve_econsent_logs(
        self, econsent_id: str, auth_user: AuthorizedUser, is_for_manager: bool
    ) -> list[EConsentLogDTO]:
        user_id = self._get_user_id(auth_user)
        if is_for_manager:
            result = self.econsent_repo.retrieve_signed_econsent_logs(econsent_id=econsent_id, user_id=user_id)
        else:
            result = self.econsent_repo.retrieve_econsent_pdfs(econsent_id=econsent_id, user_id=user_id)
            if not result:
                raise EConsentLogDoesNotExist

        return result

    @func_memory_cache()
    def retrieve_localization(self, deployment_id: str, locale: str) -> (Optional[str], dict):
        return self.repo.retrieve_localization(deployment_id=deployment_id, locale=locale)

    def update_localization(self, deployment_id: str, locale: str, localizations: dict) -> (Optional[str], dict):
        update = {}
        for key, value in localizations.items():
            update[f"{DeploymentDTO.LOCALIZATIONS}.{locale}.{key}"] = value
        return self.repo.update_deployment_fields(deployment_id=deployment_id, fields=update)

    def retrieve_module_ids(self, deployment_id: str) -> (Optional[str], dict):
        return self.repo.retrieve_module_ids(deployment_id=deployment_id)

    def retrieve_language(self, deployment_id: str) -> str:
        return self.repo.retrieve_language(deployment_id=deployment_id)

    def delete_localizations_by_keys(self, deployment_id: str, localization_keys: list[str]):
        self.repo.delete_localizations_by_keys(deployment_id=deployment_id, keys=localization_keys)

    @cached_property
    def _module_ids_with_no_disclaimer_config(self):
        modules_with_no_disclaimer_config = [
            LicensedQuestionnaireModule,
            AwarenessTrainingModule,
            AZScreeningQuestionnaireModule,
            BackgroundInformationModule,
            ECGHealthKitModule,
            HighFrequencyStepModule,
            StepModule,
            SurgeryDetailsModule,
        ]
        excluded_modules = []
        for module in modules_with_no_disclaimer_config:
            excluded_modules.extend(get_all_subclasses(module))
        excluded_module_ids = [
            m.moduleId for m in excluded_modules
        ] + excluded_modules_ids_for_applying_default_disclaimer_config
        return set(excluded_module_ids)

    def apply_default_disclaimer_configs(self, deployment: DeploymentDTO):
        if not self.config.server.moduleResult.applyDefaultDisclaimerConfig or not deployment.moduleConfigs:
            return
        default_footnote = Footnote.from_dict(
            {
                Footnote.ENABLED: True,
                Footnote.TEXT: LocalizationKeys.DEFAULT_DISCLAIMER_TEXT,
            }
        )
        for module_config in deployment.moduleConfigs:
            if module_config.configBody:
                if module_config.configBody.get("trademarkText"):
                    continue
                if module_config.get_questionnaire_type() in QuestionnaireConstants.NO_DEFAULT_DISCLAIMER:
                    continue
                if module_config.configBody.get("isPAM") or module_config.configBody.get("ispam"):
                    continue
            if module_config.moduleId in self._module_ids_with_no_disclaimer_config:
                continue
            footnote = module_config.footnote
            if not footnote or (not footnote.text and footnote.enabled):
                module_config.footnote = default_footnote

    @staticmethod
    def _get_user_id(auth_user: AuthorizedUser) -> typing.Optional[str]:
        if auth_user.is_user():
            return auth_user.id
        elif auth_user.is_manager():
            return None
        else:
            raise PermissionDenied

    @staticmethod
    def _post_key_action_create(deployment_id: str, key_action_id: str):
        event_bus = inject.instance(EventBusAdapter)
        event_bus.emit(PostCreateKeyActionConfigEvent(key_action_id, deployment_id))

    @staticmethod
    def _post_key_action_update(key_action_config_id: str, deployment_id: str):
        # TODO make async
        event_bus = inject.instance(EventBusAdapter)
        event_bus.emit(PostUpdateKeyActionConfigEvent(key_action_config_id, deployment_id))

    @staticmethod
    def _post_key_action_delete(deployment_id: str = None, key_action_id: str = None, module_config_id: str = None):
        event_bus = inject.instance(EventBusAdapter)
        event_bus.emit(
            PostDeleteKeyActionConfigEvent(
                deployment_id=deployment_id, key_action_id=key_action_id, module_config_id=module_config_id
            )
        )

    @staticmethod
    def _post_module_config_delete(deployment_id: str, module_config_id: str):
        event = ModuleConfigDeleteDisableEvent(deployment_id, module_config_id)
        event_bus = inject.instance(EventBusAdapter)
        event_bus.emit(event)

    @staticmethod
    def _post_delete_roles_event(deleted_ids: list[str], deployment_id: str):
        event = DeleteDeploymentCustomRolesEvent(deleted_ids, deployment_id)
        event_bus = inject.instance(EventBusAdapter)
        event_bus.emit(event)

    def _set_mandatory_profile_fields(self, module_id: str, deployment: DeploymentDTO):
        module = self.modules_manager.find_module(module_id=module_id)
        if not (userProfileMandatoryFields := module.userProfileMandatoryFields):
            return

        profile = deployment.profile or ProfileFields()
        fields = profile.fields or ProfileFields()
        mandatory_fields = set(fields.mandatoryOnboardingFields or [])

        for profile_field_str in userProfileMandatoryFields:
            setattr(fields, profile_field_str, True)
            if profile_field_str == ProfileFields.GENDER and not fields.genderOptions:
                setattr(
                    fields,
                    ProfileFields.GENDER_OPTIONS,
                    ProfileFields._get_default_gender_options(),
                )

        profile.fields = fields
        profile.fields.mandatoryOnboardingFields = list(mandatory_fields.union(module.userProfileMandatoryFields))
        self.repo.update_deployment_fields(
            deployment_id=deployment.id, fields={DeploymentDTO.PROFILE: profile.to_dict()}
        )

    def _check_SaMD(self, current: DeploymentDTO, previous: DeploymentDTO = None):
        is_SaMD_changed = (
            previous is not None
            and previous.isSaMD is not None
            and current.isSaMD is not None
            and previous.isSaMD != current.isSaMD
        )
        if is_SaMD_changed:
            raise PermissionDenied("SaMD field cannot be updated. Please contact support.")

        SaMD = self.config.server.deployment.configSaMD
        is_SaMD_allowed = SaMD and SaMD.enabled
        if not is_SaMD_allowed and current.isSaMD:
            raise PermissionDenied("SaMD cannot be enabled. Please contact support.")

    def _check_profile_mandatory_fields(self, deployment: DeploymentDTO, deployment_before_update: DeploymentDTO):
        if (
            deployment.profile is None
            or deployment.profile.fields is None
            or deployment.profile.fields.mandatoryOnboardingFields is None
        ):
            return

        mandatory_fields = self._get_deployment_mandatory_fields(deployment_before_update)
        requested_mandatory_fields = set(deployment.profile.fields.mandatoryOnboardingFields)
        missing = mandatory_fields - requested_mandatory_fields
        if missing:
            raise InvalidRequestException(f"Field [{missing.pop()}] is mandatory")

    def _get_deployment_mandatory_fields(self, deployment: DeploymentDTO) -> set:
        mandatory_fields = set()
        for module_config in deployment.moduleConfigs:
            module = self.modules_manager.find_module(module_id=module_config.moduleId)
            if module_config.status == EnableStatus.ENABLED and module.userProfileMandatoryFields:
                mandatory_fields.update(module.userProfileMandatoryFields)
        return mandatory_fields

    def create_component_config(
        self,
        deployment_id: str,
        component_name: str,
        config: BasePhoenixDynamicConfig,
        update_revision: bool = True,
    ):
        self.retrieve_deployment(deployment_id=deployment_id)
        config.createDateTime = config.updateDateTime = datetime.now(tz=timezone.utc)
        self.repo.create_component_config(
            deployment_id=deployment_id,
            component_name=component_name,
            config=config,
            update_revision=update_revision,
        )

    def create_or_update_component_config(
        self,
        deployment_id: str,
        component_name: str,
        config: BasePhoenixDynamicConfig,
        removed_fields: list[str],
        update_revision: bool = True,
    ):
        self.retrieve_deployment(deployment_id=deployment_id)
        config.updateDateTime = datetime.now(tz=timezone.utc)
        self.repo.create_or_update_component_config(
            deployment_id=deployment_id,
            component_name=component_name,
            config=config,
            removed_fields=removed_fields,
            update_revision=update_revision,
        )

    def delete_component_config(self, deployment_id: str, component_name: str):
        self.repo.delete_component_config(deployment_id=deployment_id, component_name=component_name)

    def bulk_delete_user_consent_logs(self, user_id: str) -> int:
        return self.consent_repo.bulk_delete_user_consent_logs(user_id=user_id)

    def bulk_delete_user_econsent_logs(self, user_id: str):
        return self.econsent_repo.bulk_delete_user_econsent_logs(user_id=user_id)

    def retrieve_sso_config_id(self, deployment_id: str) -> str:
        return self.repo.retrieve_sso_config_id(deployment_id=deployment_id)

    @staticmethod
    def get_invitee_sso_config(invitation: InvitationDTO):
        deployment_id = invitation.deployment_ids[0]
        configuration = DeploymentService().retrieve_deployment(deployment_id)
        return configuration.sso

    @staticmethod
    def get_user_sso_config(auth_repo_user):
        from sdk.authorization.services.authorization import AuthorizationService

        user_profile = AuthorizationService().retrieve_user_profile(auth_repo_user.id)
        authz_user = AuthorizedUser(user_profile)
        return authz_user.deployment.sso

    def update_deployment_fields(self, deployment_id: str, fields: dict) -> str:
        return self.repo.update_deployment_fields(deployment_id, fields)

    def create_deployment_revision(self, deployment_revision: DeploymentRevisionDTO):
        return self.repo.create_deployment_revision(deployment_revision=deployment_revision)

    def create_deployment_labels(self, deployment_id, labels: list[Label]):
        return self.repo.create_deployment_labels(deployment_id=deployment_id, labels=labels)

    def create_deployment_template(self, template: DeploymentTemplateDTO) -> str:
        return self.repo.create_deployment_template(template)

    def create_draft(self, original: DeploymentDTO) -> str:
        return self.repo.create_draft(original)

    def delete_deployment_label(self, deployment_id: str, label_id: str):
        return self.repo.delete_deployment_label(deployment_id=deployment_id, label_id=label_id)

    def retrieve_deployment_template(self, template_id: str) -> DeploymentTemplateDTO:
        return self.repo.retrieve_deployment_template(template_id)

    def delete_deployment_template(self, template_id: str):
        return self.repo.delete_deployment_template(template_id)

    def retrieve_files_in_library(
        self,
        library_id: str,
        deployment_id: str = None,
        offset: int = 0,
        limit: int = 100,
    ):
        return self.repo.retrieve_files_in_library(library_id, deployment_id, offset, limit)

    def update_full_deployment(self, deployment: DeploymentDTO) -> str:
        return self.repo.update_full_deployment(deployment=deployment)

    def update_localizations(self, deployment_id: str, localizations: dict) -> str:
        return self.repo.update_localizations(deployment_id=deployment_id, localizations=localizations)

    def publish_draft(self, draft: DeploymentDTO, name: str, submitter_id: str) -> str:
        return self.repo.publish_draft(draft, name, submitter_id)

    def reorder_onboarding_module_configs(self, deployment_id: str, ordering_data: list[OrderUpdateObject]):
        return self.repo.reorder_onboarding_module_configs(
            deployment_id=deployment_id,
            ordering_data=ordering_data,
        )

    def reorder_module_configs(self, deployment_id: str, ordering_data: list[OrderUpdateObject]):
        return self.repo.reorder_module_configs(
            deployment_id=deployment_id,
            ordering_data=ordering_data,
        )

    def reorder_learn_sections(self, deployment_id: str, ordering_data: list[OrderUpdateObject]):
        return self.repo.reorder_learn_sections(
            deployment_id=deployment_id,
            ordering_data=ordering_data,
        )

    def reorder_learn_articles(
        self,
        deployment_id: str,
        section_id: str,
        ordering_data: list[OrderUpdateObject],
    ):
        return self.repo.reorder_learn_articles(
            deployment_id=deployment_id,
            section_id=section_id,
            ordering_data=ordering_data,
        )

    def retrieve_deployment_templates(self, organization_id: str = None) -> list[DeploymentTemplateDTO]:
        return self.repo.retrieve_deployment_templates(organization_id=organization_id)

    def rollback_draft(self, deployment_id: str, version: str) -> str:
        return self.repo.rollback_draft(deployment_id=deployment_id, version=version)

    def retrieve_deployment_labels(self, deployment_id: str) -> list[Label]:
        return self.repo.retrieve_deployment_labels(deployment_id=deployment_id)

    def update_deployment_labels(self, deployment_id, labels: list[Label], updated_label: Optional[Label]):
        return self.repo.update_deployment_labels(
            deployment_id=deployment_id, labels=labels, updated_label=updated_label
        )

    def update_deployment_template(self, template_id: str, template: DeploymentTemplateDTO) -> str:
        return self.repo.update_deployment_template(template_id=template_id, template=template)

    def retrieve_all_localization(self, deployment_id: str) -> (dict, Optional[str]):
        return self.repo.retrieve_all_localization(deployment_id=deployment_id)

    def retrieve_deployment_revision_by_module_config_version(
        self,
        deployment_id: str,
        module_id: str,
        module_config_version: int,
        module_config_id: str = None,
        config_body_id: str = None,
    ) -> Optional[DeploymentRevisionDTO]:
        # ToDo: check and make sure it is works correctly for the Configs when the modules are in the CMS
        return self.repo.retrieve_deployment_revision_by_module_config_version(
            deployment_id=deployment_id,
            module_id=module_id,
            module_config_id=module_config_id,
            module_config_version=module_config_version,
        )

    def check_deployment_exists(self, deployment_id: str) -> bool:
        return self.repo.check_deployment_exists(deployment_id=deployment_id)

    def get_active_deployment_ids_in_organizations(self, organization_ids: list[str]) -> list[str]:
        return self.repo.get_active_deployment_ids_in_organizations(organization_ids)

    def remove_cms_article_references(self, deployment_id: str, article_id: str):
        deployment = self.retrieve_deployment_with_fields(
            deployment_id=deployment_id, only_fields=[DeploymentDTO.KEY_ACTIONS, DeploymentDTO.MODULE_CONFIGS]
        )

        for ka in deployment.keyActions or []:
            if ka.learnArticleId == article_id:
                self.delete_key_action(deployment_id=deployment_id, key_action_id=ka.id)

        module_configs_updated = False
        for mc in deployment.moduleConfigs or []:
            if article_id in (mc.learnArticleIds or []):
                mc.learnArticleIds.remove(article_id)
                module_configs_updated = True

        if module_configs_updated:
            module_configs = [config.to_dict(include_none=False) for config in deployment.moduleConfigs]
            if module_configs:
                self.update_deployment_fields(
                    deployment_id=deployment_id, fields={DeploymentDTO.MODULE_CONFIGS: module_configs}
                )

    def retrieve_deployment_ids_with_key_actions(self) -> list[str]:
        return self.repo.retrieve_deployment_ids_with_key_actions()


class LocalizationKeys:
    DEFAULT_DISCLAIMER_TEXT = "hu_module_config_default_disclaimer_text"
