import json
import logging
from copy import copy, deepcopy
from datetime import UTC, datetime
from typing import Optional

import deepdiff
from bson import ObjectId
from django.db import connection, models
from django.db.models import Case, F, Func, Q, Subquery, Value, When
from django.db.models.expressions import RawSQL
from django.db.models.functions import Cast
from django.db.transaction import atomic
from django.utils.safestring import SafeString

from sdk.authorization.dtos.role.role import RoleDTO
from sdk.common.adapter.event_bus_adapter import EventBusAdapter
from sdk.common.common_models.sort import SortField
from sdk.common.exceptions.exceptions import ObjectDoesNotExist
from sdk.common.localization.utils import Language
from sdk.common.utils import inject
from sdk.common.utils.common_functions_utils import escape
from sdk.common.utils.file_utils import convert_complex_types_in_jsonb_to_str
from sdk.common.utils.format_sort_field import format_sort_fields
from sdk.common.utils.validators import model_to_dict, remove_none_values
from sdk.deployment.dtos.builder import Builder
from sdk.deployment.dtos.deployment import (
    ChangeType,
    DeploymentDTO,
    DeploymentRevisionDTO,
    DeploymentTemplateDTO,
    Label,
    ModuleConfig,
    OnboardingModuleConfigDTO,
)
from sdk.deployment.dtos.deployment_info import DeploymentInfo
from sdk.deployment.dtos.learn import (
    Learn,
    LearnArticle,
    LearnSection,
    OrderUpdateObject,
)
from sdk.deployment.dtos.status import EnableStatus, Status
from sdk.deployment.events import ModuleConfigDeleteDisableEvent
from sdk.deployment.exceptions import (
    ComponentConfigAlreadyExists,
    ComponentConfigDoesNotExist,
    DeploymentDoesNotExist,
    DeploymentWithVersionNumberDoesNotExist,
    LearnArticleDoesNotExist,
    LearnSectionDoesNotExist,
)
from sdk.deployment.models import DeploymentRevision, DeploymentTemplate
from sdk.deployment.models.deployment import Deployment
from sdk.deployment.repository.consent_repository import ConsentRepository
from sdk.deployment.repository.deployment_repository import DeploymentRepository
from sdk.deployment.repository.econsent_repository import EConsentRepository
from sdk.key_action.models.config import KeyActionConfig
from sdk.module_result.dtos.module_config import FilledBy
from sdk.organization.models import Organization
from sdk.phoenix.config.dynamic_component_config import BasePhoenixDynamicConfig
from sdk.storage.dtos import S3Object
from sdk.storage.dtos.file_storage import FileStorageDTO
from sdk.storage.models import FileStorage

logger = logging.getLogger(__name__)


class PostgresDeploymentRepository(DeploymentRepository):
    """Repository to work with deployment collection."""

    IGNORED_FIELDS = (
        DeploymentDTO.CREATE_DATE_TIME,
        DeploymentDTO.UPDATE_DATE_TIME,
    )

    DEPLOYMENT_ID = "deploymentId"

    def create_deployment_revision(self, deployment_revision: DeploymentRevisionDTO):
        current: DeploymentDTO = self.retrieve_deployment(deployment_id=deployment_revision.deploymentId)
        if current.status is Status.DRAFT:
            return self._create_draft_revision(deployment_revision, current)

        return self._create_deployment_revision(deployment_revision)

    def _create_draft_revision(self, revision: DeploymentRevisionDTO, current: DeploymentDTO):
        deployment_id = ObjectId(revision.deploymentId)

        previous_revision = (
            DeploymentRevision.objects.filter(deploymentId=deployment_id)
            .order_by("-version", "-updateDateTime")
            .first()
        )
        revision_doc = revision.to_dict(
            include_none=False,
            ignored_fields=self.IGNORED_FIELDS,
            exclude_fields=(DeploymentRevisionDTO.SNAP,),  # dump snapshot separetly
        )
        snapshot = revision.snap.to_dict(
            include_none=False,
            ignored_fields=self.IGNORED_FIELDS,
        )
        deployment_doc = current.to_dict(include_none=False, ignored_fields=self.IGNORED_FIELDS)
        if not (change := self._calculate_revision_diff(deployment_doc, snapshot)):
            return str(revision.deploymentId)

        now = datetime.utcnow()

        for key in change:
            if isinstance(change[key], deepdiff.diff.SetOrdered):
                change[key] = list(change[key])

        change[DeploymentRevisionDTO.SUBMITTER_ID] = ObjectId(revision.submitterId)
        change[DeploymentRevisionDTO.CREATE_DATE_TIME] = now

        changes = []
        if previous_revision:
            changes = previous_revision.changes or []

        change = convert_complex_types_in_jsonb_to_str(change, 0, 0)
        changes.append(change)
        if previous_revision:
            DeploymentRevision.objects.filter(mongoId=previous_revision.mongoId).update(
                changes=changes,
                updateDateTime=now,
            )
        else:
            revision_doc[DeploymentRevisionDTO.SNAP] = snapshot
            revision_doc[DeploymentRevisionDTO.CHANGES] = changes
            revision_doc[DeploymentRevisionDTO.UPDATE_DATE_TIME] = now
            revision_doc[DeploymentRevisionDTO.CREATE_DATE_TIME] = now
            revision_doc = convert_complex_types_in_jsonb_to_str(revision_doc)
            DeploymentRevision.objects.create(**revision_doc)

        return str(revision.deploymentId)

    def _create_deployment_revision(self, revision: DeploymentRevisionDTO):
        document = revision.to_dict(include_none=False, ignored_fields=self.IGNORED_FIELDS)
        now = datetime.utcnow()
        document[DeploymentRevisionDTO.UPDATE_DATE_TIME] = now
        document[DeploymentRevisionDTO.CREATE_DATE_TIME] = now
        result = DeploymentRevision(**document)
        result.save()
        return str(result.mongoId)

    """ Create document section """

    def create_deployment(self, deployment: DeploymentDTO, deployment_id: ObjectId = None) -> str:
        now = datetime.utcnow()
        deployment.createDateTime = deployment.updateDateTime = now

        deployment.status = Status.DRAFT
        deployment.moduleConfigs = []
        deployment.onboardingConfigs = []
        deployment.keyActions = []
        deployment.builder = Builder(tabs=[])

        deployment.learn = Learn(id=ObjectId(), updateDateTime=now, createDateTime=now)

        ignored_fields = (
            *self.IGNORED_FIELDS,
            f"{DeploymentDTO.LEARN}.{Learn.UPDATE_DATE_TIME}",
            f"{DeploymentDTO.LEARN}.{Learn.CREATE_DATE_TIME}",
        )
        deployment_dict = deployment.to_dict(ignored_fields=ignored_fields)
        deployment_dict = remove_none_values(deployment_dict)
        if deployment_id:
            deployment_dict["mongoId"] = str(deployment_id)
        if deployment.id:
            deployment_dict["mongoId"] = str(deployment.id)
        deployment_dict.pop(DeploymentDTO.ID, None)
        deployment_dict = convert_complex_types_in_jsonb_to_str(deployment_dict)
        result = Deployment(**deployment_dict)
        result.save()

        for mc in result.moduleConfigs or []:
            self._publish_module_config_event(str(result.mongoId), old=None, new=mc)

        return str(result.mongoId)

    def create_draft(self, original: DeploymentDTO) -> str:
        draft = original.to_dict(include_none=False, ignored_fields=self.IGNORED_FIELDS)
        draft.pop(DeploymentDTO.ID)
        draft[DeploymentDTO.STATUS] = Status.DRAFT.value
        draft_doc = Deployment(**draft)
        draft_doc.save()
        draft_id = draft_doc.mongoId
        Deployment.objects.filter(mongoId=original.id).update(draftId=draft_id)

        for mc in draft_doc.moduleConfigs or []:
            self._publish_module_config_event(str(draft_id), old=None, new=mc)

        return str(draft_id)

    @atomic()
    def publish_draft(self, draft: DeploymentDTO, name: str, submitter_id: str) -> str:
        draft_id = ObjectId(draft.id)
        draft_doc = draft.to_dict(
            include_none=False,
            exclude_fields={DeploymentDTO.ID, "mongoId"},
            ignored_fields=self.IGNORED_FIELDS,
        )
        draft_doc[DeploymentDTO.STATUS] = Status.DEPLOYED.value
        original = Deployment.objects.filter(draftId=draft_id).first()
        original_id = None
        version = DeploymentDTO.FIRST_VERSION
        if original:
            original_id = original.mongoId
            version = original.version or DeploymentDTO.FIRST_VERSION

        original_id = ObjectId(original_id)
        draft_doc["mongoId"] = draft_doc.pop(DeploymentDTO.ID, original_id)
        draft_doc[DeploymentDTO.DRAFT_ID] = draft_id
        draft_doc[DeploymentDTO.VERSION] = version + 1
        draft_doc = model_to_dict(Deployment(**draft_doc))
        draft_doc["mongoId"] = draft_doc.pop(DeploymentDTO.ID, original_id)

        existing_deployment = Deployment.objects.filter(mongoId=original_id).first()
        if not existing_deployment:
            existing_deployment = Deployment(**draft_doc)
        else:
            for key, value in draft_doc.items():
                setattr(existing_deployment, key, value)
        existing_deployment.version = draft_doc[DeploymentDTO.VERSION]
        existing_deployment.save()

        for revision in DeploymentRevision.objects.filter(deploymentId=draft_id):
            revision.deploymentId = original_id
            revision.snap = revision.snap or {}
            revision.snap["id"] = str(original_id)
            revision.save()

        now = datetime.utcnow()
        deployment_revision = {
            DeploymentRevisionDTO.DEPLOYMENT_ID: original_id,
            DeploymentRevisionDTO.NAME: name,
            DeploymentRevisionDTO.CHANGE_TYPE: ChangeType.PUBLISH.value,
            DeploymentRevisionDTO.VERSION: draft_doc[DeploymentDTO.VERSION],
            DeploymentRevisionDTO.UPDATE_DATE_TIME: now,
            DeploymentRevisionDTO.CREATE_DATE_TIME: now,
            DeploymentRevisionDTO.SNAP: draft_doc,
            DeploymentRevisionDTO.SUBMITTER_ID: ObjectId(submitter_id),
            DeploymentRevisionDTO.CHANGES: [],
        }
        deployment_revision = convert_complex_types_in_jsonb_to_str(deployment_revision)
        DeploymentRevision.objects.create(**deployment_revision)
        return str(original_id)

    def rollback_draft(self, deployment_id: str, version: str) -> str:
        """
        @deployment_id - str, id of deployment to roll back.
        @version - str, version to roll back to. Format: "x.y".
        "x.0" rolls back to the original x version.
        "x.y" rolls back to the x version of draft with all changes up to index "y-1".
        """
        draft_version, change_index = list(map(int, version.split(".")))
        draft_revision: dict = self._retrieve_revision(deployment_id=deployment_id, version=draft_version)
        if not draft_revision:
            raise DeploymentWithVersionNumberDoesNotExist

        new_draft = draft_revision["snap"]
        changes = draft_revision["changes"]
        for i in range(change_index):
            try:
                change = changes[i]
            except IndexError:
                raise DeploymentWithVersionNumberDoesNotExist

            if not change:
                continue

            new_draft += deepdiff.Delta(change, mutate=True)

        db_id = Deployment.objects.filter(mongoId=deployment_id).values_list("id", flat=True).first()
        new_draft["mongoId"] = new_draft.pop("id")
        new_draft["id"] = db_id
        draft_doc = Deployment(**new_draft)
        draft_doc.updateDateTime = datetime.utcnow()
        draft_doc.save()

        DeploymentRevision.objects.filter(deploymentId=deployment_id, version=draft_version).update(
            changes=changes[:change_index],
            updateDateTime=datetime.utcnow(),
        )
        return deployment_id

    def create_onboarding_module_config(
        self, deployment_id: str, config: OnboardingModuleConfigDTO, update_revision: bool = True
    ) -> str:
        config.id = config.id or str(ObjectId())
        module_config_dict = remove_none_values(config.to_dict())
        module_config_dict = convert_complex_types_in_jsonb_to_str(module_config_dict, 0, 0)
        changes = {
            "updateDateTime": datetime.utcnow(),
            "onboardingConfigs": RawSQL(
                '"onboardingConfigs" || %s',
                (json.dumps(module_config_dict),),
                output_field=models.JSONField(),
            ),
        }
        if update_revision:
            changes.update(**{"version": F("version") + Value(1)})

        count = Deployment.objects.filter(mongoId=deployment_id).update(**changes)
        if count == 0:
            raise DeploymentDoesNotExist

        return str(config.id)

    @atomic()
    def update_onboarding_module_config(
        self,
        deployment_id: str,
        onboarding_module_config: OnboardingModuleConfigDTO,
        update_revision: bool = True,
    ) -> str:
        onboarding_config_dict = remove_none_values(onboarding_module_config.to_dict())
        onboarding_module_config_id = str(ObjectId(onboarding_module_config.id))
        onboarding_config_dict[OnboardingModuleConfigDTO.ID] = onboarding_module_config_id
        del onboarding_config_dict[OnboardingModuleConfigDTO.VERSION]
        del onboarding_config_dict[OnboardingModuleConfigDTO.ID]

        deployment = Deployment.objects.filter(mongoId=deployment_id).select_for_update().first()
        if not deployment:
            return str(onboarding_module_config_id)

        config_found = False
        for config in deployment.onboardingConfigs:
            if config["id"] == onboarding_module_config_id:
                config.update(onboarding_config_dict)
                if update_revision:
                    config[OnboardingModuleConfigDTO.VERSION] += 1
                config_found = True
                break

        if not config_found:
            raise DeploymentDoesNotExist

        deployment.updateDateTime = datetime.now()
        if update_revision:
            deployment.version += 1
        deployment.save()

    def create_module_config(self, deployment_id: str, config: ModuleConfig, update_revision: bool = True) -> str:
        config.id = str(ObjectId())
        module_config_dict = remove_none_values(config.to_dict(ignored_fields=self.IGNORED_FIELDS))
        module_config_dict = convert_complex_types_in_jsonb_to_str(module_config_dict, 0, 0)
        count = Deployment.objects.filter(mongoId=deployment_id).update(
            updateDateTime=datetime.utcnow(),
            moduleConfigs=RawSQL(
                '"moduleConfigs" || %s',
                (json.dumps(module_config_dict),),
                output_field=models.JSONField(),
            ),
            **({"version": F("version") + Value(1)} if update_revision else {}),
        )
        if count == 0:
            raise DeploymentDoesNotExist

        self._publish_module_config_event(deployment_id, old=None, new=module_config_dict)
        return str(config.id)

    @atomic()
    def update_module_config(
        self,
        deployment_id: str,
        config: ModuleConfig,
        update_revision: bool = True,
    ) -> str:
        module_config_id = str(config.id)
        config.updateDateTime = datetime.utcnow()
        module_config_dict = remove_none_values(config.to_dict(ignored_fields=self.IGNORED_FIELDS))
        del module_config_dict[ModuleConfig.VERSION]
        del module_config_dict[ModuleConfig.ID]

        deployment = Deployment.objects.filter(mongoId=str(deployment_id)).select_for_update().first()
        if not deployment:
            return str(module_config_id)

        module_config_found = False
        for mc in deployment.moduleConfigs:
            if mc["id"] == module_config_id:
                module_config_found = True
                break
        if not module_config_found:
            raise DeploymentDoesNotExist

        original_config = None
        for module_config in deployment.moduleConfigs:
            if module_config["id"] == module_config_id:
                original_config = deepcopy(module_config)
                module_config.update(module_config_dict)
                if update_revision:
                    module_config[ModuleConfig.VERSION] = module_config.get(ModuleConfig.VERSION, 0) + 1
                break

        deployment.moduleConfigs = convert_complex_types_in_jsonb_to_str(deployment.moduleConfigs, 0, 0)
        deployment.updateDateTime = datetime.now()
        if update_revision:
            deployment.version += 1
        deployment.save()

        if original_config:
            module_config_dict["id"] = module_config_id
            self._publish_module_config_event(deployment_id, old=original_config, new=module_config_dict)

        return str(module_config_id)

    @atomic()
    def create_component_config(
        self,
        deployment_id: str,
        component_name: str,
        config: BasePhoenixDynamicConfig,
        update_revision: bool = True,
    ):
        config_dict = remove_none_values(config.to_dict(ignored_fields=self.IGNORED_FIELDS))
        deployment = Deployment.objects.filter(mongoId=deployment_id).first()
        if not deployment:
            raise DeploymentDoesNotExist

        component_configs = deployment.componentConfigs or {}
        if component_configs.get(component_name):
            raise ComponentConfigAlreadyExists

        component_configs[component_name] = config_dict
        deployment.componentConfigs = component_configs
        deployment.updateDateTime = datetime.utcnow()
        if update_revision:
            deployment.version += 1
        deployment.save()

    @atomic()
    def create_or_update_component_config(
        self,
        deployment_id: str,
        component_name: str,
        config: BasePhoenixDynamicConfig,
        removed_fields: list[str],
        update_revision: bool = True,
    ):
        deployment = Deployment.objects.filter(mongoId=deployment_id).first()
        if not deployment:
            raise DeploymentDoesNotExist

        component_configs = deployment.componentConfigs or {}
        if not component_configs.get(component_name):
            raise ComponentConfigDoesNotExist

        config_dict = remove_none_values(config.to_dict(ignored_fields=self.IGNORED_FIELDS))
        for field in removed_fields:
            config_dict.pop(field, None)

        config_dict[BasePhoenixDynamicConfig.UPDATE_DATE_TIME] = datetime.utcnow()
        component_configs[component_name] = config_dict
        deployment.componentConfigs = component_configs
        deployment.updateDateTime = datetime.utcnow()
        if update_revision:
            deployment.version += 1
        deployment.save()

    @atomic()
    def create_key_action(self, deployment_id: str, key_action: KeyActionConfig) -> str:
        key_action.id = ObjectId(key_action.id)
        key_action.createDateTime = key_action.updateDateTime = datetime.utcnow()

        key_action_dict = key_action.to_dict(
            include_none=False,
            ignored_fields=self.IGNORED_FIELDS,
        )
        key_action_dict = convert_complex_types_in_jsonb_to_str(key_action_dict, 0, 0)

        result = Deployment.objects.filter(mongoId=deployment_id).update(
            keyActions=RawSQL(
                '"keyActions" || %s',
                (json.dumps(key_action_dict),),
                output_field=models.JSONField(),
            ),
            updateDateTime=datetime.utcnow(),
        )
        if result == 0:
            raise DeploymentDoesNotExist
        return str(key_action.id)

    @atomic()
    def create_learn_section(self, deployment_id: str, learn_section: LearnSection) -> str:
        learn_section.createDateTime = learn_section.updateDateTime = datetime.utcnow()
        learn_section_dict = learn_section.to_dict(
            include_none=False,
            ignored_fields=self.IGNORED_FIELDS,
        )
        learn_section_dict[LearnSection.ID] = ObjectId()
        learn_section_dict = convert_complex_types_in_jsonb_to_str(learn_section_dict, 0, 0)
        deployment = Deployment.objects.filter(mongoId=deployment_id).first()
        if not deployment:
            raise DeploymentDoesNotExist

        deployment.learn = deployment.learn or {}
        deployment.learn[Learn.SECTIONS] = deployment.learn.get(Learn.SECTIONS, []) + [learn_section_dict]
        deployment.updateDateTime = datetime.utcnow()
        deployment.save()
        return str(learn_section_dict[LearnSection.ID])

    @atomic()
    def create_learn_article(self, deployment_id: str, section_id: str, learn_article: LearnArticle) -> str:
        learn_article.createDateTime = learn_article.updateDateTime = datetime.utcnow()
        learn_article.id = ObjectId()
        learn_article_dict = learn_article.to_dict(ignored_fields=self.IGNORED_FIELDS)
        deployment = Deployment.objects.filter(mongoId=deployment_id).first()
        if not deployment:
            raise DeploymentDoesNotExist

        section = None
        sections = deployment.learn.get(Learn.SECTIONS, [])
        for sec in sections:
            if sec["id"] == section_id:
                section = sec
                break

        if not section:
            raise LearnSectionDoesNotExist

        learn_article_dict = convert_complex_types_in_jsonb_to_str(learn_article_dict, 0, 0)
        section[LearnSection.ARTICLES] = section.get(LearnSection.ARTICLES, []) + [learn_article_dict]
        deployment.updateDateTime = datetime.utcnow()
        deployment.save()
        return str(learn_article.id)

    """ END Create document section """

    """ Retrieve documents section """

    def retrieve_deployments(
        self,
        skip: int = 0,
        limit: int = None,
        search_criteria: str = None,
        sort_fields: list[SortField] = None,
        status: list[Status] = None,
        ids: list[str] = None,
        fields: list[str] = None,
    ) -> tuple[list[DeploymentDTO], int]:
        query = Q()
        if status:
            query &= Q(status__in=[st.value for st in status])
        if search_criteria:
            query &= Q(name__iregex=escape(search_criteria)) | Q(mongoId__iregex=escape(search_criteria))
        if ids:
            query &= Q(mongoId__in=[str(id_) for id_ in ids if ObjectId.is_valid(id_)])
        formatted_sort: list[tuple[str, int]] = format_sort_fields(sort_fields or [], DeploymentDTO.VALID_SORT_FIELDS)
        order_by = [f"{'' if order == 1 else '-'}{field}" for field, order in formatted_sort]
        deployments = Deployment.objects.filter(query).order_by(*order_by)
        if limit:
            deployments = deployments[skip : skip + limit]
        else:
            deployments = deployments[skip:]
        if fields:
            fields = fields + ["mongoId"]
            deployments = deployments.values(*fields)

        count = Deployment.objects.filter(query).count()
        result = []
        for deployment in deployments:
            dic = model_to_dict(deployment) if not fields else {field: deployment[field] for field in fields}
            dic[DeploymentDTO.ID] = dic.pop("mongoId", dic.pop("id"))
            result.append(self.deployment_from_document(dic))
        return result, count

    def retrieve_deployments_by_ids(self, deployment_ids: list[str]) -> list[DeploymentDTO]:
        deployment_ids = [str(id_) for id_ in deployment_ids if ObjectId.is_valid(id_)]
        deployments = Deployment.objects.filter(mongoId__in=deployment_ids)
        return [self.deployment_from_document(model_to_dict(deployment)) for deployment in deployments]

    def retrieve_deployments_info(
        self, deployment_ids: list[str] = None, draft_ids: list[str] = None
    ) -> list[DeploymentInfo]:
        if not any((deployment_ids, draft_ids)):
            return []

        query = Q()
        values = ["mongoId", DeploymentDTO.CODE, DeploymentDTO.NAME, DeploymentDTO.STATUS]
        if deployment_ids:
            query &= Q(mongoId__in=[str(id_) for id_ in deployment_ids])
        else:
            query &= Q(draftId__in=[str(id_) for id_ in draft_ids])
            values.append(DeploymentDTO.DRAFT_ID)

        deployments = Deployment.objects.filter(query).values(*values)
        result = []
        for deployment in deployments:
            deployment["id"] = deployment.pop("mongoId")
            result.append(DeploymentInfo.from_dict(deployment))
        return result

    def retrieve_deployment(self, deployment_id: str, exclude_fields: list[str] = None) -> DeploymentDTO:
        deployment = Deployment.objects.filter(mongoId=deployment_id).first()
        if not deployment:
            raise DeploymentDoesNotExist
        dic = model_to_dict(deployment)
        [dic.pop(field, None) for field in exclude_fields or [] if field != DeploymentDTO.ID_]
        return self.deployment_from_document(dic)

    def retrieve_draft(self, deployment_id: str) -> DeploymentDTO:
        draft = Deployment.objects.filter(originalId=deployment_id).first()
        if not draft:
            raise DeploymentDoesNotExist
        return self.deployment_from_document(model_to_dict(draft))

    """ If the version does not exist in deployment then search in deploymentrevision """

    def retrieve_deployment_by_version_number(self, deployment_id: str, version_number: int) -> DeploymentDTO:
        result = Deployment.objects.filter(mongoId=deployment_id, version=version_number).first()
        if result is None:
            deployment_revision = self._retrieve_revision(deployment_id, version_number)
            if deployment_revision is None:
                if version_number == DeploymentDTO.FIRST_VERSION:
                    result = Deployment.objects.filter(mongoId=deployment_id).first()
                    if result is None:
                        raise DeploymentWithVersionNumberDoesNotExist
                else:
                    raise DeploymentWithVersionNumberDoesNotExist
            else:
                result = deployment_revision[DeploymentRevisionDTO.SNAP]
        else:
            result = model_to_dict(result)

        return self.deployment_from_document(result)

    def retrieve_deployment_ids_by_tags(self, tags: list[str]) -> list[str]:
        if not tags:
            return []
        query = Q()
        for tag in tags:
            query |= Q(tags__contains=[tag])
        ids = Deployment.objects.filter(query).values_list("mongoId", flat=True)
        return list(ids)

    def retrieve_revision(self, deployment_id: str, version: int = None) -> DeploymentRevisionDTO | None:
        """
        Retrieve deployment revision by deployment id and version number.
        Returns latest version if version number is not provided.
        """
        revision = self._retrieve_revision(deployment_id, version)
        if revision is None:
            return None

        return DeploymentRevisionDTO.from_dict(revision, use_validator_field=False)

    def _retrieve_revision(self, deployment_id: str, version: int = None) -> dict:
        query = Q()
        if deployment_id:
            query &= Q(deploymentId=deployment_id)
        if version:
            query &= Q(version=version)

        revision = DeploymentRevision.objects.filter(query).order_by("-updateDateTime").first()
        if revision is None:
            return None
        return model_to_dict(revision)

    def retrieve_deployment_revisions(self, deployment_id: str) -> list[DeploymentRevisionDTO]:
        revisions = DeploymentRevision.objects.filter(deploymentId=deployment_id)
        return [
            DeploymentRevisionDTO.from_dict(model_to_dict(revision), use_validator_field=False)
            for revision in revisions
        ]

    def retrieve_module_configs(self, deployment_id: str) -> list[ModuleConfig]:
        deployment = Deployment.objects.filter(mongoId=deployment_id).values(DeploymentDTO.MODULE_CONFIGS).first()
        if not deployment:
            raise DeploymentDoesNotExist

        module_configs = deployment.get(DeploymentDTO.MODULE_CONFIGS, [])
        return [ModuleConfig.from_dict(config, use_validator_field=False) for config in module_configs]

    def retrieve_module_configs_filled_by(
        self, deployment_ids: list[str], filled_by: list[FilledBy]
    ) -> list[ModuleConfig]:
        deployments = Deployment.objects.filter(mongoId__in=deployment_ids).values(DeploymentDTO.MODULE_CONFIGS)

        result = []
        for deployment in deployments:
            module_configs = deployment.get(DeploymentDTO.MODULE_CONFIGS, [])
            for config in module_configs:
                config_body = config.get(ModuleConfig.CONFIG_BODY, {})
                if config_body.get(ModuleConfig.FILLED_BY, "") in filled_by:
                    result.append(ModuleConfig.from_dict(config, use_validator_field=False))

        return result

    def check_export_short_code_module_config(self, short_code: str) -> bool:
        # TODO: #django-test make it faster and more efficient
        all_deployments = Deployment.objects.all()
        for deployment in all_deployments:
            for module_config in deployment.moduleConfigs:
                config_body = module_config.get("configBody", {})
                pages = config_body.get("pages", [])
                for page in pages:
                    items = page.get("items", [])
                    for item in items:
                        if item.get("exportShortCode") == short_code:
                            return True

        return False

    def check_deployment_exists(self, deployment_id: str) -> bool:
        return Deployment.objects.filter(mongoId=deployment_id).exists()

    def retrieve_onboarding_module_configs(self, deployment_id: str) -> list[OnboardingModuleConfigDTO]:
        deployment = Deployment.objects.filter(mongoId=deployment_id).first()
        if not deployment:
            raise DeploymentDoesNotExist

        onboarding_configs = deployment.onboardingConfigs or []
        return [OnboardingModuleConfigDTO.from_dict(config, use_validator_field=False) for config in onboarding_configs]

    def retrieve_article_by_id(self, article_id: str, deployment_id: str) -> LearnArticle:
        deployment = self.retrieve_deployment(deployment_id=deployment_id)
        for section in deployment.learn.sections or []:
            for article in section.articles or []:
                if article.id == article_id:
                    return article
        raise ObjectDoesNotExist("Article not found", log_level=logging.INFO)

    def retrieve_module_config(self, module_config_id: str) -> Optional[ModuleConfig]:
        deployment = Deployment.objects.filter(moduleConfigs__contains=[{"id": module_config_id}]).first()
        if not deployment:
            return None

        for config in deployment.moduleConfigs:
            if config["id"] == module_config_id:
                return ModuleConfig.from_dict(config, use_validator_field=False)
        return None

    def retrieve_key_actions(
        self, deployment_id: str, trigger: KeyActionConfig.Trigger = None
    ) -> list[KeyActionConfig]:
        query = Q(mongoId=deployment_id)
        if trigger:
            query &= Q(keyActions__contains=[{KeyActionConfig.TRIGGER: trigger.value}])
        deployment = Deployment.objects.filter(query).first()
        if not deployment:
            raise DeploymentDoesNotExist
        return [KeyActionConfig.from_dict(k, use_validator_field=False) for k in deployment.keyActions]

    """ END Retrieve documents section """

    """ Update document section """

    def update_deployment(self, deployment: DeploymentDTO):
        deployment.validate_keys_should_not_be_present_on_update()
        deployment_dict = deployment.to_dict(ignored_fields=self.IGNORED_FIELDS)
        deployment_dict = remove_none_values(deployment_dict)
        deployment_id = deployment_dict.pop("id")
        entity = Deployment.objects.filter(mongoId=deployment_id).first()
        if not entity:
            raise DeploymentDoesNotExist
        for key, value in deployment_dict.items():
            if isinstance(value, dict):
                value = convert_complex_types_in_jsonb_to_str(value, 0, 0)

            setattr(entity, key, value)
        entity.save()

        for key, value in deployment_dict.items():
            if key == DeploymentDTO.MODULE_CONFIGS:
                for mc in value or []:
                    self._publish_module_config_event(deployment_id, old=None, new=mc)

        return deployment_id

    def update_deployment_fields(self, deployment_id: str, fields: dict):
        """
        @deployment_id - str, id of deployment to update.
        @fields - dict of fields to update, supports nested fields.
        """
        deployment = Deployment.objects.filter(mongoId=deployment_id).first()
        if not deployment:
            raise DeploymentDoesNotExist

        for key, value in fields.items():
            keys = key.split(".")
            root = deployment

            while len(keys) > 1:
                if isinstance(root, dict):
                    k = keys.pop(0)
                    root[k] = root.setdefault(k, {})
                    root = root[k]
                else:
                    k = keys.pop(0)
                    setattr(root, k, getattr(root, k) or {})
                    root = getattr(root, k)

            if isinstance(value, dict):
                value = convert_complex_types_in_jsonb_to_str(value, 0, 0)

            if isinstance(root, dict):
                root[keys[0]] = value
            else:
                setattr(root, keys[0], value)

        deployment.save()
        return deployment_id

    def update_deployment_icon(self, deployment_id: str, icon: S3Object):
        if not icon:
            return
        new_val = icon if isinstance(icon, str) else icon.to_dict()
        return Deployment.objects.filter(mongoId=deployment_id).update(icon=new_val)

    @atomic(savepoint=False)
    def update_full_deployment(self, deployment: DeploymentDTO) -> str:
        consent_repo = inject.instance(ConsentRepository)
        econsent_repo = inject.instance(EConsentRepository)

        if module_configs := deployment.moduleConfigs:
            if module_configs is not None:
                for module_config in module_configs:
                    self.update_module_config(
                        deployment_id=deployment.id,
                        config=module_config,
                        update_revision=False,
                    )
                deployment.moduleConfigs = None

        if onboarding_configs := deployment.onboardingConfigs:
            if onboarding_configs is not None:
                for onboarding_config in onboarding_configs:
                    self.update_onboarding_module_config(
                        deployment_id=deployment.id,
                        onboarding_module_config=onboarding_config,
                        update_revision=False,
                    )
                deployment.onboardingConfigs = None

        if roles := deployment.roles:
            self.create_or_update_roles(deployment_id=deployment.id, roles=roles)
            deployment.roles = None

        if deployment.learn and deployment.learn.sections:
            for learn_section in deployment.learn.sections:
                if learn_section.articles is not None:
                    for learn_article in learn_section.articles:
                        self.update_learn_article(
                            deployment_id=deployment.id,
                            section_id=learn_section.id,
                            article=learn_article,
                        )

                learn_section.articles = None
                self.update_learn_section(
                    deployment_id=deployment.id,
                    learn_section=learn_section,
                )
            deployment.learn = None

        if key_actions := deployment.keyActions:
            for key_action in key_actions:
                self.update_key_action(
                    deployment_id=deployment.id,
                    key_action=key_action,
                    key_action_id=key_action.id,
                )
            deployment.keyActions = None

        if consent := deployment.consent:
            consent_repo.create_consent(deployment_id=deployment.id, consent=consent)
            deployment.consent = None

        if econsent := deployment.econsent:
            econsent_repo.create_econsent(deployment_id=deployment.id, econsent=econsent)
            deployment.econsent = None

        deployment.version = None
        deployment_dict = deployment.to_dict(ignored_fields=self.IGNORED_FIELDS)
        deployment_dict = remove_none_values(deployment_dict)
        deployment_id = deployment_dict.pop("id")

        deployment_entity = Deployment.objects.filter(mongoId=deployment_id).first()
        if not deployment_entity:
            raise DeploymentDoesNotExist

        for key, value in deployment_dict.items():
            setattr(deployment_entity, key, value)

        deployment_entity.updateDateTime = datetime.utcnow()
        deployment_entity.save()
        return deployment.id

    def update_enrollment_counter(self, deployment_id: str, **kwargs) -> DeploymentDTO:
        count = Deployment.objects.filter(mongoId=deployment_id).update(
            enrollmentCounter=Case(
                When(enrollmentCounter__isnull=True, then=Value(1)),
                default=F("enrollmentCounter") + Value(1),
            )
        )
        if count == 0:
            raise DeploymentDoesNotExist
        deployment = Deployment.objects.filter(mongoId=deployment_id).first()
        return self.deployment_from_document(model_to_dict(deployment))

    @atomic()
    def update_learn_section(self, deployment_id: str, learn_section: LearnSection) -> str:
        learn_section.updateDateTime = datetime.utcnow()
        learn_section_dict: dict = learn_section.to_dict(ignored_fields=self.IGNORED_FIELDS)
        learn_section_dict = remove_none_values(learn_section_dict)
        learn_section_dict.pop("id")

        deployment = Deployment.objects.filter(mongoId=deployment_id).first()
        if not deployment:
            raise DeploymentDoesNotExist

        sections = deployment.learn.get("sections", [])
        learn_section_found = False
        for section in sections:
            if section["id"] == str(learn_section.id):
                section.update(learn_section_dict)
                learn_section_found = True
                break

        if not learn_section_found:
            raise LearnSectionDoesNotExist

        deployment.learn = convert_complex_types_in_jsonb_to_str(deployment.learn, 0, 0)
        deployment.updateDateTime = datetime.utcnow()
        deployment.save()
        return str(learn_section.id)

    @atomic()
    def update_learn_article(
        self,
        deployment_id: str,
        section_id: str,
        article: LearnArticle,
    ) -> str:
        article_dict = self.prepare_article_for_update(article, deployment_id)
        deployment = Deployment.objects.filter(mongoId=deployment_id).first()
        if not deployment:
            raise DeploymentDoesNotExist

        sections = deployment.learn.get("sections", [])
        learn_section_found = False
        for section in sections:
            if section["id"] == section_id:
                articles = section.get("articles", [])
                for art in articles:
                    if art["id"] == article.id:
                        art.update(article_dict)
                        learn_section_found = True
                        break
                break

        if not learn_section_found:
            raise LearnArticleDoesNotExist

        deployment.learn = convert_complex_types_in_jsonb_to_str(deployment.learn, 0, 0)
        deployment.updateDateTime = datetime.utcnow()
        deployment.save()
        return str(article.id)

    def prepare_article_for_update(self, article: LearnArticle, deployment_id: str):
        old_article = self.retrieve_article_by_id(article.id, deployment_id)
        article.updateDateTime = datetime.utcnow()
        article.createDateTime = old_article.createDateTime

        article_dict = article.to_dict(ignored_fields=self.IGNORED_FIELDS)
        article_dict[LearnArticle.ID] = str(article.id)
        return remove_none_values(article_dict)

    def update_key_action(
        self, deployment_id: str, key_action_id: str, key_action: KeyActionConfig
    ) -> tuple[str, bool]:
        key_action.id = key_action_id
        key_action.updateDateTime = datetime.utcnow()
        deployment = Deployment.objects.filter(mongoId=deployment_id).first()
        if not deployment:
            raise DeploymentDoesNotExist

        key_action_to_update = list(filter(lambda x: x["id"] == key_action_id, deployment.keyActions))
        if len(key_action_to_update) == 0:
            raise ObjectDoesNotExist("Deployment not found", log_level=logging.INFO)
        key_action_to_update = key_action_to_update[0]

        update_required = not self._are_equal(
            remove_none_values(key_action_to_update),
            key_action.to_dict(include_none=False),
        )

        if update_required:
            key_action.createDateTime = key_action_to_update.pop("createDateTime", datetime.utcnow())
            key_action_to_update.update(
                key_action.to_dict(
                    include_none=False,
                    ignored_fields=self.IGNORED_FIELDS,
                )
            )
            if key_action.notifyEvery is None:
                key_action_to_update.pop(KeyActionConfig.NOTIFY_EVERY, None)

            deployment.keyActions = convert_complex_types_in_jsonb_to_str(
                [
                    key_action if key_action["id"] != key_action_id else key_action_to_update
                    for key_action in deployment.keyActions
                ],
                0,
                0,
            )
            deployment.updateDateTime = datetime.utcnow()
            deployment.save()

        return str(key_action_id), update_required

    """ END Update document section """

    """ Delete document section """

    def delete_deployment(self, deployment_id: str) -> str:
        count, _ = Deployment.objects.filter(mongoId=deployment_id).delete()
        if count == 0:
            raise DeploymentDoesNotExist

        return deployment_id

    def delete_module_config(self, deployment_id: str, module_config_id: str) -> None:
        mc_id = str(ObjectId(module_config_id))  # to prevent sql injection
        result = Deployment.objects.filter(mongoId=deployment_id).update(
            moduleConfigs=Func(
                F("moduleConfigs"),
                Value(f'$ ? (@.id != "{mc_id}")', output_field=None),
                function="jsonb_path_query_array",
                output_field=Cast(F("moduleConfigs"), output_field=None),
            ),
            version=F("version") + Value(1),
            updateDateTime=datetime.utcnow(),
        )
        if result == 0:
            raise DeploymentDoesNotExist

    def delete_component_config(self, deployment_id: str, component_name: str) -> None:
        deployment = Deployment.objects.filter(mongoId=deployment_id).first()
        if not deployment:
            raise DeploymentDoesNotExist

        component_configs = deployment.componentConfigs or {}
        if not component_configs.get(component_name):
            raise ComponentConfigDoesNotExist

        component_configs.pop(component_name)
        deployment.componentConfigs = component_configs
        deployment.updateDateTime = datetime.utcnow()
        deployment.version += 1
        deployment.save()

    def delete_onboarding_module_config(self, deployment_id: str, onboarding_config_id: str) -> None:
        onboarding_config_id = str(ObjectId(onboarding_config_id))  # to prevent sql injection
        count = Deployment.objects.filter(mongoId=deployment_id).update(
            onboardingConfigs=Func(
                F("onboardingConfigs"),
                Value(f'$ ? (@.id != "{onboarding_config_id}")', output_field=None),
                function="jsonb_path_query_array",
                output_field=Cast(F("onboardingConfigs"), output_field=None),
            ),
            version=F("version") + Value(1),
            updateDateTime=datetime.utcnow(),
        )
        if count == 0:
            raise DeploymentDoesNotExist

    @atomic()
    def delete_learn_section(self, deployment_id: str, section_id: str):
        deployment = Deployment.objects.filter(mongoId=deployment_id).first()
        if not deployment:
            raise DeploymentDoesNotExist

        sections = deployment.learn.get("sections", [])
        section_found = False
        for section in sections:
            if section["id"] == section_id:
                sections.remove(section)
                section_found = True
                break

        if not section_found:
            raise LearnSectionDoesNotExist

        deployment.updateDateTime = datetime.utcnow()
        deployment.save()

    @atomic()
    def delete_learn_article(self, deployment_id: str, section_id: str, article_id: str):
        deployment = Deployment.objects.filter(mongoId=deployment_id).first()
        if not deployment:
            raise DeploymentDoesNotExist

        sections = deployment.learn.get("sections", [])
        section_found = False
        for section in sections:
            if section["id"] == section_id:
                articles = section.get("articles", [])
                article_found = False
                for article in articles:
                    if article["id"] == article_id:
                        articles.remove(article)
                        article_found = True
                        break
                if article_found:
                    section_found = True
                    break

        if not section_found:
            raise ObjectDoesNotExist("Learn article not found", log_level=logging.INFO)

        deployment.updateDateTime = datetime.utcnow()
        deployment.save()

    def delete_key_action(
        self,
        deployment_id: str,
        key_action_id: str = None,
        module_config_id: str = None,
    ) -> None:
        key_action_id = str(ObjectId(key_action_id)) if key_action_id else None  # to prevent sql injection
        module_config_id = str(ObjectId(module_config_id)) if module_config_id else None  # to prevent sql injection

        deployment = Deployment.objects.filter(mongoId=deployment_id).first()
        if not deployment:
            raise DeploymentDoesNotExist

        key_action_exists = False
        for key_action in deployment.keyActions:
            if key_action_id and key_action["id"] == key_action_id:
                key_action_exists = True
                break
            if module_config_id and key_action.get("moduleConfigId") == module_config_id:
                key_action_exists = True
                break

        if not key_action_exists:
            raise ObjectDoesNotExist("Key action not found", log_level=logging.INFO)

        if key_action_id:
            key_action_func = Func(
                F("keyActions"),
                Value(f'$ ? (@.id != "{key_action_id}")', output_field=None),
                function="jsonb_path_query_array",
                output_field=Cast(F("keyActions"), output_field=None),
            )
        else:
            key_action_func = Func(
                F("keyActions"),
                Value(
                    f'$ ? (!exists(@.moduleConfigId) || @.moduleConfigId != "{module_config_id}")', output_field=None
                ),
                function="jsonb_path_query_array",
                output_field=Cast(F("keyActions"), output_field=None),
            )

        result = Deployment.objects.filter(mongoId=deployment_id).update(
            keyActions=key_action_func,
            updateDateTime=datetime.utcnow(),
        )
        if result == 0:
            raise DeploymentDoesNotExist

    """ END Delete document section """

    @atomic()
    def delete_key_actions(self, deployment_id: str, exclude_types: tuple | None = None) -> list[str]:
        deployment = Deployment.objects.filter(mongoId=deployment_id).first()
        if not deployment:
            raise DeploymentDoesNotExist

        if not deployment.keyActions:
            return []

        ids = []
        for key_action in deployment.keyActions:
            if not exclude_types or key_action["type"] not in exclude_types:
                ids.append(str(key_action["id"]))
        if not ids:
            return []

        not_in_query = "&&".join([f'@.id <> "{SafeString(id_)}"' for id_ in ids])
        Deployment.objects.filter(mongoId=deployment_id).update(
            keyActions=Func(
                F("keyActions"),
                Value(f"$ ? ({not_in_query})", output_field=None),
                function="jsonb_path_query_array",
                output_field=Cast(F("keyActions"), output_field=None),
            ),
        )
        return ids

    @staticmethod
    def _are_equal(dict1, dict2) -> bool:
        dict1 = copy(dict1)
        dict2 = copy(dict2)
        for _dict in (dict1, dict2):
            _dict.pop("createDateTime", None)
            _dict.pop("updateDateTime", None)
            _dict.pop("version", None)

        return dict1 == dict2

    def create_or_update_roles(self, deployment_id: str, roles: list[RoleDTO]) -> list[str]:
        roles_dicts = []

        for role in roles:
            role.id = role.id or str(ObjectId())
            role_dict = role.to_dict(include_none=False)
            role_dict[RoleDTO.ID] = ObjectId(role.id)
            roles_dicts.append(role_dict)

        roles_dicts = convert_complex_types_in_jsonb_to_str(roles_dicts, 0, 0)
        count = Deployment.objects.filter(mongoId=deployment_id).update(roles=roles_dicts)
        if count == 0:
            raise DeploymentDoesNotExist

        return [role.id for role in roles]

    def update_localizations(self, deployment_id: str, localizations: dict) -> str:
        count = Deployment.objects.filter(mongoId=deployment_id).update(
            localizations=localizations,
            updateDateTime=datetime.utcnow(),
        )
        if count == 0:
            raise DeploymentDoesNotExist
        return str(deployment_id)

    def retrieve_localization(self, deployment_id: str, locale: str) -> (Optional[str], dict):
        localizations_data, deployment_locale = self.retrieve_all_localization(deployment_id=deployment_id)
        default_localization = localizations_data.get(deployment_locale, {})
        if localizations := localizations_data.get(locale, {}):
            if deployment_locale != locale:
                default_localization = {**default_localization, **localizations}
            return deployment_locale, default_localization
        if default_localization:
            return deployment_locale, default_localization
        return deployment_locale, localizations_data.get(Language.EN, {})

    def retrieve_all_localization(self, deployment_id: str) -> (dict, Optional[str]):
        deployment = Deployment.objects.filter(mongoId=deployment_id).values("localizations", "language").first()
        if not deployment:
            raise DeploymentDoesNotExist
        localizations_data = deployment.get("localizations") or {}
        deployment_locale = deployment.get("language") or Language.EN
        return localizations_data, deployment_locale

    def retrieve_module_ids(self, deployment_id: str, status=EnableStatus.ENABLED) -> list[str]:
        deployment = Deployment.objects.filter(mongoId=deployment_id).values(DeploymentDTO.MODULE_CONFIGS).first()
        if not deployment:
            raise DeploymentDoesNotExist

        module_configs = deployment.get(DeploymentDTO.MODULE_CONFIGS, [])
        return [
            config[ModuleConfig.MODULE_ID]
            for config in module_configs
            if not status or config[ModuleConfig.STATUS] == status
        ]

    def retrieve_language(self, deployment_id: str) -> str:
        deployment = Deployment.objects.filter(mongoId=deployment_id).values(DeploymentDTO.LANGUAGE).first()
        if not deployment:
            raise DeploymentDoesNotExist
        return deployment.get(DeploymentDTO.LANGUAGE, Language.EN)

    @atomic()
    def delete_localizations_by_keys(self, deployment_id: str, keys: list[str]):
        deployment = Deployment.objects.select_for_update().filter(mongoId=deployment_id).first()
        if not deployment:
            raise DeploymentDoesNotExist

        localizations = deployment.localizations
        if not localizations:
            return

        updated = {lang: {k: v for k, v in data.items() if k not in keys} for lang, data in localizations.items()}
        deployment.localizations = updated
        deployment.updateDateTime = datetime.now(UTC)

        deployment.save(update_fields=[DeploymentDTO.LOCALIZATIONS, DeploymentDTO.UPDATE_DATE_TIME])

    @staticmethod
    def deployment_from_document(doc: dict) -> DeploymentDTO:
        return DeploymentDTO.from_dict(doc, use_validator_field=False, ignore_none=True)

    @atomic()
    def reorder_learn_sections(self, deployment_id: str, ordering_data: list[OrderUpdateObject]):
        updated_time = datetime.utcnow()
        deployment = Deployment.objects.filter(mongoId=deployment_id).first()
        if not deployment:
            raise DeploymentDoesNotExist

        for learn_section in ordering_data:
            section_found = False
            for section in deployment.learn.get("sections", []):
                if section["id"] == learn_section.id:
                    section[LearnSection.ORDER] = learn_section.order
                    section[LearnSection.UPDATE_DATE_TIME] = updated_time
                    section_found = True
                    break

            if not section_found:
                raise LearnSectionDoesNotExist

        deployment.learn = convert_complex_types_in_jsonb_to_str(deployment.learn, 0, 0)
        deployment.updateDateTime = updated_time
        deployment.save()

    @atomic()
    def reorder_learn_articles(
        self,
        deployment_id: str,
        section_id: str,
        ordering_data: list[OrderUpdateObject],
    ):
        updated_time = datetime.utcnow()
        deployment = Deployment.objects.filter(mongoId=deployment_id).first()
        if not deployment:
            raise DeploymentDoesNotExist

        sections = deployment.learn.get("sections", [])
        article_found = False
        for section in sections:
            if section["id"] == section_id:
                for learn_article in ordering_data:
                    for article in section.get(LearnSection.ARTICLES, []):
                        if article["id"] == str(learn_article.id):
                            article[LearnArticle.ORDER] = learn_article.order
                            article[LearnArticle.UPDATE_DATE_TIME] = updated_time
                            article_found = True
                            break

                    if not article_found:
                        raise LearnArticleDoesNotExist

                break

        if not article_found:
            raise ObjectDoesNotExist("Learn article not found", log_level=logging.INFO)

        deployment.learn = convert_complex_types_in_jsonb_to_str(deployment.learn, 0, 0)
        deployment.updateDateTime = updated_time
        deployment.save()

    @atomic()
    def reorder_module_configs(self, deployment_id: str, ordering_data: list[OrderUpdateObject]):
        updated_time = datetime.utcnow()
        deployment = Deployment.objects.filter(mongoId=deployment_id).first()
        if not deployment:
            raise DeploymentDoesNotExist

        for module_config in ordering_data:
            config_found = False
            for config in deployment.moduleConfigs:
                if config["id"] == module_config.id:
                    config[ModuleConfig.ORDER] = module_config.order
                    config[ModuleConfig.UPDATE_DATE_TIME] = updated_time
                    config[ModuleConfig.VERSION] = config.get(ModuleConfig.VERSION, 1) + 1
                    config_found = True
                    break

            if not config_found:
                raise ObjectDoesNotExist("Module config not found", log_level=logging.INFO)

        deployment.moduleConfigs = convert_complex_types_in_jsonb_to_str(deployment.moduleConfigs, 0, 0)
        deployment.updateDateTime = updated_time
        deployment.version += 1
        deployment.save()

    def reorder_onboarding_module_configs(self, deployment_id: str, ordering_data: list[OrderUpdateObject]):
        updated_time = datetime.utcnow()
        deployment = Deployment.objects.filter(mongoId=deployment_id).first()
        if not deployment:
            raise DeploymentDoesNotExist

        onboarding_configs = deployment.onboardingConfigs or []
        for onboarding_config in ordering_data:
            config_found = False
            for config in onboarding_configs:
                if config["id"] == onboarding_config.id:
                    config[OnboardingModuleConfigDTO.ORDER] = onboarding_config.order
                    config[OnboardingModuleConfigDTO.VERSION] = config.get(OnboardingModuleConfigDTO.VERSION, 1) + 1
                    config_found = True
                    break

            if not config_found:
                raise ObjectDoesNotExist("Onboarding module config not found", log_level=logging.INFO)

        deployment.updateDateTime = updated_time
        deployment.version += 1
        deployment.save()

    def retrieve_deployment_revision_by_module_config_version(
        self,
        deployment_id: str,
        module_id: str,
        module_config_version: int,
        module_config_id: str = None,
        config_body_id: str = None,
    ) -> Optional[DeploymentRevisionDTO]:
        json_query = []
        if module_id:
            safe_module_id = SafeString(module_id)
            json_query.append(f'@.moduleId == "{safe_module_id}"')

        if module_config_id:
            safe_module_config_id = SafeString(module_config_id)
            json_query.append(f'@.id == "{safe_module_config_id}"')

        if not module_config_version:
            json_query.append("@.version == null")
        else:
            safe_module_config_version = SafeString(module_config_version)
            json_query.append(f'@.version == "{safe_module_config_version}"')

        if config_body_id:
            safe_config_body_id = SafeString(config_body_id)
            json_query.append(f'@.configBody.id == "{safe_config_body_id}"')

        params = remove_none_values({"deployment_id": deployment_id})
        query = f"""
            SELECT id FROM deployment_revision
            WHERE "deploymentId" = %(deployment_id)s
                AND jsonb_path_exists(
                    "snap",
                    '$.moduleConfigs[*] ? ({" && ".join(json_query)})'
                )
        """

        with connection.cursor() as cursor:
            cursor.execute(query, params)
            revision_id = cursor.fetchall()
            if not revision_id:
                return None

            revision = DeploymentRevision.objects.filter(mongoId=revision_id[0][0]).first()
            return DeploymentRevisionDTO.from_dict(model_to_dict(revision), use_validator_field=False)

    def create_deployment_template(self, template: DeploymentTemplateDTO) -> str:
        template.createDateTime = template.updateDateTime = datetime.utcnow()
        new_event = DeploymentTemplate(**template.to_dict(include_none=False))
        new_event.save()
        return str(new_event.mongoId)

    def retrieve_deployment_templates(self, organization_id: str = None) -> list[DeploymentTemplateDTO]:
        query = Q()
        if organization_id:
            query &= Q(organizationIds__contains=[organization_id])
        results = DeploymentTemplate.objects.filter(query)
        return [DeploymentTemplateDTO.from_dict(model_to_dict(result)) for result in results]

    def retrieve_deployment_template(self, template_id: str) -> DeploymentTemplateDTO:
        template = DeploymentTemplate.objects.filter(mongoId=template_id).first()
        if not template:
            raise ObjectDoesNotExist("Template not found", log_level=logging.INFO)

        return DeploymentTemplateDTO.from_dict(model_to_dict(template))

    def delete_deployment_template(self, template_id: str):
        count, _ = DeploymentTemplate.objects.filter(mongoId=template_id).delete()
        if count == 0:
            raise ObjectDoesNotExist("Template not found", log_level=logging.INFO)

    def update_deployment_template(self, template_id: str, template: DeploymentTemplateDTO) -> str:
        old_template = DeploymentTemplate.objects.filter(mongoId=template_id).first()
        if not old_template:
            raise ObjectDoesNotExist("Template not found", log_level=logging.INFO)

        data = template.to_dict(include_none=False)
        for key, value in data.items():
            setattr(old_template, key, value)

        old_template.save()
        return template_id

    def retrieve_files_in_library(
        self,
        library_id: str,
        deployment_id: str = None,
        offset: int = 0,
        limit: int = 100,
    ):
        match_query = remove_none_values(
            {
                f"{FileStorageDTO.METADATA}__libraryId": library_id,
                f"{FileStorageDTO.METADATA}__deploymentId": deployment_id,
            }
        )
        results = FileStorage.objects.filter(**match_query)[offset : offset + limit]
        count = FileStorage.objects.filter(**match_query).count()

        files = []
        for result in results:
            files.append(FileStorageDTO.from_dict(model_to_dict(result)))
        return files, count

    @atomic()
    def create_deployment_labels(self, deployment_id: str, labels: list[Label]):
        labels_dicts = []
        for label in labels:
            label.createDateTime = datetime.now()
            label_dict = label.to_dict(include_none=False)
            label_dict[Label.ID] = ObjectId()
            label_dict[Label.AUTHOR_ID] = ObjectId(label.authorId)
            labels_dicts.append(label_dict)

        labels_dicts = convert_complex_types_in_jsonb_to_str(labels_dicts, 0, 0)
        count = Deployment.objects.filter(mongoId=deployment_id).update(
            labels=RawSQL(
                '"labels" || %s',
                (json.dumps(labels_dicts),),
                output_field=models.JSONField(),
            ),
        )
        if count == 0:
            raise DeploymentDoesNotExist

        return [str(label[Label.ID]) for label in labels_dicts]

    def retrieve_deployment_labels(self, deployment_id: str) -> list[Label]:
        deployment = self.retrieve_deployment(deployment_id=deployment_id)
        if not deployment.features.labels:
            raise ObjectDoesNotExist(message="Label feature is not enabled")
        return deployment.labels or []

    def update_deployment_labels(
        self,
        deployment_id: str,
        labels: list[Label],
        updated_label: Optional[Label] = None,
    ):
        if updated_label:
            label_index = self.find_label_index_in_labels(updated_label.id, labels)
            labels[label_index].text = updated_label.text
            labels[label_index].updatedBy = updated_label.updatedBy
            labels[label_index].updateDateTime = datetime.now()

        updated_labels_dicts = list()

        for label in labels:
            label_dict = label.to_dict(include_none=False)
            label_dict[Label.ID] = ObjectId(label.id)
            label_dict[Label.AUTHOR_ID] = ObjectId(label.authorId)
            if label.updatedBy:
                label_dict[Label.UPDATED_BY] = ObjectId(label.updatedBy)
            updated_labels_dicts.append(label_dict)

        updated_labels_dicts = convert_complex_types_in_jsonb_to_str(updated_labels_dicts, 0, 0)
        count = Deployment.objects.filter(mongoId=deployment_id).update(labels=updated_labels_dicts)
        if count == 0:
            raise DeploymentDoesNotExist
        return str(deployment_id)

    def delete_deployment_label(self, deployment_id: str, label_id: str):
        label_id = str(ObjectId(label_id))  # to prevent sql injection

        deployment = Deployment.objects.filter(mongoId=deployment_id).first()
        if not deployment:
            raise DeploymentDoesNotExist
        label_exists = False
        for label in deployment.labels:
            if label["id"] == label_id:
                label_exists = True
                break
        if not label_exists:
            raise ObjectDoesNotExist("Label does not exist in deployment or static label")

        result = Deployment.objects.filter(mongoId=deployment_id).update(
            labels=Func(
                F("labels"),
                Value(f'$ ? (@.id != "{label_id}")', output_field=None),
                function="jsonb_path_query_array",
                output_field=Cast(F("labels"), output_field=None),
            ),
        )
        if result == 0:
            raise DeploymentDoesNotExist

    def find_label_index_in_labels(self, label_id, labels: list[Label]) -> int:
        if not labels:
            raise ObjectDoesNotExist(message=f"Label with {label_id} doesn't exist")
        label_index = self._find_label_index_in_labels(str(label_id), labels)
        if label_index is None:
            raise ObjectDoesNotExist(message=f"Label with {label_id} doesn't exist")
        return label_index

    @staticmethod
    def _find_label_index_in_labels(label_id, labels: list[Label]) -> int | None:
        n = 0
        for label in labels:
            if label.id == label_id:
                return n
            n += 1

    def _calculate_revision_diff(self, current: dict, previous: dict) -> dict:
        diff = deepdiff.DeepDiff(
            previous,
            current,
            ignore_order=True,
            exclude_paths=["version", *self.IGNORED_FIELDS],
            verbose_level=2,
            ignore_private_variables=False,
        )
        return diff.to_dict()

    @staticmethod
    def _publish_module_config_event(deployment_id: str, old: Optional[dict], new: dict):
        if new.get("status", None) == EnableStatus.DISABLED.value and (
            not old or old["status"] != EnableStatus.DISABLED.value
        ):
            module_config_id = new["id"]
            event = ModuleConfigDeleteDisableEvent(deployment_id, module_config_id)
            event_bus = inject.instance(EventBusAdapter)
            event_bus.emit(event, raise_error=True)

    def retrieve_sso_config_id(self, deployment_id: str) -> Optional[str]:
        deployment = Deployment.objects.filter(mongoId=deployment_id).first()
        return deployment.sso if deployment else None

    def get_active_deployment_ids_in_organizations(self, organization_ids: list[str]) -> list[str]:
        return Deployment.objects.filter(
            status=Status.DEPLOYED.value,
            mongoId__in=Subquery(
                Organization.objects.filter(mongoId__in=organization_ids)
                .annotate(deployment_id=Func(F("deploymentIds"), function="unnest"))
                .values("deployment_id")
            ),
        ).values_list("mongoId", flat=True)

    def retrieve_deployment_ids_with_key_actions(self) -> list[str]:
        query = Q(keyActions__isnull=False) & ~Q(keyActions=[])
        return list(Deployment.objects.filter(query).values_list(Deployment.MONGO_ID, flat=True))
