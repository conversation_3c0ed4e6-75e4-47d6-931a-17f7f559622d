from abc import ABC, abstractmethod
from typing import Optional

from bson import ObjectId

from sdk.authorization.dtos.role.role import RoleDTO
from sdk.common.common_models.sort import SortField
from sdk.deployment.dtos.deployment import (
    DeploymentDTO,
    DeploymentRevisionDTO,
    DeploymentTemplateDTO,
    Label,
    ModuleConfig,
    OnboardingModuleConfigDTO,
)
from sdk.deployment.dtos.deployment_info import DeploymentInfo
from sdk.deployment.dtos.learn import LearnArticle, LearnSection, OrderUpdateObject
from sdk.deployment.dtos.status import EnableStatus, Status
from sdk.key_action.models.config import KeyActionConfig
from sdk.module_result.dtos.module_config import FilledBy
from sdk.phoenix.config.dynamic_component_config import BasePhoenixDynamicConfig
from sdk.storage.dtos import S3Object


class DeploymentRepository(ABC):
    IGNORED_FIELDS = ()

    @abstractmethod
    def create_deployment(self, deployment: DeploymentDTO, deployment_id: ObjectId = None) -> str:
        raise NotImplementedError

    @abstractmethod
    def create_draft(self, original: DeploymentDTO) -> str:
        raise NotImplementedError

    @abstractmethod
    def publish_draft(self, draft: DeploymentDTO, name: str, submitter_id: str) -> str:
        raise NotImplementedError

    @abstractmethod
    def rollback_draft(self, deployment_id: str, version: str) -> str:
        raise NotImplementedError

    @abstractmethod
    def create_module_config(self, deployment_id: str, config: ModuleConfig, update_revision: bool = True) -> str:
        raise NotImplementedError

    @abstractmethod
    def update_module_config(
        self,
        deployment_id: str,
        config: ModuleConfig,
        update_revision: bool = True,
    ) -> str:
        raise NotImplementedError

    @abstractmethod
    def create_component_config(
        self,
        deployment_id: str,
        component_name: str,
        config: BasePhoenixDynamicConfig,
        update_revision: bool = True,
    ):
        raise NotImplementedError

    @abstractmethod
    def create_or_update_component_config(
        self,
        deployment_id: str,
        component_name: str,
        config: BasePhoenixDynamicConfig,
        removed_fields: list[str],
        update_revision: bool = True,
    ):
        raise NotImplementedError

    @abstractmethod
    def update_onboarding_module_config(
        self,
        deployment_id: str,
        onboarding_module_config: OnboardingModuleConfigDTO,
        update_revision: bool = True,
    ) -> str:
        raise NotImplementedError

    @abstractmethod
    def create_deployment_revision(self, deployment_revision: DeploymentRevisionDTO):
        raise NotImplementedError

    @abstractmethod
    def create_onboarding_module_config(
        self, deployment_id: str, config: OnboardingModuleConfigDTO, update_revision: bool = True
    ) -> str:
        raise NotImplementedError

    @abstractmethod
    def create_key_action(self, deployment_id: str, key_action: KeyActionConfig) -> str:
        raise NotImplementedError

    @abstractmethod
    def create_learn_section(self, deployment_id: str, learn_section: LearnSection) -> str:
        raise NotImplementedError

    @abstractmethod
    def create_learn_article(self, deployment_id: str, section_id: str, learn_article: LearnArticle) -> str:
        raise NotImplementedError

    @abstractmethod
    def retrieve_deployment(self, deployment_id: str, exclude_fields: list[str] = None) -> DeploymentDTO:
        raise NotImplementedError

    @abstractmethod
    def retrieve_draft(self, deployment_id: str) -> DeploymentDTO:
        raise NotImplementedError

    @abstractmethod
    def update_enrollment_counter(self, deployment_id: str, **kwargs) -> DeploymentDTO:
        raise NotImplementedError

    @abstractmethod
    def retrieve_deployment_by_version_number(self, deployment_id: str, version_number: int) -> DeploymentDTO:
        raise NotImplementedError

    @abstractmethod
    def retrieve_deployment_ids_by_tags(self, tags: list[str]) -> list[str]:
        raise NotImplementedError

    @abstractmethod
    def retrieve_revision(self, deployment_id: str, version: int = None) -> DeploymentRevisionDTO:
        raise NotImplementedError

    @abstractmethod
    def retrieve_deployment_revisions(self, deployment_id: str) -> list[DeploymentRevisionDTO]:
        raise NotImplementedError

    @abstractmethod
    def retrieve_deployments(
        self,
        skip: int = None,
        limit: int = None,
        search_criteria: str = None,
        sort_fields: list[SortField] = None,
        status: list[Status] = None,
        ids: list[str] = None,
        fields: list[str] = None,
    ) -> tuple[list[DeploymentDTO], int]:
        raise NotImplementedError

    @abstractmethod
    def retrieve_deployments_by_ids(self, deployment_ids: list[str]) -> list[DeploymentDTO]:
        raise NotImplementedError

    @abstractmethod
    def retrieve_deployments_info(
        self, deployment_ids: list[str] = None, draft_ids: list[str] = None
    ) -> list[DeploymentInfo]:
        raise NotImplementedError

    @abstractmethod
    def retrieve_module_configs(self, deployment_id: str) -> list[ModuleConfig]:
        raise NotImplementedError

    @abstractmethod
    def retrieve_module_configs_filled_by(
        self, deployment_ids: list[str], filled_by: list[FilledBy]
    ) -> list[ModuleConfig]:
        raise NotImplementedError

    @abstractmethod
    def retrieve_module_config(self, module_config_id: str) -> Optional[ModuleConfig]:
        raise NotImplementedError

    @abstractmethod
    def retrieve_key_actions(
        self, deployment_id: str, trigger: KeyActionConfig.Trigger = None
    ) -> list[KeyActionConfig]:
        raise NotImplementedError

    @abstractmethod
    def update_deployment(self, deployment: DeploymentDTO) -> str:
        raise NotImplementedError

    @abstractmethod
    def update_deployment_fields(self, deployment_id: str, fields: dict) -> str:
        raise NotImplementedError

    @abstractmethod
    def update_deployment_icon(self, deployment_id: str, icon: S3Object) -> str:
        raise NotImplementedError

    @abstractmethod
    def retrieve_deployment_labels(self, deployment_id: str) -> list[Label]:
        raise NotImplementedError

    @abstractmethod
    def create_deployment_labels(self, deployment_id, labels: list[Label]):
        raise NotImplementedError

    @abstractmethod
    def update_deployment_labels(self, deployment_id, labels: list[Label], updated_label: Optional[Label]):
        raise NotImplementedError

    @abstractmethod
    def delete_deployment_label(self, deployment_id, label_id):
        raise NotImplementedError

    @abstractmethod
    def update_learn_article(
        self,
        deployment_id: str,
        section_id: str,
        article: LearnArticle,
    ) -> str:
        raise NotImplementedError

    @abstractmethod
    def update_learn_section(
        self,
        deployment_id: str,
        learn_section: LearnSection,
    ) -> str:
        raise NotImplementedError

    @abstractmethod
    def reorder_learn_sections(self, deployment_id: str, ordering_data: list[OrderUpdateObject]):
        raise NotImplementedError

    @abstractmethod
    def reorder_learn_articles(
        self,
        deployment_id: str,
        section_id: str,
        ordering_data: list[OrderUpdateObject],
    ):
        raise NotImplementedError

    @abstractmethod
    def reorder_module_configs(self, deployment_id: str, ordering_data: list[OrderUpdateObject]):
        raise NotImplementedError

    @abstractmethod
    def reorder_onboarding_module_configs(self, deployment_id: str, ordering_data: list[OrderUpdateObject]):
        raise NotImplementedError

    @abstractmethod
    def update_key_action(
        self,
        deployment_id: str,
        key_action_id: str,
        key_action: KeyActionConfig,
    ) -> tuple[str, bool]:
        raise NotImplementedError

    @abstractmethod
    def delete_deployment(self, deployment_id: str) -> None:
        raise NotImplementedError

    @abstractmethod
    def delete_module_config(self, deployment_id: str, module_config_id: str) -> None:
        raise NotImplementedError

    @abstractmethod
    def delete_component_config(self, deployment_id: str, component_name: str) -> None:
        raise NotImplementedError

    @abstractmethod
    def delete_onboarding_module_config(self, deployment_id: str, onboarding_config_id: str) -> None:
        raise NotImplementedError

    @abstractmethod
    def delete_learn_section(self, deployment_id: str, section_id: str) -> None:
        raise NotImplementedError

    @abstractmethod
    def delete_learn_article(self, deployment_id: str, section_id: str, article_id: str) -> None:
        raise NotImplementedError

    @abstractmethod
    def delete_key_action(
        self,
        deployment_id: str,
        key_action_id: str = None,
        module_config_id: str = None,
    ) -> None:
        raise NotImplementedError

    @abstractmethod
    def delete_key_actions(self, deployment_id: str, exclude_types: tuple | None = None) -> list[str]:
        raise NotImplementedError

    @abstractmethod
    def create_or_update_roles(
        self,
        deployment_id: str,
        roles: list[RoleDTO],
    ) -> list[str]:
        raise NotImplementedError

    @abstractmethod
    def update_localizations(self, deployment_id: str, localizations: dict) -> str:
        raise NotImplementedError

    @abstractmethod
    def retrieve_localization(self, deployment_id: str, locale: str) -> (Optional[str], dict):
        raise NotImplementedError

    @abstractmethod
    def retrieve_all_localization(self, deployment_id: str) -> (dict, Optional[str]):
        raise NotImplementedError

    @abstractmethod
    def retrieve_module_ids(self, deployment_id: str, status=EnableStatus.ENABLED) -> list[str]:
        raise NotImplementedError

    @abstractmethod
    def retrieve_language(self, deployment_id: str) -> str:
        raise NotImplementedError

    @abstractmethod
    def delete_localizations_by_keys(self, deployment_id: str, keys: list[str]):
        raise NotImplementedError

    @abstractmethod
    def retrieve_onboarding_module_configs(self, deployment_id: str) -> list[OnboardingModuleConfigDTO]:
        raise NotImplementedError

    def retrieve_deployment_revision_by_module_config_version(
        self,
        deployment_id: str,
        module_id: str,
        module_config_version: int,
        module_config_id: str = None,
        config_body_id: str = None,
    ) -> Optional[DeploymentRevisionDTO]:
        raise NotImplementedError

    @abstractmethod
    def check_export_short_code_module_config(self, short_code: str) -> bool:
        raise NotImplementedError

    def check_deployment_exists(self, deployment_id: str) -> bool:
        raise NotImplementedError

    @abstractmethod
    def update_full_deployment(self, deployment: DeploymentDTO) -> str:
        raise NotImplementedError

    @abstractmethod
    def create_deployment_template(self, template: DeploymentTemplateDTO) -> str:
        raise NotImplementedError

    @abstractmethod
    def retrieve_deployment_templates(self, organization_id: str = None) -> list[DeploymentTemplateDTO]:
        raise NotImplementedError

    @abstractmethod
    def retrieve_deployment_template(self, template_id: str) -> DeploymentTemplateDTO:
        raise NotImplementedError

    @abstractmethod
    def update_deployment_template(self, template_id: str, template: DeploymentTemplateDTO) -> str:
        raise NotImplementedError

    @abstractmethod
    def delete_deployment_template(self, template_id: str):
        raise NotImplementedError

    @abstractmethod
    def retrieve_files_in_library(
        self,
        library_id: str,
        deployment_id: str = None,
        offset: int = 0,
        limit: int = 100,
    ):
        raise NotImplementedError

    @abstractmethod
    def retrieve_article_by_id(self, article_id: str, deployment_id: str) -> LearnArticle:
        raise NotImplementedError

    @abstractmethod
    def retrieve_sso_config_id(self, deployment_id: str) -> str:
        raise NotImplementedError

    @abstractmethod
    def get_active_deployment_ids_in_organizations(self, organization_ids: list[str]) -> list[str]:
        raise NotImplementedError

    @abstractmethod
    def retrieve_deployment_ids_with_key_actions(self) -> list[str]:
        raise NotImplementedError
