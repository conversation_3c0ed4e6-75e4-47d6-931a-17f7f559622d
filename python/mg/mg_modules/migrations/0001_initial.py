# Generated by Django 5.1.5 on 2025-02-19 12:42

from django.db import migrations, models

from sdk.common.utils.file_utils import move_mongo_collection_to_postgres_table



def migrate_data(apps, schema_editor):
    move_mongo_collection_to_postgres_table(
        apps,
        app_name="mg_modules",
        migrations=[
            {
                "model_name": "ArmFatigue",
                "mongo_model": "armfatigue",
            },
            {
                "model_name": "Dysarthria",
                "mongo_model": "dysarthria",
            },
            {
                "model_name": "Dysphonia",
                "mongo_model": "dysphonia",
            },
            {
                "model_name": "Ptosis",
                "mongo_model": "ptosis",
            },
        ],
    )


class Migration(migrations.Migration):
    initial = True

    dependencies = []

    operations = [
        migrations.CreateModel(
            name="ArmFatigue",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("mongoId", models.Char<PERSON>ield(default=None, max_length=24, unique=True)),
                (
                    "userId",
                    models.CharField(db_index=True, default=None, max_length=24),
                ),
                (
                    "moduleId",
                    models.CharField(db_index=True, default=None, max_length=64),
                ),
                (
                    "moduleConfigId",
                    models.CharField(db_index=True, default=None, max_length=24),
                ),
                (
                    "moduleResultId",
                    models.CharField(
                        blank=True,
                        db_index=True,
                        default=None,
                        max_length=32,
                        null=True,
                    ),
                ),
                (
                    "deploymentId",
                    models.CharField(db_index=True, default=None, max_length=24),
                ),
                ("version", models.PositiveIntegerField(default=0)),
                ("deviceName", models.CharField(blank=True, max_length=256, null=True)),
                ("isAggregated", models.BooleanField(default=False)),
                ("startDateTime", models.DateTimeField(blank=True, null=True)),
                ("endDateTime", models.DateTimeField(blank=True, null=True)),
                ("createDateTime", models.DateTimeField(blank=True, null=True)),
                ("updateDateTime", models.DateTimeField(blank=True, null=True)),
                (
                    "submitterId",
                    models.CharField(db_index=True, default=None, max_length=24),
                ),
                (
                    "correlationStartDateTime",
                    models.DateTimeField(blank=True, null=True),
                ),
                ("client", models.JSONField(blank=True, null=True)),
                ("server", models.JSONField(blank=True, null=True)),
                ("ragThreshold", models.JSONField(blank=True, null=True)),
                ("flags", models.JSONField(blank=True, null=True)),
                (
                    "eventLogId",
                    models.CharField(blank=True, default=None, max_length=24, null=True),
                ),
                ("device", models.JSONField(blank=True, null=True)),
                ("source", models.TextField(blank=True, null=True)),
                ("isAverage", models.BooleanField(blank=True, null=True)),
                (
                    "noteId",
                    models.CharField(
                        blank=True,
                        db_index=True,
                        default=None,
                        max_length=24,
                        null=True,
                    ),
                ),
                ("feedback", models.JSONField(blank=True, null=True)),
                (
                    "meal",
                    models.CharField(
                        blank=True,
                        choices=[
                            ("FASTING", "FASTING"),
                            ("AFTER_BREAKFAST", "AFTER_BREAKFAST"),
                            ("AFTER_BREAKFAST_2_HOURS", "AFTER_BREAKFAST_2_HOURS"),
                            ("BEFORE_LUNCH", "BEFORE_LUNCH"),
                            ("AFTER_LUNCH", "AFTER_LUNCH"),
                            ("AFTER_LUNCH_2_HOURS", "AFTER_LUNCH_2_HOURS"),
                            ("BEFORE_DINNER", "BEFORE_DINNER"),
                            ("AFTER_DINNER", "AFTER_DINNER"),
                            ("AFTER_DINNER_2_HOURS", "AFTER_DINNER_2_HOURS"),
                            ("NONE", "NONE"),
                        ],
                        default=None,
                        max_length=32,
                        null=True,
                    ),
                ),
                (
                    "userNote",
                    models.CharField(blank=True, default=None, max_length=1000, null=True),
                ),
                (
                    "arm",
                    models.CharField(choices=[("LEFT", "LEFT"), ("RIGHT", "RIGHT")], max_length=16),
                ),
                ("angle", models.FloatField(blank=True, default=None, null=True)),
                (
                    "classification",
                    models.CharField(blank=True, default=None, max_length=255, null=True),
                ),
                ("output", models.JSONField(blank=True, default=None, null=True)),
                ("recording", models.JSONField()),
                ("time", models.FloatField(blank=True, default=None, null=True)),
            ],
            options={
                "db_table": "arm_fatigue",
            },
        ),
        migrations.CreateModel(
            name="Dysarthria",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("mongoId", models.CharField(default=None, max_length=24, unique=True)),
                (
                    "userId",
                    models.CharField(db_index=True, default=None, max_length=24),
                ),
                (
                    "moduleId",
                    models.CharField(db_index=True, default=None, max_length=64),
                ),
                (
                    "moduleConfigId",
                    models.CharField(db_index=True, default=None, max_length=24),
                ),
                (
                    "moduleResultId",
                    models.CharField(
                        blank=True,
                        db_index=True,
                        default=None,
                        max_length=32,
                        null=True,
                    ),
                ),
                (
                    "deploymentId",
                    models.CharField(db_index=True, default=None, max_length=24),
                ),
                ("version", models.PositiveIntegerField(default=0)),
                ("deviceName", models.CharField(blank=True, max_length=256, null=True)),
                ("isAggregated", models.BooleanField(default=False)),
                ("startDateTime", models.DateTimeField(blank=True, null=True)),
                ("endDateTime", models.DateTimeField(blank=True, null=True)),
                ("createDateTime", models.DateTimeField(blank=True, null=True)),
                ("updateDateTime", models.DateTimeField(blank=True, null=True)),
                (
                    "submitterId",
                    models.CharField(db_index=True, default=None, max_length=24),
                ),
                (
                    "correlationStartDateTime",
                    models.DateTimeField(blank=True, null=True),
                ),
                ("client", models.JSONField(blank=True, null=True)),
                ("server", models.JSONField(blank=True, null=True)),
                ("ragThreshold", models.JSONField(blank=True, null=True)),
                ("flags", models.JSONField(blank=True, null=True)),
                (
                    "eventLogId",
                    models.CharField(blank=True, default=None, max_length=24, null=True),
                ),
                ("device", models.JSONField(blank=True, null=True)),
                ("source", models.TextField(blank=True, null=True)),
                ("isAverage", models.BooleanField(blank=True, null=True)),
                (
                    "noteId",
                    models.CharField(
                        blank=True,
                        db_index=True,
                        default=None,
                        max_length=24,
                        null=True,
                    ),
                ),
                ("feedback", models.JSONField(blank=True, null=True)),
                (
                    "meal",
                    models.CharField(
                        blank=True,
                        choices=[
                            ("FASTING", "FASTING"),
                            ("AFTER_BREAKFAST", "AFTER_BREAKFAST"),
                            ("AFTER_BREAKFAST_2_HOURS", "AFTER_BREAKFAST_2_HOURS"),
                            ("BEFORE_LUNCH", "BEFORE_LUNCH"),
                            ("AFTER_LUNCH", "AFTER_LUNCH"),
                            ("AFTER_LUNCH_2_HOURS", "AFTER_LUNCH_2_HOURS"),
                            ("BEFORE_DINNER", "BEFORE_DINNER"),
                            ("AFTER_DINNER", "AFTER_DINNER"),
                            ("AFTER_DINNER_2_HOURS", "AFTER_DINNER_2_HOURS"),
                            ("NONE", "NONE"),
                        ],
                        default=None,
                        max_length=32,
                        null=True,
                    ),
                ),
                (
                    "userNote",
                    models.CharField(blank=True, default=None, max_length=1000, null=True),
                ),
                ("countingRecordingID", models.CharField(max_length=255)),
                (
                    "initialProbability",
                    models.FloatField(blank=True, default=None, null=True),
                ),
                (
                    "fatiguedProbability",
                    models.FloatField(blank=True, default=None, null=True),
                ),
                ("probability", models.FloatField(blank=True, default=None, null=True)),
                (
                    "confidence",
                    models.CharField(blank=True, default=None, max_length=255, null=True),
                ),
                ("buttercupInitialRecordingID", models.CharField(max_length=32)),
                ("buttercupFatiguedRecordingID", models.CharField(max_length=32)),
                ("severity", models.CharField(max_length=32, blank=True, null=True, default=None)),
            ],
            options={
                "db_table": "dysarthria",
            },
        ),
        migrations.CreateModel(
            name="Dysphonia",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("mongoId", models.CharField(default=None, max_length=24, unique=True)),
                (
                    "userId",
                    models.CharField(db_index=True, default=None, max_length=24),
                ),
                (
                    "moduleId",
                    models.CharField(db_index=True, default=None, max_length=64),
                ),
                (
                    "moduleConfigId",
                    models.CharField(db_index=True, default=None, max_length=24),
                ),
                (
                    "moduleResultId",
                    models.CharField(
                        blank=True,
                        db_index=True,
                        default=None,
                        max_length=32,
                        null=True,
                    ),
                ),
                (
                    "deploymentId",
                    models.CharField(db_index=True, default=None, max_length=24),
                ),
                ("version", models.PositiveIntegerField(default=0)),
                ("deviceName", models.CharField(blank=True, max_length=256, null=True)),
                ("isAggregated", models.BooleanField(default=False)),
                ("startDateTime", models.DateTimeField(blank=True, null=True)),
                ("endDateTime", models.DateTimeField(blank=True, null=True)),
                ("createDateTime", models.DateTimeField(blank=True, null=True)),
                ("updateDateTime", models.DateTimeField(blank=True, null=True)),
                (
                    "submitterId",
                    models.CharField(db_index=True, default=None, max_length=24),
                ),
                (
                    "correlationStartDateTime",
                    models.DateTimeField(blank=True, null=True),
                ),
                ("client", models.JSONField(blank=True, null=True)),
                ("server", models.JSONField(blank=True, null=True)),
                ("ragThreshold", models.JSONField(blank=True, null=True)),
                ("flags", models.JSONField(blank=True, null=True)),
                (
                    "eventLogId",
                    models.CharField(blank=True, default=None, max_length=24, null=True),
                ),
                ("device", models.JSONField(blank=True, null=True)),
                ("source", models.TextField(blank=True, null=True)),
                ("isAverage", models.BooleanField(blank=True, null=True)),
                (
                    "noteId",
                    models.CharField(
                        blank=True,
                        db_index=True,
                        default=None,
                        max_length=24,
                        null=True,
                    ),
                ),
                ("feedback", models.JSONField(blank=True, null=True)),
                (
                    "meal",
                    models.CharField(
                        blank=True,
                        choices=[
                            ("FASTING", "FASTING"),
                            ("AFTER_BREAKFAST", "AFTER_BREAKFAST"),
                            ("AFTER_BREAKFAST_2_HOURS", "AFTER_BREAKFAST_2_HOURS"),
                            ("BEFORE_LUNCH", "BEFORE_LUNCH"),
                            ("AFTER_LUNCH", "AFTER_LUNCH"),
                            ("AFTER_LUNCH_2_HOURS", "AFTER_LUNCH_2_HOURS"),
                            ("BEFORE_DINNER", "BEFORE_DINNER"),
                            ("AFTER_DINNER", "AFTER_DINNER"),
                            ("AFTER_DINNER_2_HOURS", "AFTER_DINNER_2_HOURS"),
                            ("NONE", "NONE"),
                        ],
                        default=None,
                        max_length=32,
                        null=True,
                    ),
                ),
                (
                    "userNote",
                    models.CharField(blank=True, default=None, max_length=1000, null=True),
                ),
                ("countingRecordingID", models.CharField(max_length=255)),
                (
                    "initialProbability",
                    models.FloatField(blank=True, default=None, null=True),
                ),
                (
                    "fatiguedProbability",
                    models.FloatField(blank=True, default=None, null=True),
                ),
                ("probability", models.FloatField(blank=True, default=None, null=True)),
                (
                    "confidence",
                    models.CharField(blank=True, default=None, max_length=255, null=True),
                ),
                ("aaahhhInitialRecordingID", models.CharField(max_length=32)),
                ("aaahhhFatiguedRecordingID", models.CharField(max_length=32)),
                ("severity", models.CharField(max_length=32, blank=True, null=True, default=None)),
            ],
            options={
                "db_table": "dysphonia",
            },
        ),
        migrations.CreateModel(
            name="Ptosis",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("mongoId", models.CharField(default=None, max_length=24, unique=True)),
                (
                    "userId",
                    models.CharField(db_index=True, default=None, max_length=24),
                ),
                (
                    "moduleId",
                    models.CharField(db_index=True, default=None, max_length=64),
                ),
                (
                    "moduleConfigId",
                    models.CharField(db_index=True, default=None, max_length=24),
                ),
                (
                    "moduleResultId",
                    models.CharField(
                        blank=True,
                        db_index=True,
                        default=None,
                        max_length=32,
                        null=True,
                    ),
                ),
                (
                    "deploymentId",
                    models.CharField(db_index=True, default=None, max_length=24),
                ),
                ("version", models.PositiveIntegerField(default=0)),
                ("deviceName", models.CharField(blank=True, max_length=256, null=True)),
                ("isAggregated", models.BooleanField(default=False)),
                ("startDateTime", models.DateTimeField(blank=True, null=True)),
                ("endDateTime", models.DateTimeField(blank=True, null=True)),
                ("createDateTime", models.DateTimeField(blank=True, null=True)),
                ("updateDateTime", models.DateTimeField(blank=True, null=True)),
                (
                    "submitterId",
                    models.CharField(db_index=True, default=None, max_length=24),
                ),
                (
                    "correlationStartDateTime",
                    models.DateTimeField(blank=True, null=True),
                ),
                ("client", models.JSONField(blank=True, null=True)),
                ("server", models.JSONField(blank=True, null=True)),
                ("ragThreshold", models.JSONField(blank=True, null=True)),
                ("flags", models.JSONField(blank=True, null=True)),
                (
                    "eventLogId",
                    models.CharField(blank=True, default=None, max_length=24, null=True),
                ),
                ("device", models.JSONField(blank=True, null=True)),
                ("source", models.TextField(blank=True, null=True)),
                ("isAverage", models.BooleanField(blank=True, null=True)),
                (
                    "noteId",
                    models.CharField(
                        blank=True,
                        db_index=True,
                        default=None,
                        max_length=24,
                        null=True,
                    ),
                ),
                ("feedback", models.JSONField(blank=True, null=True)),
                (
                    "meal",
                    models.CharField(
                        blank=True,
                        choices=[
                            ("FASTING", "FASTING"),
                            ("AFTER_BREAKFAST", "AFTER_BREAKFAST"),
                            ("AFTER_BREAKFAST_2_HOURS", "AFTER_BREAKFAST_2_HOURS"),
                            ("BEFORE_LUNCH", "BEFORE_LUNCH"),
                            ("AFTER_LUNCH", "AFTER_LUNCH"),
                            ("AFTER_LUNCH_2_HOURS", "AFTER_LUNCH_2_HOURS"),
                            ("BEFORE_DINNER", "BEFORE_DINNER"),
                            ("AFTER_DINNER", "AFTER_DINNER"),
                            ("AFTER_DINNER_2_HOURS", "AFTER_DINNER_2_HOURS"),
                            ("NONE", "NONE"),
                        ],
                        default=None,
                        max_length=32,
                        null=True,
                    ),
                ),
                (
                    "userNote",
                    models.CharField(blank=True, default=None, max_length=1000, null=True),
                ),
                ("recording", models.JSONField()),
                (
                    "severityLeft",
                    models.SmallIntegerField(blank=True, default=None, null=True),
                ),
                (
                    "severityRight",
                    models.SmallIntegerField(blank=True, default=None, null=True),
                ),
                (
                    "timeLeft",
                    models.SmallIntegerField(blank=True, default=None, null=True),
                ),
                (
                    "timeRight",
                    models.SmallIntegerField(blank=True, default=None, null=True),
                ),
                ("isPrompted", models.BooleanField(default=False)),
            ],
            options={
                "abstract": False,
                "indexes": [
                    models.Index(
                        fields=[
                            "moduleId",
                            "startDateTime",
                            "userId",
                            "moduleConfigId",
                        ],
                        name="mg_modules__moduleI_ad4135_idx",
                    ),
                    models.Index(
                        fields=["deploymentId", "userId", "moduleId", "startDateTime"],
                        name="mg_modules__deploym_22faac_idx",
                    ),
                    models.Index(
                        fields=["deploymentId", "userId", "moduleId", "createDateTime"],
                        name="mg_modules__deploym_af1364_idx",
                    ),
                ],
            },
        ),
        migrations.RunPython(migrate_data, migrations.RunPython.noop, atomic=True),
    ]
