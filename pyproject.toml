[project]
name = "huma-rpm-server"
version = "1.36.0"
description = "Add your description here"
readme = "README.md"
requires-python = "~=3.11"
dependencies = [
    "google-cloud-aiplatform==1.69.0",
    "framework",
    "huma-plugins",
    "sdk",
    "mg-plugin",
    "openai==0.27.8",
    "hu-plugin-craft-server",
]

[tool.uv]
dev-dependencies = [
    "coverage>=7.6.4",
    "freezegun>=1.5.1",
    "packaging>=24.1",
    "pre-commit>=4.0.1",
    "pytest-cov>=6.0.0",
    "pytest-django>=4.9.0",
    "ruff>=0.9.7",
    "yamllint>=1.35.1",
]

[tool.uv.sources]
framework = { git = "https://github.com/huma-engineering/huma-server-platform.git", rev = "v2.4.4" }
huma-plugins = { path = "libs/huma-server-plugins" }
mg-plugin = { path = "libs/hu-plugin-mg/python" }
hu-plugin-craft-server = { path = "libs/hu-craft-server/hu-plugin-craft-server" }
sdk = { path = "libs/huma-server-sdk" }


[tool.ruff]
extend = "libs/huma-server-sdk/pyproject.toml"
