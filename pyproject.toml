[project]
name = "huma_plugins"
version = "1.41.0"
description = "Add your description here"
authors = [
    { name = "Huma Engineering team", email = "<EMAIL>" }
]
readme = "README.md"
requires-python = "~=3.11"
dependencies = [
    "sdk",
    "google-cloud-aiplatform==1.69.0",
    "openai==0.27.8",
    "beautifulsoup4",
]
classifiers = [
    "Development Status :: 3 - Alpha",
    "Intended Audience :: Developers",
    "Topic :: Software Development :: Build Tools",
    "Programming Language :: Python :: 3",
    "Programming Language :: Python :: 3.11",
    "Private :: Do Not Upload"
]

[tool.uv]
dev-dependencies = [
    "coverage==7.3.4",
    "freezegun==1.4.0",
    "parameterized==0.9.0",
    "pre-commit==3.6.0",
    "pytest-cov==4.1.0",
    "pytest-django>=4.9.0",
    "ruff>=0.9.7",
    "yamllint==1.33.0",
]

[tool.uv.sources]
sdk = { path = "../huma-server-sdk" }

[build-system]
requires = ["hatchling"]
build-backend = "hatchling.build"

[tool.ruff]
extend = "../huma-server-sdk/pyproject.toml"

[tool.ruff.lint.flake8-tidy-imports.banned-api]
"mg".msg = "huma_plugins should not import mg to prevent circular dependencies"
